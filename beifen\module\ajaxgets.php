<?php

$type = $_GET['type'] ? $_GET['type'] : 'website';

if($type == 'arccate'){
	$key = $_GET['key'];
	$ids = explode(',', $key);
	$cid = array_pop($ids);
	$cid = intval($cid);
	
	$sql = "SELECT cate_id, cate_name FROM ".$DB->table('arccate')." WHERE root_id='$cid' ORDER BY cate_id ASC";
	$categories = $DB->fetch_all($sql);
	if (!empty($categories) && is_array($categories)) {
		$temp = array();
		foreach ($categories as $row) {
			$temp[$row['cate_id']]	= $row['cate_name'];
		}
		
		echo json_encode($temp);
	}
}else{
	$key = $_GET['key'];
	$ids = explode(',', $key);
	$id	= array_pop($ids);
	$id	= intval($id);
	
	$sql = "SELECT cate_id, cate_name FROM ".$DB->table('categories')." WHERE root_id='$id' AND cate_mod='$type' ORDER BY cate_id ASC";
	$categories = $DB->fetch_all($sql);
	if (!empty($categories) && is_array($categories)) {
		$data = array();
		foreach ($categories as $row) {
			$data[$row['cate_id']]	= $row['cate_name'];
		}
		echo json_encode($data);
	}
}
?>