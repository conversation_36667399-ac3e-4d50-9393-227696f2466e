<?php
if (!defined('IN_MEMBER')) exit('Access Denied');

require(CORE_PATH.'module/category.php');
require(CORE_PATH.'module/favorite.php');

$page_url = '?mod=favorite';
$tplfile = 'favorite.html';
$table = $DB->table('favorites');

$action = isset($_GET['act']) ? $_GET['act'] : 'list';
$smarty->assign('action', $action); 

if (!$smarty->isCached($tplfile)) {
	/** list */
	if ($action == 'list') {
		$page_name = '我的收藏';
		$smarty->assign('crumb', get_crumb().' &rsaquo; '.$page_name);
		
		$pagesize = 10;
		$curpage = intval($_GET['page']);
		if ($curpage > 1) {
			$start = ($curpage - 1) * $pagesize;
		} else {
			$start = 0;
			$curpage = 1;
		}
		
		$where = "f.user_id='".$myself['user_id']."'";
		$favorites = get_favorite_list($where, 'ftime', 'DESC', $start, $pagesize);
		$total = $DB->get_count($table.' f', $where);
		$showpage = showpage($page_url, $total, $curpage, $pagesize);
		
		$smarty->assign('page_name', $page_name);
		$smarty->assign('favorites', $favorites);
		$smarty->assign('total', $total);
		$smarty->assign('showpage', $showpage);
	}
	
	/** del */
	if ($action == 'del') {
		$fav_ids = (array) ($_POST['fid'] ? $_POST['fid'] : $_GET['fid']);
	
		$DB->delete($table, 'fav_id IN ('.dimplode($fav_ids).')');
	
		redirect($page_url);
	}
}

smarty_output($tplfile);
?>