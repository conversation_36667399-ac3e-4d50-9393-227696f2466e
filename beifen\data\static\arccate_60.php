<?php
//File name: arccate_60.php
//Creation time: 2017-01-07 11:18:28

if (!defined('IN_IWEBDIR')) exit('Access Denied');

$static_data = array(
	'cate_id' => '60',
	'root_id' => '57',
	'cate_name' => '礼品资讯',
	'cate_dir' => '',
	'cate_url' => '',
	'cate_isbest' => '0',
	'cate_keywords' => '礼品新闻,礼品资讯',
	'cate_description' => '礼品新闻资讯频道汇集礼品行业焦点、市场观察、促销技巧、营销管理、礼品行业解读、礼品顾问等礼品从业人员关注的新闻资讯。',
	'cate_arrparentid' => '0,57',
	'cate_arrchildid' => '60',
	'cate_childcount' => '0',
	'cate_postcount' => '0',
);
?>