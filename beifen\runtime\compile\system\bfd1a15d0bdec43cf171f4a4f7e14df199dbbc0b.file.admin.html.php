<?php /* Smarty version Smarty-3.1.18, created on 2023-07-07 14:40:26
         compiled from "/www/wwwroot/digg58.com/templet/system/admin.html" */ ?>
<?php /*%%SmartyHeaderCode:168340302063ca458e38b2c5-40342698%%*/if(!defined('SMARTY_DIR')) exit('no direct access allowed');
$_valid = $_smarty_tpl->decodeProperties(array (
  'file_dependency' => 
  array (
    'bfd1a15d0bdec43cf171f4a4f7e14df199dbbc0b' => 
    array (
      0 => '/www/wwwroot/digg58.com/templet/system/admin.html',
      1 => 1649575948,
      2 => 'file',
    ),
  ),
  'nocache_hash' => '168340302063ca458e38b2c5-40342698',
  'function' => 
  array (
  ),
  'version' => 'Smarty-3.1.18',
  'unifunc' => 'content_63ca458e4133a3_96806627',
  'variables' => 
  array (
    'page_name' => 0,
  ),
  'has_nocache_code' => false,
),false); /*/%%SmartyHeaderCode%%*/?>
<?php if ($_valid && !is_callable('content_63ca458e4133a3_96806627')) {function content_63ca458e4133a3_96806627($_smarty_tpl) {?><!DOCTYPE html>

<html>

<head>

<meta charset="utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title><?php echo $_smarty_tpl->tpl_vars['page_name']->value;?>
</title>

<link rel="stylesheet" href="../public/bootstrap/css/bootstrap.min.css">

<!-- <script type="text/javascript" src="../public/scripts/jquery.min.js"></script>
<script src="../public/bootstrap/js/bootstrap.min.js"></script>
<script type="text/javascript" src="../public/scripts/jquery.treeview.js"></script>

<script type="text/javascript">

$(document).ready(function(){  

    /*$("#main_frame").load(function(){

        $(this).height($(this).height() - $("#main_frame").offset().top - 10);    

    });

	*/

	$("#menu").treeview();

	

	var win_height = $(window).height() - $("#main_frame").offset().top - 5;

	$("#main_frame").height(win_height);

	$("#sidebar").css("height", win_height);

});

</script> -->

</head>


<body>
<style type="text/css" media="all">
    #astop a{color:#000;}
    nav#navigation {
    width: 96%;
    margin: 0 auto;
}
</style>

<div id="wrapper" class="toggled">
<nav id="navigation">
<ul id="astop"  class="nav navbar-nav navbar-right">
    <li><a href="/" target="_blank">预览网站</a></li>
    <li><a href="main.php" target="main">管理首页</a></li>
    <li><a href="cache.php" target="main">缓存管理</a></li>
    <li><a href="editpwd.php" target="main">修改密码</a></li>
    <li><a href="login.php?act=logout" onclick="return confirm('确认退出吗？');">注销登陆</a></li>
</ul>
</nav>
    <!--<div class="overlay"></div>-->

    	<nav class="navbar navbar-inverse navbar-fixed-top" id="sidebar-wrapper" role="navigation">


            	<ul class="nav sidebar-nav">

                    <li class="sidebar-brand">
                        <a href="./admin.php">
                           目录管理系统
                        </a>
                    </li>

                	<li class="dropdown">

                    	<a href="#" class="dropdown-toggle" data-toggle="dropdown"><i class="fa fa-fw fa-plus"></i> 系统设置 <span class="caret"></a>

                    	<ul class="dropdown-menu" role="menu">

                        	<li><a href="option.php?opt=basic" target="main">站点信息</a></li>

                            <li><a href="option.php?opt=misc" target="main">选项设置</a></li>

                            <li><a href="option.php?opt=user" target="main">会员设置</a></li>

							<li><a href="option.php?opt=payment" target="main">支付设置</a></li>

							<li><a href="option.php?opt=score" target="main">积分设置</a></li>

							<li><a href="option.php?opt=cache" target="main">缓存设置</a></li>

                            <li><a href="option.php?opt=link" target="main">链接设置</a></li>

                            <li><a href="option.php?opt=mail" target="main">邮件设置</a></li>

                        </ul>

                    </li>

                    <li class="dropdown">

                    	<a href="#" class="dropdown-toggle" data-toggle="dropdown"><i class="fa fa-fw fa-plus"></i> 站点管理 <span class="caret"></a>

                    	<ul  class="dropdown-menu" role="menu">

                        	<li><a href="category.php?mod=website" target="main">分类列表</a></li>

                            <li><a href="category.php?mod=website&act=reset" target="main">分类复位</a></li>

                            <li><a href="category.php?mod=website&act=merge" target="main">分类合并</a></li>

                            <li><a href="website.php" target="main">站点管理</a></li>

                            <li><a href="website.php?act=down" target="main">下载图片</a></li>

                            <li><a href="weblink.php" target="main">友链管理</a></li>

                        </ul>                    	

                    </li>

                    <li class="dropdown">

                    	<a href="#" class="dropdown-toggle" data-toggle="dropdown"><i class="fa fa-fw fa-plus"></i> 文章管理 <span class="caret"></a>

                    	<ul class="dropdown-menu" role="menu">

                            <li><a href="arccate.php" target="main">文章分类</a></li>

                            <li><a href="article.php" target="main">文章管理</a></li>

                        </ul>                    	

                    </li>

                    <li class="dropdown">

                    	<a href="#" class="dropdown-toggle" data-toggle="dropdown"><i class="fa fa-fw fa-plus"></i> 用户管理 <span class="caret"></a>

                    	<ul class="dropdown-menu" role="menu">

                        	<li><a href="user.php" target="main">注册会员</a></li>

							<li><a href="consume.php" target="main">消费记录</a></li>

                        </ul>                    	

                    </li>

                    <li class="dropdown">

                    	<a href="#" class="dropdown-toggle" data-toggle="dropdown"><i class="fa fa-fw fa-plus"></i> 辅助功能 <span class="caret"></a>

						<ul class="dropdown-menu" role="menu">

							<li><a href="adver.php" target="main">网站广告</a></li>

							<li><a href="link.php" target="main">友情链接</a></li>

                            <li><a href="feedback.php" target="main">意见反馈</a></li>

						</ul>                        

                    </li>

                    <li class="dropdown">

                    	<a href="#" class="dropdown-toggle" data-toggle="dropdown"><i class="fa fa-fw fa-plus"></i> 自定义管理 <span class="caret"></a>

						<ul class="dropdown-menu" role="menu">

							<li><a href="label.php" target="main">自定义标签</a></li>

							<li><a href="page.php" target="main">自定义页面</a></li>

						</ul>

                    </li>

                    <li class="dropdown">

                    	<a href="#" class="dropdown-toggle" data-toggle="dropdown"><i class="fa fa-fw fa-plus"></i> 数据库管理 <span class="caret"></a>

						<ul class="dropdown-menu" role="menu">

							<li><a href="database.php?act=backup" target="main">数据库备份</a></li>

							<li><a href="database.php?act=restore" target="main">数据库恢复</a></li>

							<li><a href="database.php?act=maintain" target="main">数据库维护</a></li>

							<li><a href="database.php?act=dbinfo" target="main">数据库信息</a></li>

						</ul>

                    </li>

                </ul>


        </nav>


        <div id="page-content-wrapper">
          <button type="button" class="hamburger is-closed animated fadeInLeft" data-toggle="offcanvas">
            <span class="hamb-top"></span>
            <span class="hamb-middle"></span>
            <span class="hamb-bottom"></span>
          </button>
            <div class="col-lg-10 col-lg-offset-1">
                <div class="row">
                    <iframe id="main_frame" name="main" frameborder="0" src="main.php" style="min-height:800px;width: 90%;"></iframe>
                </div>
            </div>
        </div>

</div>
<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.4.0/css/font-awesome.min.css">
<style>
/*@import "";*/

/*-------------------------------*/
/*           VARIABLES           */
/*-------------------------------*/
body {
  position: relative;
  overflow-x: hidden;
}
body,
html {
  height: 100%;
      background: #e9eaec;
}
.nav .open > a {
  background-color: transparent;
}
.nav .open > a:hover {
  background-color: transparent;
}
.nav .open > a:focus {
  background-color: transparent;
}
/*-------------------------------*/
/*           Wrappers            */
/*-------------------------------*/
#wrapper {
  -moz-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  -webkit-transition: all 0.5s ease;
  padding-left: 0;
  transition: all 0.5s ease;
}
#wrapper.toggled {
  padding-left: 220px;
}
#wrapper.toggled #sidebar-wrapper {
  width: 220px;
}
#wrapper.toggled #page-content-wrapper {
  margin-right: -220px;
  position: absolute;
}
#sidebar-wrapper {
  -moz-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  -webkit-transition: all 0.5s ease;
  background: #1a1a1a;
  height: 100%;
  left: 220px;
  margin-left: -220px;
  overflow-x: hidden;
  overflow-y: auto;
  transition: all 0.5s ease;
  width: 0;
  z-index: 1000;
}
#sidebar-wrapper::-webkit-scrollbar {
  display: none;
}
#page-content-wrapper {
  margin-top: 70px;
  width: 100%;
}
/*-------------------------------*/
/*     Sidebar nav styles        */
/*-------------------------------*/
.sidebar-nav {
  list-style: none;
  margin: 0;
  padding: 0;
  position: absolute;
  top: 0;
  width: 220px;
}
.sidebar-nav li {
  display: inline-block;
  line-height: 20px;
  position: relative;
  width: 100%;
}
.sidebar-nav li:before {
  background-color: #1c1c1c;
  content: '';
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  -webkit-transition: width 0.2s ease-in;
  transition: width 0.2s ease-in;
  width: 3px;
  z-index: -1;
}
.sidebar-nav li:first-child a {
  background-color: #1a1a1a;
  color: #ffffff;
}
.sidebar-nav li:nth-child(2):before {
  background-color: #3a3939;
}
.sidebar-nav li:nth-child(3):before {
  background-color: #3a3939;
}
.sidebar-nav li:nth-child(4):before {
  background-color: #3a3939;
}
.sidebar-nav li:nth-child(5):before {
  background-color: #3a3939;
}
.sidebar-nav li:nth-child(6):before {
  background-color: #3a3939;
}
.sidebar-nav li:nth-child(7):before {
  background-color: #3a3939;
}
.sidebar-nav li:nth-child(8):before {
  background-color: #3a3939;
}
.sidebar-nav li:nth-child(9):before {
  background-color: #3a3939;
}
.sidebar-nav li:hover:before {
  -webkit-transition: width 0.2s ease-in;
  transition: width 0.2s ease-in;
  width: 100%;
}
.sidebar-nav .dropdown-menu a {
    padding-left: 70px;
}
.sidebar-nav li a {
  color: #dddddd;
  display: block;
  padding: 10px 15px 10px 30px;
  text-decoration: none;
}
.sidebar-nav li.open:hover before {
  -webkit-transition: width 0.2s ease-in;
  transition: width 0.2s ease-in;
  width: 100%;
}
.sidebar-nav .dropdown-menu {
  background-color: #222222;
  border-radius: 0;
  border: none;
  box-shadow: none;
  margin: 0;
  padding: 0;
  position: relative;
  width: 100%;
}
.sidebar-nav li a:hover,
.sidebar-nav li a:active,
.sidebar-nav li a:focus,
.sidebar-nav li.open a:hover,
.sidebar-nav li.open a:active,
.sidebar-nav li.open a:focus {
  background-color: transparent;
  color: #ffffff;
  text-decoration: none;
}
.sidebar-nav > .sidebar-brand {
  font-size: 20px;
  height: 65px;
  line-height: 44px;
}
/*-------------------------------*/
/*       Hamburger-Cross         */
/*-------------------------------*/
.hamburger {
  background: transparent;
  border: none;
  display: block;
  height: 32px;
  margin-left: 15px;
  position: fixed;
  top: 20px;
  width: 32px;
  z-index: 999;
}
.hamburger:hover {
  outline: none;
}
.hamburger:focus {
  outline: none;
}
.hamburger:active {
  outline: none;
}
.hamburger.is-closed:before {
  -webkit-transform: translate3d(0, 0, 0);
  -webkit-transition: all 0.35s ease-in-out;
  color: #ffffff;
  content: '';
  display: block;
  font-size: 14px;
  line-height: 32px;
  opacity: 0;
  text-align: center;
  width: 100px;
}
.hamburger.is-closed:hover before {
  -webkit-transform: translate3d(-100px, 0, 0);
  -webkit-transition: all 0.35s ease-in-out;
  display: block;
  opacity: 1;
}
.hamburger.is-closed:hover .hamb-top {
  -webkit-transition: all 0.35s ease-in-out;
  top: 0;
}
.hamburger.is-closed:hover .hamb-bottom {
  -webkit-transition: all 0.35s ease-in-out;
  bottom: 0;
}
.hamburger.is-closed .hamb-top {
  -webkit-transition: all 0.35s ease-in-out;
  background-color: #000;
  top: 5px;
}
.hamburger.is-closed .hamb-middle {
  background-color: #000;
  margin-top: -2px;
  top: 50%;
}
.hamburger.is-closed .hamb-bottom {
  -webkit-transition: all 0.35s ease-in-out;
  background-color: #000;
  bottom: 5px;
}
.hamburger.is-closed .hamb-top,
.hamburger.is-closed .hamb-middle,
.hamburger.is-closed .hamb-bottom,
.hamburger.is-open .hamb-top,
.hamburger.is-open .hamb-middle,
.hamburger.is-open .hamb-bottom {
  height: 4px;
  left: 0;
  position: absolute;
  width: 100%;
}
.hamburger.is-open .hamb-top {
  -webkit-transform: rotate(45deg);
  -webkit-transition: -webkit-transform 0.2s cubic-bezier(0.73, 1, 0.28, 0.08);
  background-color: #ffffff;
  margin-top: -2px;
  top: 50%;
}
.hamburger.is-open .hamb-middle {
  background-color: #ffffff;
  display: none;
}
.hamburger.is-open .hamb-bottom {
  -webkit-transform: rotate(-45deg);
  -webkit-transition: -webkit-transform 0.2s cubic-bezier(0.73, 1, 0.28, 0.08);
  background-color: #ffffff;
  margin-top: -2px;
  top: 50%;
}
.hamburger.is-open:before {
  -webkit-transform: translate3d(0, 0, 0);
  -webkit-transition: all 0.35s ease-in-out;
  color: #ffffff;
  content: '';
  display: block;
  font-size: 14px;
  line-height: 32px;
  opacity: 0;
  text-align: center;
  width: 100px;
}
.hamburger.is-open:hover before {
  -webkit-transform: translate3d(-100px, 0, 0);
  -webkit-transition: all 0.35s ease-in-out;
  display: block;
  opacity: 1;
}
/*-------------------------------*/
/*          Dark Overlay         */
/*-------------------------------*/
.overlay {
  position: fixed;
  display: none;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 1;
}
/* SOME DEMO STYLES - NOT REQUIRED */
body h1,
body h2,
body h3,
body h4 {
  color: rgba(255, 255, 255, 0.9);
}
body p,
body blockquote {
  color: rgba(255, 255, 255, 0.7);
}
body a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: underline;
}
body a:hover {
  color: #ffffff;
}
</style>
<script src="https://www.jq22.com/jquery/jquery-1.10.2.js"></script>
<script src="https://www.jq22.com/jquery/bootstrap-3.3.4.js"></script>

<script type="text/javascript">
        $(document).ready(function () {
          var trigger = $('.hamburger'),
              overlay = $('.overlay'),
             isClosed = false;

            trigger.click(function () {
              hamburger_cross();      
            });

            function hamburger_cross() {

              if (isClosed == true) {          
                overlay.hide();
                trigger.removeClass('is-open');
                trigger.addClass('is-closed');
                isClosed = false;
              } else {   
                overlay.show();
                trigger.removeClass('is-closed');
                trigger.addClass('is-open');
                isClosed = true;
              }
          }
          
          $('[data-toggle="offcanvas"]').click(function () {
                $('#wrapper').toggleClass('toggled');
          });  
        });
    </script>
</body>

</html><?php }} ?>
