<?php /* Smarty version Smarty-3.1.18, created on 2023-07-07 14:44:34
         compiled from "/www/wwwroot/digg58.com/templet/default/footer.html" */ ?>
<?php /*%%SmartyHeaderCode:97635429663baa731c50301-01153301%%*/if(!defined('SMARTY_DIR')) exit('no direct access allowed');
$_valid = $_smarty_tpl->decodeProperties(array (
  'file_dependency' => 
  array (
    'b90c54f3ab8af2a6b6754da31b2a04d1cacbfa69' => 
    array (
      0 => '/www/wwwroot/digg58.com/templet/default/footer.html',
      1 => 1688711485,
      2 => 'file',
    ),
  ),
  'nocache_hash' => '97635429663baa731c50301-01153301',
  'function' => 
  array (
  ),
  'version' => 'Smarty-3.1.18',
  'unifunc' => 'content_63baa731c5e9c9_41915303',
  'variables' => 
  array (
    'site_copyright' => 0,
    'js_path' => 0,
  ),
  'has_nocache_code' => false,
),false); /*/%%SmartyHeaderCode%%*/?>
<?php if ($_valid && !is_callable('content_63baa731c5e9c9_41915303')) {function content_63baa731c5e9c9_41915303($_smarty_tpl) {?><div class="blank10"></div>
<footer class="wrapper">
	<div class="footer">
		<div class="footer-nav"><a href="/">首页</a> | <a href="/website/">网站目录</a> | <a href="/update/">最新收录</a> | <a href="/vip/">vip站点</a> | <a href="/archives/">数据归档</a> | <a href="/top/">排行榜</a> | <a href="/wait/">待审核</a> | <a href="/addurl/">网站提交</a> | <a target="_blank" href="/sitemap.xml">site地图</a> | <a target="_blank" onclick="window.open('/del.html');">删除</a></div>
		<div class="footer-copy"><?php echo $_smarty_tpl->tpl_vars['site_copyright']->value;?>
<br><?php echo insert_script_time(array(),$_smarty_tpl);?>
</div>
	</div>
</footer>
<?php echo get_adcode(10);?>

<script>
(function(){
    var bp = document.createElement('script');
    var curProtocol = window.location.protocol.split(':')[0];
    if (curProtocol === 'https') {
        bp.src = 'https://zz.bdstatic.com/linksubmit/push.js';        
    }
    else {
        bp.src = 'http://push.zhanzhang.baidu.com/push.js';
    }
    var s = document.getElementsByTagName("script")[0];
    s.parentNode.insertBefore(bp, s);
})();
function dian(selector,num){$(selector).each(function(){var maxwidth=num;   if($(this).text().length>maxwidth){$(this).text($(this).text().substring(0,maxwidth));$(this).html($(this).html()+"...");};});};dian('.newbox-list li a','9');dian('#tag li a','14');
</script>
<script>
var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?f25c54fc49dd1b9822b6e9ee0291c4f5";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script>
<script type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['js_path']->value;?>
test-clear.js" opacity="0.6"></script>

</body>
</html><?php }} ?>
