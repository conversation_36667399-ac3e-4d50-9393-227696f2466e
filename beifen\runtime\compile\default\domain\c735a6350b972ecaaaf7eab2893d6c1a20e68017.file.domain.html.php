<?php /* Smarty version Smarty-3.1.18, created on 2023-01-08 20:10:20
         compiled from "/www/wwwroot/digg58.com/templet/default/domain.html" */ ?>
<?php /*%%SmartyHeaderCode:183755890563bab2ace69df1-22637939%%*/if(!defined('SMARTY_DIR')) exit('no direct access allowed');
$_valid = $_smarty_tpl->decodeProperties(array (
  'file_dependency' => 
  array (
    'c735a6350b972ecaaaf7eab2893d6c1a20e68017' => 
    array (
      0 => '/www/wwwroot/digg58.com/templet/default/domain.html',
      1 => 1490520988,
      2 => 'file',
    ),
  ),
  'nocache_hash' => '183755890563bab2ace69df1-22637939',
  'function' => 
  array (
  ),
  'variables' => 
  array (
    'page_name' => 0,
    'curpage' => 0,
    'site_name' => 0,
    'css_path' => 0,
    'site_root' => 0,
    'option' => 0,
    'js_path' => 0,
    'crumb' => 0,
    'rssfeed' => 0,
    'cate_id' => 0,
    'w' => 0,
    'showpage' => 0,
    'new' => 0,
    'quick' => 0,
    'cate' => 0,
    'rel' => 0,
  ),
  'has_nocache_code' => false,
  'version' => 'Smarty-3.1.18',
  'unifunc' => 'content_63bab2acf2bda5_02612796',
),false); /*/%%SmartyHeaderCode%%*/?>
<?php if ($_valid && !is_callable('content_63bab2acf2bda5_02612796')) {function content_63bab2acf2bda5_02612796($_smarty_tpl) {?><!DOCTYPE HTML>

<html>

<head>

<title><?php echo $_smarty_tpl->tpl_vars['page_name']->value;?>
<?php if ($_smarty_tpl->tpl_vars['curpage']->value>1) {?>_第<?php echo $_smarty_tpl->tpl_vars['curpage']->value;?>
页<?php }?> - <?php echo $_smarty_tpl->tpl_vars['site_name']->value;?>
</title>

<meta charset="utf-8">

<meta name="Keywords" content="域名库" />

<meta name="Description" content="点我网站目录域名库集齐全网所有网站域名，方便有需求查询域名人员快捷得查到都域名得信息，以及更具域名查到域名站点信息等等。" />

<link href="<?php echo $_smarty_tpl->tpl_vars['css_path']->value;?>
default/style.css" rel="stylesheet" type="text/css" />

<script type="text/javascript">var $root = '<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
'; var $linktype = '<?php echo $_smarty_tpl->tpl_vars['option']->value['link_struct'];?>
';</script>

<script type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['js_path']->value;?>
jquery.min.js"></script>

<script type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['js_path']->value;?>
common.js"></script>

</head>



<body>

<?php echo $_smarty_tpl->getSubTemplate ("header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>


<div class="wrapper">

	<div class="crumb"><?php echo $_smarty_tpl->tpl_vars['crumb']->value;?>
&nbsp;<?php echo $_smarty_tpl->tpl_vars['rssfeed']->value;?>
</div>

	<div class="mainbox">

		<div class="mainbox-left">

            <div class="clearfix listbox allcate">

            	<h3 class="listbox-title"><?php echo $_smarty_tpl->tpl_vars['page_name']->value;?>
</h3>

            	<ul class="domain-list">

					<?php  $_smarty_tpl->tpl_vars['w'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['w']->_loop = false;
 $_from = get_websites_lunbo($_smarty_tpl->tpl_vars['cate_id']->value,130); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['w']->key => $_smarty_tpl->tpl_vars['w']->value) {
$_smarty_tpl->tpl_vars['w']->_loop = true;
?>

                	<li><a href="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_name'];?>
的域名" target="_blank"><?php echo $_smarty_tpl->tpl_vars['w']->value['web_url'];?>
</a><a class="domainSo" target="_blank" href="http://www.digg58.com/so/<?php echo $_smarty_tpl->tpl_vars['w']->value['web_url'];?>
.html" title="搜索<?php echo $_smarty_tpl->tpl_vars['w']->value['web_name'];?>
的域名"></a></li>

                	<?php }
if (!$_smarty_tpl->tpl_vars['w']->_loop) {
?>

                	<li>该目录下无任何内容！</li>

                	<?php } ?>

				</ul>
		
            	<div class="clearfix showpage"><?php echo $_smarty_tpl->tpl_vars['showpage']->value;?>
</div>
				
            </div>

		</div>

		<div class="mainbox-right">

			<div class="newbox">

            	<div class="newbox-title">最新收录</div>

                <ul class="newbox-list">

                	<?php  $_smarty_tpl->tpl_vars['new'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['new']->_loop = false;
 $_from = get_websites(0,13,false,false,'ctime'); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['new']->key => $_smarty_tpl->tpl_vars['new']->value) {
$_smarty_tpl->tpl_vars['new']->_loop = true;
?>

					<li><span><?php echo $_smarty_tpl->tpl_vars['new']->value['web_ctime'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['new']->value['web_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['new']->value['web_name'];?>
</a></li>

                   	<?php } ?>

                </ul>

            </div>
			
			<div class="blank10"></div>
			<div class="newbox">

            	<div class="newbox-title">快审收录</div>

                <ul class="newbox-list">

					<?php  $_smarty_tpl->tpl_vars['quick'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['quick']->_loop = false;
 $_from = get_websites(0,13,true); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['quick']->key => $_smarty_tpl->tpl_vars['quick']->value) {
$_smarty_tpl->tpl_vars['quick']->_loop = true;
?>

					<li><span><?php echo $_smarty_tpl->tpl_vars['quick']->value['web_ctime'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['quick']->value['web_name'];?>
</a></li>

                   	<?php } ?>

                </ul>

            </div>
			
			<div class="blank10"></div>
			<div class="newbox">

            	<div class="newbox-title">随机站点</div>

                <ul class="newbox-list">

                	<?php  $_smarty_tpl->tpl_vars['rel'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['rel']->_loop = false;
 $_from = get_websites_lunbo($_smarty_tpl->tpl_vars['cate']->value['cate_id'],14); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['rel']->key => $_smarty_tpl->tpl_vars['rel']->value) {
$_smarty_tpl->tpl_vars['rel']->_loop = true;
?>
						<li><span><?php echo $_smarty_tpl->tpl_vars['rel']->value['web_ctime'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['rel']->value['web_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['rel']->value['web_name'];?>
</a></li>
					<?php } ?>

                </ul>

            </div>
		</div>

	</div>

</div>

<?php echo $_smarty_tpl->getSubTemplate ("footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>


</body>

</html><?php }} ?>
