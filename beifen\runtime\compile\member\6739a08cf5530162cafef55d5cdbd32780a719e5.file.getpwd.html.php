<?php /* Smarty version Smarty-3.1.18, created on 2023-01-28 19:16:06
         compiled from "/www/wwwroot/digg58.com/templet/member/getpwd.html" */ ?>
<?php /*%%SmartyHeaderCode:92866203063d503f63c2730-38099998%%*/if(!defined('SMARTY_DIR')) exit('no direct access allowed');
$_valid = $_smarty_tpl->decodeProperties(array (
  'file_dependency' => 
  array (
    '6739a08cf5530162cafef55d5cdbd32780a719e5' => 
    array (
      0 => '/www/wwwroot/digg58.com/templet/member/getpwd.html',
      1 => 1418038320,
      2 => 'file',
    ),
  ),
  'nocache_hash' => '92866203063d503f63c2730-38099998',
  'function' => 
  array (
  ),
  'variables' => 
  array (
    'page_name' => 0,
    'site_name' => 0,
    'site_keywords' => 0,
    'site_description' => 0,
    'css_path' => 0,
    'site_root' => 0,
    'option' => 0,
    'js_path' => 0,
    'site_url' => 0,
    'site_title' => 0,
    'site_copyright' => 0,
  ),
  'has_nocache_code' => false,
  'version' => 'Smarty-3.1.18',
  'unifunc' => 'content_63d503f651e4a1_11493091',
),false); /*/%%SmartyHeaderCode%%*/?>
<?php if ($_valid && !is_callable('content_63d503f651e4a1_11493091')) {function content_63d503f651e4a1_11493091($_smarty_tpl) {?><!DOCTYPE html>

<html>

<head>

<title><?php echo $_smarty_tpl->tpl_vars['page_name']->value;?>
 - <?php echo $_smarty_tpl->tpl_vars['site_name']->value;?>
</title>

<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

<meta name="keywords" content="<?php echo $_smarty_tpl->tpl_vars['site_keywords']->value;?>
">

<meta name="description" content="<?php echo $_smarty_tpl->tpl_vars['site_description']->value;?>
">

<link href="<?php echo $_smarty_tpl->tpl_vars['css_path']->value;?>
member/login.css" rel="stylesheet" type="text/css" />

<script type="text/javascript">var $root = '<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
'; var $linktype = '<?php echo $_smarty_tpl->tpl_vars['option']->value['link_struct'];?>
';</script>

<script type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['js_path']->value;?>
jquery.min.js"></script>

<script type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['js_path']->value;?>
common.js"></script>

</head>



<body>

<div class="wrapper">

    <div class="header">

    	<a href="<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
" class="logo"><h1><?php echo $_smarty_tpl->tpl_vars['site_name']->value;?>
 - <?php echo $_smarty_tpl->tpl_vars['site_title']->value;?>
</h1></a>

        <div class="toplink"><a href="?mod=login">登录账户</a> <a href="?mod=register">我要注册</a></div>

    </div>

    <div class="combox">

        <div class="regform">

        	<h3><span>找回账户密码</span></h3>

        	<form name="myform" method="post" action="">

        	<ul>

            	<li><label><font color="#FF0000">*</font> 电子邮箱：</label><input type="text" name="email" size="50" maxlength="50" class="ipt" /><p>只有通过邮箱验证的账号才能找回密码</p></li>

                <li><label><font color="#FF0000">*</font> 验 证 码：</label><input type="text" name="code" size="10" maxlength="6" class="ipt" onFocus="refreshimg('mycode');" /><span id="mycode"></span><p>点击输入框即可显示验证码</p></li>

                <li><label>&nbsp;</label><input type="submit" value="发送密码重置邮件" class="btn" /><input type="hidden" name="action" value="send" /></li>

            </ul>

            </form>

            <div class="textinfo">

            	<strong>未收到密码找回邮件怎么办？</strong> <br />没有收到密码找回邮件，有可能因为延迟导致的，请稍候查收，也可以再次发送。 

			</div>

		</div>

    </div>

    <div class="footer">

    	<?php echo $_smarty_tpl->tpl_vars['site_copyright']->value;?>


    </div>

</div>

</body>

</html><?php }} ?>
