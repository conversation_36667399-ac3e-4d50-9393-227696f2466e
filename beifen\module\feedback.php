<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$page_name = '意见反馈';
$page_url = '?mod=feedback';
$tplfile = 'feedback.html';
$tpldir = 'other';

/** 缓存设置 */
$smarty->caching = false;
$smarty->compile_dir .= $tpldir;
$smarty->cache_dir .= $tpldir;
	
if (!$smarty->isCached($tplfile)) {
	$smarty->assign('page_name', $page_name);
	$smarty->assign('crumb', get_crumb('website').' &rsaquo; '.$page_name);
	$smarty->assign('rssfeed', get_rssfeed('website'));
	
	if ($_POST['action'] == 'send') {
		$fb_nick = trim($_POST['nick']);
		$fb_email = trim($_POST['email']);
		$fb_content = trim($_POST['content']);
		$check_code = md5(trim($_POST['code']));
		
		$fb_date = time();
		if (empty($fb_nick)) {
			alert('请输入昵称！');
		}
		
		if (empty($fb_email)) {
			alert('请输入电子邮件！');
		} else {
			if (!is_valid_email($fb_email)) {
				alert('请输入正确的电子邮件地址！');
			}
		}
		
		if (empty($fb_content) || strlen($fb_content) < 20) {
			alert('请输入意见内容，且长度不能小于20个字符！');
		}
		
		if (empty($check_code) || $check_code != $_SESSION['code']) {
			unset($_SESSION['code']);
			alert('您输入的验证码不正确，请重新输入！');	
		}
			
		$data = array(
			'fb_nick' => $fb_nick,
			'fb_email' => $fb_email,
			'fb_content' => $fb_content,
			'fb_date' => $fb_date,
		);
		
		$DB->insert($DB->table('feedbacks'), $data);
		unset($_SESSION['code']);
				
		alert('您的意见已经提交，谢谢您对我们的支持！', './');
	}
}
		
smarty_output($tplfile);
?>