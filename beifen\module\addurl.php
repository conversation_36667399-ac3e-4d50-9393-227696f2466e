<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');


$page_name = '网站提交';
$page_url = '?mod=addurl';
$tplfile = 'addurl.html';
$table = $DB->table('websites');

$action = isset($_GET['act']) ? $_GET['act'] : 'list';
$smarty->assign('action', $action); 

if (!$smarty->isCached($tplfile)) {
	
	/** add */
	if ($action == 'add') {
		//$page_name = '网站提交';
		
		#统计当日可提交的站点数量
		if ($options['submit_limit'] > 0) {
			$today_count = $DB->get_count($DB->table('websites'), "FROM_UNIXTIME(web_ctime, '%Y-%m-%d') = CURDATE()");
			$submit_limit = $options['submit_limit'] - $today_count;
			$smarty->assign('submit_limit', $submit_limit);
		}
		
		$smarty->assign('page_name', $page_name);
		$smarty->assign('crumb', get_crumb().' &rsaquo; '.$page_name);
		$smarty->assign('do', 'saveadd');
	}
	
	/** save */
	if (in_array($_POST['do'], array('saveadd', 'saveedit'))) {
		$cate_id = intval($_POST['cate_id']);
		$web_name = strip_tags($_POST['web_name']);
		$web_url = trim($_POST['web_url']);
		$web_tags = strip_tags($_POST['web_tags']);
		$web_intro = strip_tags($_POST['web_intro']);
		$web_ip = trim($_POST['web_ip']);
		$web_grank = intval($_POST['web_grank']);
		$web_brank = intval($_POST['web_brank']);
		$web_srank = intval($_POST['web_srank']);
		$web_arank = intval($_POST['web_arank']);
		$web_time = time();
		
		if ($cate_id <= 0) {
			alert('请选择网站所属分类！');
		} else {
			$cate = get_one_category($cate_id);
			if ($cate['cate_childcount'] > 0) {
				alert('指定的分类下有子分类，请选择子分类进行操作！');
			}
		}
	
		if (empty($web_name)) {
			alert('请输入网站名称！');
		} else {
			if (!censor_words($options['filter_words'], $web_name)) {
				alert('网站名称中含有非法关键词！');	
			}
		}
		
		if (empty($web_url)) {
			alert('请输入网站域名！');
		} else {
			if (!is_valid_domain($web_url)) {
				alert('请输入正确的网站域名！');
			}
		}
		
		if (!empty($web_tags)) {
			if (!censor_words($options['filter_words'], $web_tags)) {
				alert('TAG标签中含有非法关键词！');
			}
			
			$web_tags = str_replace('，', ',', $web_tags);
			$web_tags = str_replace(',,', ',', $web_tags);
			if (substr($web_tags, -1) == ',') {
				$web_tags = substr($web_tags, 0, strlen($web_tags) - 1);
			}
		}
			
		if (empty($web_intro)) {
			alert('请输入网站简介！');
		} else {
			if (!censor_words($options['filter_words'], $web_intro)) {
				alert('网站简介中含有非法关键词！');	
			}
		}
		
		$web_ip = sprintf("%u", ip2long($web_ip));
		//$web_ispay = $options['post_website_score'] > 0 ? 1 : 0;
		//$web_status = $options['post_website_score'] > 0 ? 3 : 2;
		$web_data = array(
			'cate_id' => $cate_id,
			'user_id' => $myself['user_id'],
			'web_name' => $web_name,
			'web_url' => $web_url,
			'web_tags' => $web_tags,
			'web_intro' => $web_intro,
			'web_ispay' => $web_ispay,
			'web_status' => 2,
			'web_ctime' => $web_time,
		);
		
		if ($_POST['do'] == 'saveadd') {
    		$query = $DB->query("SELECT web_id FROM $table WHERE web_url='$web_url'");
    		if ($DB->num_rows($query)) {
        		alert('您所提交的网站已存在！');
    		}
			
			$DB->insert($table, $web_data);
			$insert_id = $DB->insert_id();
			
			$stat_data = array(
				'web_id' => $insert_id,
				'web_ip' => $web_ip,
				'web_grank' => $web_grank,
				'web_brank' => $web_brank,
				'web_srank' => $web_srank,
				'web_arank' => $web_arank,
				'web_utime' => $web_time,
			);
			$DB->insert($DB->table('webdata'), $stat_data);
			
			
		
			alert('网站提交成功！','/wait/');
		}
	}
}


smarty_output($tplfile);
?>