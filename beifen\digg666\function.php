<?php
/**
  * 百度推送链接
  */
function baidu_push($urls,$config){
	$api = 'http://data.zz.baidu.com/urls?site=https://digg58.com&token=0ZMyG7NBSrTMb2Av';
	$ch = curl_init();
	$options =  array(
		CURLOPT_URL => $api,
		CURLOPT_POST => true,
		CURLOPT_RETURNTRANSFER => true,
		CURLOPT_POSTFIELDS => implode("\n", $urls),
		CURLOPT_HTTPHEADER => array('Content-Type: text/plain'),
	);
	curl_setopt_array($ch, $options);
	$result = curl_exec($ch);
	echo '百度推送：'.$result;
}


/** check login */

function check_admin_login($authcode) {

	global $DB;

	

	list($user_id, $user_pass) = !empty($authcode) ? explode("\t", authcode($authcode, 'DECODE', AUTH_KEY)) : array('', '');

	$user_id = intval($user_id);

	$user_pass = addslashes($user_pass);

	

	$newarr = array();

	if ($user_id && $user_pass) {

		$row = $DB->fetch_one("SELECT user_id, user_email, user_pass, nick_name, login_time, login_ip, login_count FROM ".$DB->table('users')." WHERE user_type='admin' AND user_id='$user_id'");	

		if ($row['user_pass'] == $user_pass) {

			$newarr = array(

				'user_id' => $row['user_id'],

				'user_email' => $row['user_email'],

				'nick_name' => $row['nick_name'],

				'login_time' => date('Y-m-d H:i:s', $row['login_time']),

				'login_ip' => long2ip($row['login_ip']),

				'login_count' => $row['login_count'],

			);

		}

	}

	

	return $newarr;

}



function smarty_output($template, $cache_id = NULL, $compile_id = NULL) {

	global $smarty, $action, $fileurl, $page_name;

	

	template_exists($template);

	

	$smarty->assign('action', $action);

	$smarty->assign('fileurl', $fileurl);

	$smarty->assign('page_name', $page_name);

	$smarty->display($template, $cache_id, $compile_id);

}



// 转换时间单位:秒 to XXX

function format_timespan($seconds = '') {

	if ($seconds == '') $seconds = 1;

	$str = '';

	$years = floor($seconds / 31536000);

	if ($years > 0) {

		$str .= $years.' 年, ';

	}

	$seconds -= $years * 31536000;

	$months = floor($seconds / 2628000);

	if ($years > 0 || $months > 0) {

		if ($months > 0) {

			$str .= $months.' 月, ';

		}

		$seconds -= $months * 2628000;

	}

	$weeks = floor($seconds / 604800);

	if ($years > 0 || $months > 0 || $weeks > 0) {

		if ($weeks > 0)	{

			$str .= $weeks.' 周, ';

		}

		$seconds -= $weeks * 604800;

	}

	$days = floor($seconds / 86400);

	if ($months > 0 || $weeks > 0 || $days > 0) {

		if ($days > 0) {

			$str .= $days.' 天, ';

		}

		$seconds -= $days * 86400;

	}

	$hours = floor($seconds / 3600);

	if ($days > 0 || $hours > 0) {

		if ($hours > 0) {

			$str .= $hours.' 小时, ';

		}

		$seconds -= $hours * 3600;

	}

	$minutes = floor($seconds / 60);

	if ($days > 0 || $hours > 0 || $minutes > 0) {

		if ($minutes > 0) {

			$str .= $minutes.' 分钟, ';

		}

		$seconds -= $minutes * 60;

	}

	if ($str == '') {

		$str .= $seconds.' 秒, ';

	}

	$str = substr(trim($str), 0, -1);

	return $str;

}

?>