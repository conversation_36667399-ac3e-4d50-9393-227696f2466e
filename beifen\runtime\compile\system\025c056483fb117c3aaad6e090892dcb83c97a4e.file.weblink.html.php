<?php /* Smarty version Smarty-3.1.18, created on 2023-11-06 21:17:12
         compiled from "/www/wwwroot/www.8t.lv/templet/system/weblink.html" */ ?>
<?php /*%%SmartyHeaderCode:4874456916548e7588172a6-88840907%%*/if(!defined('SMARTY_DIR')) exit('no direct access allowed');
$_valid = $_smarty_tpl->decodeProperties(array (
  'file_dependency' => 
  array (
    '025c056483fb117c3aaad6e090892dcb83c97a4e' => 
    array (
      0 => '/www/wwwroot/www.8t.lv/templet/system/weblink.html',
      1 => 1418038320,
      2 => 'file',
    ),
  ),
  'nocache_hash' => '4874456916548e7588172a6-88840907',
  'function' => 
  array (
  ),
  'variables' => 
  array (
    'action' => 0,
    'page_name' => 0,
    'fileurl' => 0,
    'keywords' => 0,
    'weblinks' => 0,
    'item' => 0,
    'showpage' => 0,
  ),
  'has_nocache_code' => false,
  'version' => 'Smarty-3.1.18',
  'unifunc' => 'content_6548e75886c092_71502837',
),false); /*/%%SmartyHeaderCode%%*/?>
<?php if ($_valid && !is_callable('content_6548e75886c092_71502837')) {function content_6548e75886c092_71502837($_smarty_tpl) {?><?php echo $_smarty_tpl->getSubTemplate ("header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>




	<?php if ($_smarty_tpl->tpl_vars['action']->value=='list') {?>

    <h3 class="title"><em><?php echo $_smarty_tpl->tpl_vars['page_name']->value;?>
</em></h3>

    <div class="listbox">

        <form name="mform" method="post" action="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">

        <div class="search">

        	<input name="keywords" type="text" id="keywords" class="ipt" size="30" value="<?php echo $_smarty_tpl->tpl_vars['keywords']->value;?>
" />

        	<input type="submit" class="btn" value="搜索" />

        </div>

        </form>

        

        <form name="mform" method="post" action="">

        <div class="toolbar">

			<select name="act" id="act" class="sel">

			<option value="del" style="color: #FF0000;">删除选定</option>

			</select>

			<input type="submit" class="btn" value="应用" onClick="if(IsCheck('link_id[]')==false){alert('请指定您要操作的链接ID！');return false;}else{return confirm('确认执行此操作吗？');}">

        </div>

        

    	<table width="100%" border="0" cellspacing="1" cellpadding="0">

    		<tr>

				<th><input type="checkbox" id="ChkAll" onClick="CheckAll(this.form)"></th>

				<th>ID</th>

                <th>所属分类</th>

                <th>交易方式</th>

				<th>链接名称</th>

				<th>链接地址</th>

				<th>发布时间</th>

                <th>操作选项</th>

    		</tr>

            <?php  $_smarty_tpl->tpl_vars['item'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['item']->_loop = false;
 $_from = $_smarty_tpl->tpl_vars['weblinks']->value; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['item']->key => $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->_loop = true;
?>

    		<tr>

				<td><input name="link_id[]" type="checkbox" value="<?php echo $_smarty_tpl->tpl_vars['item']->value['link_id'];?>
"></td>

				<td><?php echo $_smarty_tpl->tpl_vars['item']->value['link_id'];?>
</td>

				<td><?php echo $_smarty_tpl->tpl_vars['item']->value['cate_name'];?>
</td>

				<td><?php echo $_smarty_tpl->tpl_vars['item']->value['deal_type'];?>
</td>

				<td><?php echo $_smarty_tpl->tpl_vars['item']->value['link_name'];?>
</td>

				<td><?php echo $_smarty_tpl->tpl_vars['item']->value['web_url'];?>
</td>

                <td><?php echo $_smarty_tpl->tpl_vars['item']->value['link_time'];?>
</td>

                <td><?php echo $_smarty_tpl->tpl_vars['item']->value['link_opera'];?>
</td>

    		</tr>

			<?php }
if (!$_smarty_tpl->tpl_vars['item']->_loop) {
?>

			<tr><td colspan="8">无任何友情链接！</td></tr>

			<?php } ?>

		</table>

        </form>

        <div class="pagebox"><?php echo $_smarty_tpl->tpl_vars['showpage']->value;?>
</div>

    </div>

    <?php }?>

    

<?php echo $_smarty_tpl->getSubTemplate ("footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>
<?php }} ?>
