<?php

if (!defined('IN_IWEBDIR')) exit('Access Denied');



$page_name = '分类目录';

$page_url = '?mod=category';

$tplfile = 'category.html';

$tpldir = 'other';

$table = $DB->table('categories');



/** 缓存设置 */

$smarty->compile_dir .= $tpldir;

$smarty->cache_dir .= $tpldir;

$smarty->cache_lifetime = $options['cache_time_other'] * 3600;



if (!$smarty->isCached($tplfile)) {

	$categories = get_categories();



	$smarty->assign('page_name', $page_name);

	$smarty->assign('crumb', get_crumb('website').' &rsaquo; '.$page_name);

	$smarty->assign('rssfeed', get_rssfeed('website'));

	$smarty->assign('total', count($categories));

	$smarty->assign('categories', $categories);

	$smarty->assign('cate_keywords', $cate_keywords);

	$smarty->assign('cate_description', $cate_description);



}



smarty_output($tplfile);

?>