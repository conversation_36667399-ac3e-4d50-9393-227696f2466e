<?php
if (!defined('IN_MEMBER')) exit('Access Denied');

require(CORE_PATH.'include/upload.php');

$savepath = '../'.$options['upload_dir'].'/article/'.date('Ymd').'/';
$upload = new upload_file();
$upload->make_dir($savepath);
$upload->init($_FILES['imgFile'], $savepath);
if ($upload->error_code == 0) {
	$upload->save_file();
	echo json_encode(array('error' => 0, 'url' => $upload->attach['path']));
	exit;
} else {
	echo json_encode(array('error' => 1, 'message' => $upload->error()));
	exit;
}
?>