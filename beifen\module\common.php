<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

function smarty_output($template, $cache_id = NULL, $compile_id = NULL) {
	global $smarty, $options;
	
	template_exists($template);
	
	#common
	$stats = get_stats();
	$labels = get_labels();
	
	#user login
	$authcode = $_COOKIE['authcode'];
	$userinfo = check_user_login($authcode);
	
	$smarty->assign('js_path', $options['site_root'].'public/scripts/'); #js path
	$smarty->assign('img_path', $options['site_root'].'public/images/'); #img path
	$smarty->assign('css_path', $options['site_root'].'public/style/'); #img path
	$smarty->assign('theme_path', $options['site_root'].THEME_DIR.'/'); #theme path
	$smarty->assign('site_name', $options['site_name']);
	$smarty->assign('site_title', $options['site_title']);
	$smarty->assign('site_url', $options['site_url']);
	$smarty->assign('site_root', $options['site_root']);
	$smarty->assign('site_keywords', $options['site_keywords']);
	$smarty->assign('site_description', $options['site_description']);
	$smarty->assign('site_copyright', $options['site_copyright']);
	$smarty->assign('option', $options); #options
	$smarty->assign('stat', $stats); #stats
	$smarty->assign('label', stripslashes_deep($labels)); #labels
	$smarty->assign('search_words', get_format_tags($options['search_words']));
	$smarty->assign('script_time', get_scripttime()); #script time
	$smarty->assign('userinfo', $userinfo); #user login
	
	#parse template and output
	$content = $smarty->fetch($template, $cache_id, $compile_id);
	
	#strip blank
	$content = preg_replace('/\s(?=\s)/', '', $content);
	$content = preg_replace('/>\s</', '><', $content);
	
	if ($options['link_struct'] != 0) {
		$content = rewrite_output($content);
	}
	echo $content;
	
	#gzip
	$buffer = ob_get_contents();
	ob_end_clean();
	$options['is_enabled_gzip'] == 'yes' ? ob_start('ob_gzhandler') : ob_start();
	
	echo $buffer;
}

function get_scripttime() {
	global $DB, $options, $start_time;
	
	$mtime = explode(' ', microtime());
	$end_time = $mtime[1] + $mtime[0];
	$exec_time = number_format(($end_time - $start_time), 6);
	$gzip = $options['is_enabled_gzip'] == 'yes' ? 'Enabled' : 'Disabled';
	
	return 'Processed in '.$exec_time.' second(s), '.$DB->queries.' Queries, Gzip '.$gzip;
}

function insert_script_time() {
	return get_scripttime();
}

/** rss link */
function get_rssfeed($module = 'website', $cate_id = 0) {
	global $options;
	
	return '<a href="?mod=rssfeed&type='.$module.($cate_id > 0 ? '&cid='.$cate_id : '').'" target="_blank" class="RSS">RSS</a>';
}
	
/** site path */
function get_crumb($module = '', $cate_ids = '') {
	global $options;
	
	if (in_array($module, array('website', 'article', 'weblink'))) {
		$modstr = ' &rsaquo; ';
		switch ($module) {
			case 'website' :
				$modstr .= '<a href="?mod=website">网站目录</a>';
				break;
			case 'article' :
				$modstr .= '<a href="?mod=arccate">站长资讯</a>';
				break;
			case 'weblink' :
				$modstr .= '<a href="?mod=weblink">链接交换</a>';
				break;
		}
	}
	
	$strpath = '当前位置：<a href="'.$options['site_url'].'">'.$options['site_name'].'</a>'.$modstr.(!empty($cate_ids) ? get_category_path($cate_ids) : '');
	
	return $strpath;
}

/** article site path */
/** arc site path */
function get_article_sitepath($cate_ids = '', $pageinfo = '') {
	global $options;
	
	if(empty($pageinfo)){
		$strpath = '当前位置：<a href="'.$options['site_url'].'">'.$options['site_name'].'</a>'.(!empty($cate_ids) ? get_arccate_path($cate_ids) : '');
	}else{
		$strpath = '当前位置：<a href="'.$options['site_url'].'">'.$options['site_name'].'</a> &nbsp;&rsaquo;&nbsp; <a href="'.$pageinfo['url'].'">'.$pageinfo['name'].'</a>'.(!empty($cate_ids) ? get_arccate_path($cate_ids) : '');
	}
	
	
	return $strpath;
}
function get_article_crumb($module = '', $cate_ids = '') {
	global $options;
	
	/*if (in_array($module, array('website', 'article', 'weblink'))) {
		$modstr = ' &rsaquo; ';
		switch ($module) {
			case 'website' :
				$modstr .= '<a href="?mod=website">网站目录</a>';
				break;
			case 'article' :
				$modstr .= '<a href="?mod=arccate">站长资讯</a>';
				break;
			case 'weblink' :
				$modstr .= '<a href="?mod=weblink">链接交换</a>';
				break;
		}
	}*/
	
	$strpath = '当前位置：<a href="'.$options['site_url'].'">'.$options['site_name'].'</a>'.$modstr.(!empty($cate_ids) ? get_arccate_path($cate_ids) : '');
	
	return $strpath;
}


/** format tags */
function get_format_tags($str) {
	$arrstr = !empty($str) && strpos($str, ',') > 0 ? explode(',', $str) : (array) $str;
	$count = count($arrstr);
	
	$newarr = array();
	for ($i = 0; $i < $count; $i++) {
		$tag = trim($arrstr[$i]);
		$newarr[$i]['tag_name'] = $tag;
		$newarr[$i]['tag_link'] = get_search_url('tags', $tag);
	}
	
	return $newarr;
}
?>