<?php
class upload_file {
	public $attach = array();
	public $allow_type = array('bmp', 'gif', 'jpg', 'png');
	public $allow_size = 10485760;
	public $error_code = 0;
	
	public function init($filedata, $savepath) {
		$filedata['ext'] = $this->get_ext($filedata['name']);
		$filedata['path'] = $savepath.$this->make_filename().'.'.$filedata['ext'];
		$this->attach = $filedata;
		
		if (!is_array($this->attach) || empty($this->attach) || !$this->is_upload_file($this->attach['tmp_name'])) {
			$this->error_code = -101;
		} elseif (!$this->check_filetype($this->attach['ext'])) {
			$this->error_code = -102;
		} elseif (!$this->check_filesize($this->attach['size'])) {
			$this->error_code = -103;	
		} elseif (!$this->save_to_local($this->attach['tmp_name'], $this->attach['path'])) {
			$this->error_code = -104;
		} else {
			$this->error_code = 0;
		}
	}
	
	public function save_to_local($source, $target) {
		if (!$this->is_upload_file($source)) {
			$succeed = false;
		} elseif (function_exists('move_uploaded_file') && @move_uploaded_file($source, $target)) {
			$succeed = true;
		} elseif (@copy($source, $target)) {
			$succeed = true;
		} elseif (@is_readable($source) && (@$fp_s = fopen($source, 'rb')) && (@$fp_t = fopen($target, 'wb'))) {
			while (!feof($fp_s)) {
				$s_data = @fread($fp_s, @filesize($source));
				@fwrite($fp_t, $s_data);
			}
			fclose($fp_s);
			fclose($fp_t);
			$succeed = true;
		}

		if ($succeed) {
			@chmod($target, 0644);
			@unlink($source);
		}

		return $succeed;
	}
	
	private function is_upload_file($source) {
		return $source && ($source != 'none') && (is_uploaded_file($source) || is_uploaded_file(str_replace('\\\\', '\\', $source)));
	}
	
	private function check_filetype($type) {
		return $type && in_array($type, $this->allow_type) ? true : false;
	}
	
	private function check_filesize($size) {
		return $size <= 0 || $size > $this->allow_size ? false : true;
	}
	
	private function get_ext($filename) {
		return addslashes(strtolower(substr(strrchr($filename, '.'), 1, 10)));
	}
	
	private function make_filename() {
		return md5(uniqid(rand(), true));
	}
	
	public function make_dir($dir, $index = true) {
		$res = true;
		if (!is_dir($dir)) {
			$res = @mkdir($dir, 0777, true);
			$index && @touch($dir.'/index.html');
		}
		return $res;
	}
	
	public function error() {
		$errstr = '';
		switch ($this->error_code) {
			case -101 :
				$errstr = '上传文件不存在或不合法！';
				break;
			case -102 :
				$errstr = '不允许上传的文件类型！';
				break;
			case -103 :
				$errstr = '上传文件大小超出限制！';
				break;
			case -104 :
				$errstr = '无法写入文件或写入失败！';
				break;
		}
		return $errstr;
	}
}
?>