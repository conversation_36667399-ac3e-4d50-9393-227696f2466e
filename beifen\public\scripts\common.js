

$(document).ready(function(){
	//搜索
    $("#selopt").hover(
        function(){
            $("#options").slideDown();
            $("#options li a").click(function(){
                $("#cursel").text($(this).text());
                $("#type").attr("value", $(this).attr("name"));
                $("#options").hide();
            });
        },
        
        function(){$("#options").hide();}
    )
	
	
	//返回顶部
	$('.li7').click(function(){
		$('body,html').animate({scrollTop:0},500);
	})
	

	
	$('.jump-url').click(function(){
	    var jumpUrl=$('.jump-url').text();
	    if (confirm("确定访问"+jumpUrl+"吗？")){
	        window.open(jumpUrl);  
	    };
		
	})
	
	$('.jump-url1').click(function(){
	    var jumpUrl=$('.jump-url').text();
	    if (confirm("确定访问"+jumpUrl+"吗？")){
	        window.open(jumpUrl);  
	    };
		
	})
})


//搜索伪静态
function rewrite_search(){
	var $type = $("#type").val();
	var $query = $.trim($("#query").val());
	if ($type == null) {$type = "tags"}
	if ($query == "") {
		$("#query").focus();
		return false;
	} else {
		if ($linktype == 1) {
			window.location.href = $root + "search-" + $type + "-" + encodeURI($query) + ".html";
		} else if ($linktype == 2) {
			window.location.href = $root + "search/" + $type + "-" + encodeURI($query) + ".html";
		} else if ($linktype == 3) {
			window.location.href = $root + "search/" + $type + "-" + encodeURI($query);
		} else {
			this.form.submit();
		}
	}
	return false;
}

//IP伪静态
function rewrite_ipinfo(){
	var $query = $.trim($("#ip").val());
	if ($query == "") {
		$("#ip").focus();
		return false;
	} else {
		if ($linktype == 1) {
			window.location.href = $root + "ipinfo-" + $query + ".html";
		} else if ($linktype == 2) {
			window.location.href = $root + "ipinfo/" + $query + ".html";
		} else if ($linktype == 3) {
			window.location.href = $root + "ipinfo/" + $query;
		} else {
			this.form.submit();
		}
	}
	return false;
}

//Whois伪静态
function rewrite_whois(){
	var $query = $.trim($("#url").val());
	if ($query == "") {
		$("#url").focus();
		return false;
	} else {
		if ($linktype == 1) {
			window.location.href = $root + "whois-" + $query + ".html";
		} else if ($linktype == 2) {
			window.location.href = $root + "whois/" + $query + ".html";
		} else if ($linktype == 3) {
			window.location.href = $root + "whois/" + $query;
		} else {
			this.form.submit();
		}
	}
	return false;
}

//自动去除http
function strip_http() {
	var $url = $('#web_url').val();
    if ($url.indexOf('http://') >= 0) {
		$url = $url.replace('http://', '');
	}
	if ($url.indexOf('https://') >= 0) {
		$url = $url.replace('https://', '');
	}
    if ($url.indexOf('/') >= 0) {
		var $domain = $url.split('/');
		$url = $domain[0];
	}
	$url = $url.replace(' ', '');
	$('#web_url').val($url);
}

//验证url
function checkurl($url){
	if ($url == '') {
		$("#msg").html('请输入网站域名！');
		return false;
	}
	
	$(document).ready(function(){
		$("#msg").html('<img src="' + $root + 'public/images/loading.gif" align="absmiddle"> 正在验证，请稍候...'); 
		$.ajax({type: "GET", url: $root + '?mod=ajaxget&type=check', data: 'url=' + $url, cache: false, success: function($data){
				$("#msg").html($data)
			}
		});
	});
return true;
};

//获取META
function getmeta() {
	var $url = $("#web_url").val();
	if ($url == '') {
		alert('请输入网站域名！');
		$("#web_url").focus();
		return false;
	}
	$(document).ready(function(){$("#meta_btn").val('抓取中 · · ·'); $.ajax({type: "GET", url: $root + '?mod=ajaxget&type=crawl', data: 'url=' + $url, datatype: "script", cache: false, success: function($data){$("body").append($data); $("#meta_btn").val('重新获取');}});});	
}

//获取IP, PageRank, Sogou PageRank, Alexa
function getdata() {
	var $url = $("#web_url").val();
	if ($url == '') {
		alert('请输入网站域名！');
		$("#web_url").focus();
		return false;
	}
	$(document).ready(function(){$("#data_btn").val('抓取中 · · ·'); $.ajax({type: "GET", url: $root + '?mod=ajaxget&type=data', data: 'url=' + $url, datatype: "script", cache: false, success: function($data){$("body").append($data)}}); $("#data_btn").val('重新获取');});
}

//添加收藏
function addfav($wid) {
	$(document).ready(function(){$.ajax({type: "GET", url: $root + "?mod=getdata&type=addfav", data: "wid=" + $wid, cache: false, success: function($data){$("body").append($data)}});});
};

//点出统计
function clickout($wid) {
	$(document).ready(function(){$.ajax({type: "GET", url: $root + "?mod=getdata&type=outstat", data: "wid=" + $wid, cache: false, success: function($data){}});});
};

//错误报告
function report($obj, $wid) {
	$(document).ready(function(){if (confirm("确认此网址错误吗？")){ $("#" + $obj).html("正在提交，请稍候..."); $.ajax({type: "GET", url: $root + "?mod=getdata&type=error", data: "wid=" + $wid, cache: false, success: function($data){$("#" + $obj).html($data);}})};});
};

//验证码
function refreshimg($obj) {
	var $randnum = Math.random();
	$("#" + $obj).html('<img src="' + $root + 'source/include/captcha.php?s=' + $randnum + '" align="absmiddle" alt="看不清楚?换一张" onclick="this.src+='+ $randnum +'" style="cursor: pointer;">');
};

//更新日期
function uptime($wid) {
	$(document).ready(function(){$.ajax({type: "GET", url: $root + "?mod=getdata&type=uptime", data: "wid=" + $wid, cache: false, success: function($data){}});});
};
