<?php /* Smarty version Smarty-3.1.18, created on 2023-11-08 04:21:33
         compiled from "/www/wwwroot/www.8t.lv/templet/default/alert.html" */ ?>
<?php /*%%SmartyHeaderCode:962623128654a9c4df421c5-99059598%%*/if(!defined('SMARTY_DIR')) exit('no direct access allowed');
$_valid = $_smarty_tpl->decodeProperties(array (
  'file_dependency' => 
  array (
    '6273a994d7fbcdf43e96f4b7a21c70a3d7966a08' => 
    array (
      0 => '/www/wwwroot/www.8t.lv/templet/default/alert.html',
      1 => 1699369793,
      2 => 'file',
    ),
  ),
  'nocache_hash' => '962623128654a9c4df421c5-99059598',
  'function' => 
  array (
  ),
  'variables' => 
  array (
    'msg' => 0,
    'url' => 0,
  ),
  'has_nocache_code' => false,
  'version' => 'Smarty-3.1.18',
  'unifunc' => 'content_654a9c4e03d508_24348406',
),false); /*/%%SmartyHeaderCode%%*/?>
<?php if ($_valid && !is_callable('content_654a9c4e03d508_24348406')) {function content_654a9c4e03d508_24348406($_smarty_tpl) {?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

<head>

<title>系统提示</title>

<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

<style type="text/css">

* {margin: 0px; padding: 0px;}

body {background: #E9EAEC; font: 12px/23px Verdana, Arial, Helvetica, sans-serif;}

a {color: #5c97de; text-decoration: none;}

a:hover {color: #f30; text-decoration: underline;}

.msgbox {
	    
    margin: 180px auto 0px auto;
    width: 350px;
    border-radius: 10px;
}

.title {background: #5c97de; color: #fff; font: bold 12px normal; padding: 7px;}

.content {background: #3ABA6F; color: #fff; padding: 15px 10px;
	    border-radius: 10px 10px 0px 0px;
	    text-align: center;
	    font-size: 16px;
    font-weight: bold;
}

.link {
	    border-radius:  0px 0px 10px 10px;
	background: #fff;  color: #005F93; line-height: 30px; padding: 3px; text-align: center;}

h1{line-height: 40px;    padding-bottom: 10px;}
</style>

</head>



<body>

<div class="msgbox">
	
    <div class="content">
    	<h1>系统提示!</h1>
    	<?php echo $_smarty_tpl->tpl_vars['msg']->value;?>
</div>

    <div class="link"><strong>系统 <span id="seconds" style="color: #E74C3C;">2</span> 秒后将自动跳转</strong></div>

</div>

<script type="text/javascript">

var i = 2;

var retime = setInterval(function() {

	i = i - 1;

	if (i < 0){

		window.location.href= '<?php echo $_smarty_tpl->tpl_vars['url']->value;?>
';

		window.clearInterval(retime);

		return;

	}

	document.getElementById("seconds").innerHTML = i;

}, 1000);

</script>

</body>

</html><?php }} ?>
