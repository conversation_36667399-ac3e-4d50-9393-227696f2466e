<?php
/** config cache */
function options_cache() {
	global $DB;
	
	$sql = "SELECT * FROM ".$DB->table('options');
	$data = $DB->fetch_all($sql);
	$code = "\$static_data = array(\r\n";
	if (!empty($data) && is_array($data)) {
		foreach ($data as $row) {
			$code .= "\t'".addslashes($row['option_name'])."' => '".addslashes($row['option_value'])."',\r\n";
		}
	}
	$code .= ");";
	
	write_cache('options', $code);
}

/** adver cache */
function advers_cache() {
	global $DB;
	
	$sql = "SELECT * FROM ".$DB->table('advers')." ORDER BY adver_id DESC";
	$data = $DB->fetch_all($sql);
	$code = "\$static_data = array(\r\n";
	if (!empty($data) && is_array($data)) {
		foreach ($data as $row) {
			$code .= "\t'".$row['adver_id']."' => array(\r\n\t\t'adver_type' => '".$row['adver_type']."',\r\n\t\t'adver_name' => '".addslashes($row['adver_name'])."',\r\n\t\t'adver_url' => '".addslashes($row['adver_url'])."',\r\n\t\t'adver_code' => '".addslashes($row['adver_code'])."',\r\n\t\t'adver_etips' => '".$row['adver_etips']."',\r\n\t\t'adver_days' => '".$row['adver_days']."',\r\n\t\t'adver_date' => '".$row['adver_date']."'\r\n\t),\r\n";
		}
	}
	$code .= ");";
	
	write_cache('advers', $code);
}

/** link cache */
function links_cache() {
	global $DB;
	
	$sql = "SELECT * FROM ".$DB->table('links')." WHERE link_hide=1 ORDER BY link_order ASC";
	$data = $DB->fetch_all($sql);
	$code = "\$static_data = array(\r\n";
	if (!empty($data) && is_array($data)) {
		foreach ($data as $row) {
			$code .= "\t'".$row['link_id']."' => array(\r\n\t\t'link_name' => '".addslashes($row['link_name'])."',\r\n\t\t'link_url' => '".addslashes($row['link_url'])."',\r\n\t\t'logo_url' => '".addslashes($row['link_logo'])."',\r\n\t),\r\n";
		}
	}
	$code .= ");";
	
	write_cache('links', $code);
}

/** category cache */
function categories_cache() {
	global $DB;
	
	$sql = "SELECT * FROM ".$DB->table('categories')." ORDER BY cate_order ASC, cate_id ASC";
	$data = $DB->fetch_all($sql);
	$code .= "\$static_data = array(\r\n";
	if (!empty($data) && is_array($data)) {
		foreach ($data as $row) {
			$code .= "\t'".$row['cate_id']."' => array(\r\n\t\t'cate_id' => '".$row['cate_id']."',\r\n\t\t'root_id' => '".$row['root_id']."',\r\n\t\t'cate_mod' => '".$row['cate_mod']."',\r\n\t\t'cate_name' => '".addslashes($row['cate_name'])."',\r\n\t\t'cate_dir' => '".addslashes($row['cate_dir'])."',\r\n\t\t'cate_url' => '".addslashes($row['cate_url'])."',\r\n\t\t'cate_isbest' => '".$row['cate_isbest']."',\r\n\t\t'cate_keywords' => '".addslashes($row['cate_keywords'])."',\r\n\t\t'cate_description' => '".addslashes($row['cate_description'])."',\r\n\t\t'cate_arrparentid' => '".$row['cate_arrparentid']."',\r\n\t\t'cate_arrchildid' => '".$row['cate_arrchildid']."',\r\n\t\t'cate_childcount' => '".$row['cate_childcount']."',\r\n\t\t'cate_postcount' => '".$row['cate_postcount']."'\r\n\t),\r\n";
			
			$code_1 = "\$static_data = array(\r\n";
			$code_1 .= "\t'cate_id' => '".$row['cate_id']."',\r\n\t'root_id' => '".$row['root_id']."',\r\n\t'cate_mod' => '".$row['cate_mod']."',\r\n\t'cate_name' => '".addslashes($row['cate_name'])."',\r\n\t'cate_dir' => '".addslashes($row['cate_dir'])."',\r\n\t'cate_url' => '".addslashes($row['cate_url'])."',\r\n\t'cate_isbest' => '".$row['cate_isbest']."',\r\n\t'cate_keywords' => '".addslashes($row['cate_keywords'])."',\r\n\t'cate_description' => '".addslashes($row['cate_description'])."',\r\n\t'cate_arrparentid' => '".$row['cate_arrparentid']."',\r\n\t'cate_arrchildid' => '".$row['cate_arrchildid']."',\r\n\t'cate_childcount' => '".$row['cate_childcount']."',\r\n\t'cate_postcount' => '".$row['cate_postcount']."',\r\n";
			$code_1 .= ");";
			
			write_cache('category_'.$row['cate_id'], $code_1);
		}
	}
	$code .= ");";
	
	write_cache('categories', $code);
}

/** arccate cache */
function arccate_cache() {
	global $DB;
	
	$sql = "SELECT * FROM ".$DB->table('arccate')." ORDER BY cate_sort ASC, cate_id ASC";
	$categories = $DB->fetch_all($sql);
	$contents .= "\$static_data = array(\r\n";
	foreach ($categories as $cate) {
		$contents .= "\t'".$cate['cate_id']."' => array(\r\n\t\t'cate_id' => '".$cate['cate_id']."',\r\n\t\t'root_id' => '".$cate['root_id']."',\r\n\t\t'cate_name' => '".$cate['cate_name']."',\r\n\t\t'cate_dir' => '".$cate['cate_dir']."',\r\n\t\t'cate_url' => '".$cate['cate_url']."',\r\n\t\t'cate_isbest' => '".$cate['cate_isbest']."',\r\n\t\t'cate_keywords' => '".$cate['cate_keywords']."',\r\n\t\t'cate_description' => '".$cate['cate_description']."',\r\n\t\t'cate_arrparentid' => '".$cate['cate_arrparentid']."',\r\n\t\t'cate_arrchildid' => '".$cate['cate_arrchildid']."',\r\n\t\t'cate_childcount' => '".$cate['cate_childcount']."',\r\n\t\t'cate_postcount' => '".$cate['cate_postcount']."'\r\n\t),\r\n";
		
		$contents_1 = "\$static_data = array(\r\n";
		$contents_1 .= "\t'cate_id' => '".$cate['cate_id']."',\r\n\t'root_id' => '".$cate['root_id']."',\r\n\t'cate_name' => '".$cate['cate_name']."',\r\n\t'cate_dir' => '".$cate['cate_dir']."',\r\n\t'cate_url' => '".$cate['cate_url']."',\r\n\t'cate_isbest' => '".$cate['cate_isbest']."',\r\n\t'cate_keywords' => '".$cate['cate_keywords']."',\r\n\t'cate_description' => '".$cate['cate_description']."',\r\n\t'cate_arrparentid' => '".$cate['cate_arrparentid']."',\r\n\t'cate_arrchildid' => '".$cate['cate_arrchildid']."',\r\n\t'cate_childcount' => '".$cate['cate_childcount']."',\r\n\t'cate_postcount' => '".$cate['cate_postcount']."',\r\n";
		$contents_1 .= ");";
		
		write_cache('arccate_'.$cate['cate_id'], $contents_1);
	}
	$contents .= ");";
	
	write_cache('arccate', $contents);
}

/** label cache */
function labels_cache() {
	global $DB;
	
	$sql = "SELECT * FROM ".$DB->table('labels');
	$data = $DB->fetch_all($sql);
	$code = "\$static_data = array(\r\n";
	if (!empty($data) && is_array($data)) {
		foreach ($data as $row) {
			$code .= "\t'".addslashes($row['label_name'])."' => '".addslashes($row['label_content'])."',\r\n";
		}
	}
	$code .= ");";
	
	write_cache('labels', $code);
}

/** archives cache */
function archives_cache() {
	global $DB;
	
	$sql = "SELECT web_ctime FROM ".$DB->table('websites')." WHERE web_status=3 ORDER BY web_ctime DESC";
	$query = $DB->query($sql);
	$data = array();
	while ($row = $DB->fetch_array($query)) {
		$data[] = date('Y-m', $row['web_ctime']);
	}
	$DB->free_result($query);
		
	$count = array_count_values($data);
	
	foreach ($count as $key => $val) {
		list($year, $month) = explode('-', $key);
		$archives[$year][$month] = $val;
	}
		
	$code = "\$static_data = array(\r\n";
	if (!empty($archives) && is_array($archives)) {
		foreach ($archives as $year => $arr) {
			$code .= "\t'".$year."' => array(";
			ksort($arr);
			foreach ($arr as $month => $num) {
				$code .= "\r\n\t\t'".$month."' => '".$num."',";
			}
			$code .= "\r\n\t),\r\n";
		}
	}
	$code .= ");";
	
	write_cache('archives', $code);
}

/** stats cache */
function stats_cache() {
	global $DB;
	
	$category = $DB->get_count($DB->table('categories'));
	$website = $DB->get_count($DB->table('websites'));
	$article = $DB->get_count($DB->table('articles'));
	$audit = $DB->get_count($DB->table('websites'), array('web_status' => 2));
	$user = $DB->get_count($DB->table('users'), "user_type != 'admin'");
	$adver = $DB->get_count($DB->table('advers'));
	$link = $DB->get_count($DB->table('links'));
	$feedback = $DB->get_count($DB->table('feedbacks'));
	$label = $DB->get_count($DB->table('labels'));
	$page = $DB->get_count($DB->table('pages'));
	
	$code = "\$static_data = array(\r\n";
	$code .= "\t'category' => '".$category."',\r\n";
	$code .= "\t'website' => '".$website."',\r\n";
	$code .= "\t'article' => '".$article."',\r\n";
	$code .= "\t'audit' => '".$audit."',\r\n";
	$code .= "\t'user' => '".$user."',\r\n";
	$code .= "\t'adver' => '".$adver."',\r\n";
	$code .= "\t'link' => '".$link."',\r\n";
	$code .= "\t'feedback' => '".$feedback."',\r\n";
	$code .= "\t'label' => '".$label."',\r\n";
	$code .= "\t'page' => '".$page."',\r\n";
	$code .= ");";
	
	write_cache('stats', $code);
}
?>