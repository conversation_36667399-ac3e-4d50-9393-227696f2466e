<?php
//File name: category_2.php
//Creation time: 2023-11-07 01:09:36

if (!defined('IN_IWEBDIR')) exit('Access Denied');

$static_data = array(
	'cate_id' => '2',
	'root_id' => '0',
	'cate_mod' => 'website',
	'cate_name' => '生活资讯',
	'cate_dir' => 'shenghuo',
	'cate_url' => '',
	'cate_isbest' => '0',
	'cate_keywords' => '生活资讯,生活资讯网,生活资讯网站',
	'cate_description' => '生活资讯网站大全 - 我们为您搜集生活资讯频道、生活资讯节目、生活资讯杂志、时尚生活资讯、生活资讯网站等等，找网站就来点我网站目录。',
	'cate_arrparentid' => '0',
	'cate_arrchildid' => '2,24,25,26,27,28,29,30,31,32,33,34,35,36,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,105,106,107,109,111,112,113,114,115,116,117,159,163,167',
	'cate_childcount' => '45',
	'cate_postcount' => '4630',
);
?>