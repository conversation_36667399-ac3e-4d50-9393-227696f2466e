<?php
if (!defined('IN_MEMBER')) exit('Access Denied');

require(CORE_PATH.'module/consume.php');

$page_name = '财务管理';
$page_url = '?mod=consume';
$tplfile = 'consume.html';
$table = $DB->table('consumes');

$action = isset($_GET['act']) ? $_GET['act'] : 'list';
$smarty->assign('action', $action); 

if (!$smarty->isCached($tplfile)) {
	/** list */
	if ($action == 'list') {
		$page_name = '财务管理';
		$smarty->assign('crumb', get_crumb().' &rsaquo; '.$page_name);
		
		$pagesize = 20;
		$curpage = intval($_GET['page']);
		if ($curpage > 1) {
			$start = ($curpage - 1) * $pagesize;
		} else {
			$start = 0;
			$curpage = 1;
		}
		
		$where = "c.user_id='".$myself['user_id']."'";
		$consumes = get_consume_list($where, 'time', 'DESC', $start, $pagesize);
		$total = $DB->get_count($table.' c', $where);
		$showpage = showpage($page_url, $total, $curpage, $pagesize);
		
		$smarty->assign('page_name', $page_name);
		$smarty->assign('consumes', $consumes);
		$smarty->assign('total', $total);
		$smarty->assign('showpage', $showpage);
	}
	
	/** pay */
	if ($action == 'pay') {
		$page_name = '积分充值';
		
		$order_id = strtoupper(uniqid());
		$smarty->assign('page_name', $page_name);
		$smarty->assign('order_id', $order_id);
	}
	
	/** jump */
	if ($action == 'jump') {
		$cons_id = intval($_GET['cid']);
		$row = $DB->fetch_one("SELECT order_id, cons_name, cons_money FROM $table WHERE cons_id='$cons_id' LIMIT 1");
		if (!$row) {
			alert('该交易不存在！', '?mod=consume&act=pay');	
		}
		
		$code = '<script type="text/javascript" src="'.$options['site_root'].'public/scripts/jquery.min.js"></script>';
		$code .= '<script type="text/javascript">';
		$code .= '$(document).ready(function(){$.ajax({type: "POST", url: \''.$options['site_root'].'member/?mod=payment\', data: {\'orderid\' : \''.$row['order_id'].'\', \'subject\' : \''.$row['cons_name'].'\', \'price\' : \''.$row['cons_money'].'\'}, datatype: \'html\', cache: false, success: function($data){document.write($data);}})});';
		$code .= '</script>';
		echo($code);
		exit();
	}
}

smarty_output($tplfile);
?>