<?php /* Smarty version Smarty-3.1.18, created on 2023-11-06 21:55:04
         compiled from "/www/wwwroot/www.8t.lv/templet/default/index.html" */ ?>
<?php /*%%SmartyHeaderCode:9046613906548e4cadeb597-87255533%%*/if(!defined('SMARTY_DIR')) exit('no direct access allowed');
$_valid = $_smarty_tpl->decodeProperties(array (
  'file_dependency' => 
  array (
    'e97e9be4daa1ecf9b4469f46009258db67edb767' => 
    array (
      0 => '/www/wwwroot/www.8t.lv/templet/default/index.html',
      1 => 1699278504,
      2 => 'file',
    ),
  ),
  'nocache_hash' => '9046613906548e4cadeb597-87255533',
  'function' => 
  array (
  ),
  'version' => 'Smarty-3.1.18',
  'unifunc' => 'content_6548e4caec2266_94836141',
  'variables' => 
  array (
    'site_title' => 0,
    'site_name' => 0,
    'site_keywords' => 0,
    'site_description' => 0,
    'css_path' => 0,
    'site_root' => 0,
    'option' => 0,
    'js_path' => 0,
    'cate' => 0,
    'scate' => 0,
    'cate_id' => 0,
    'rel' => 0,
    'quick' => 0,
    'year' => 0,
    'arr' => 0,
    'item' => 0,
    'month' => 0,
    'instat' => 0,
    'outstat' => 0,
    'new' => 0,
    'hot' => 0,
    'link' => 0,
  ),
  'has_nocache_code' => false,
),false); /*/%%SmartyHeaderCode%%*/?>
<?php if ($_valid && !is_callable('content_6548e4caec2266_94836141')) {function content_6548e4caec2266_94836141($_smarty_tpl) {?><!DOCTYPE HTML>



<html>



<head>



<title><?php echo $_smarty_tpl->tpl_vars['site_title']->value;?>
 - <?php echo $_smarty_tpl->tpl_vars['site_name']->value;?>
</title>



<meta charset="utf-8">



<meta name="Keywords" content="<?php echo $_smarty_tpl->tpl_vars['site_keywords']->value;?>
" />



<meta name="Description" content="<?php echo $_smarty_tpl->tpl_vars['site_description']->value;?>
" />
<meta property="og:type" content="image">
<meta property="og:image" content="/logoSpider.png">


<?php echo $_smarty_tpl->getSubTemplate ("header-public.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>




<link href="..<?php echo $_smarty_tpl->tpl_vars['css_path']->value;?>
default/style.css" rel="stylesheet" type="text/css" />



<script type="text/javascript">var $root = '<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
'; var $linktype = '<?php echo $_smarty_tpl->tpl_vars['option']->value['link_struct'];?>
';</script>



<script type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['js_path']->value;?>
jquery.min.js"></script>



<script type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['js_path']->value;?>
common.js"></script>



</head>







<body>



<?php echo $_smarty_tpl->getSubTemplate ("header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>




<div class="wrapper">
<div class="blank10"></div>
<?php echo get_adcode(8);?>

  <div class="blank10"></div>

    <div class="mainbox">



    	<div class="mainbox-left">

			

			<div class="bestbox allcate ">



        		<div class="newbox-title">推荐网站</div>



				<div class="bestbox-body">



					<ul class="clearfix bw-bestbox-list">



						<?php echo get_adcode(9);?>




					</ul>



				</div>



        	</div>

			

			<div class="blank10"></div>

<?php echo get_adcode(11);?>

  <div class="blank10"></div>

			<?php  $_smarty_tpl->tpl_vars['cate'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['cate']->_loop = false;
 $_from = get_categories(); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['cate']->key => $_smarty_tpl->tpl_vars['cate']->value) {
$_smarty_tpl->tpl_vars['cate']->_loop = true;
?>



			<?php if ($_smarty_tpl->tpl_vars['cate']->value['cate_mod']=='website') {?>



			<div class="clearfix indexcatebox allcate">



				<div class="newbox-title"><?php echo $_smarty_tpl->tpl_vars['cate']->value['cate_name'];?>
</div>

				<ul>

					<?php  $_smarty_tpl->tpl_vars['scate'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['scate']->_loop = false;
 $_from = get_categories($_smarty_tpl->tpl_vars['cate']->value['cate_id']); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['scate']->key => $_smarty_tpl->tpl_vars['scate']->value) {
$_smarty_tpl->tpl_vars['scate']->_loop = true;
?>



					<li><h2><a href="<?php echo $_smarty_tpl->tpl_vars['scate']->value['cate_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['scate']->value['cate_name'];?>
网站目录"><?php echo $_smarty_tpl->tpl_vars['scate']->value['cate_name'];?>
</a></h2><em>(<?php echo $_smarty_tpl->tpl_vars['scate']->value['cate_postcount'];?>
)</em></li>



					<?php } ?>



				</ul>



			</div>



			<div class="blank10"></div>



			<?php }?>



			<?php } ?>

			

			

			<div class="clearfix allcate indexcatebox-sj">

				<div class="newbox-title">随机网站</div>

               	<ul>

					<?php  $_smarty_tpl->tpl_vars['rel'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['rel']->_loop = false;
 $_from = get_websites_lunbo($_smarty_tpl->tpl_vars['cate_id']->value,20); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['rel']->key => $_smarty_tpl->tpl_vars['rel']->value) {
$_smarty_tpl->tpl_vars['rel']->_loop = true;
?>

						<li><a href="<?php echo $_smarty_tpl->tpl_vars['rel']->value['web_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['rel']->value['web_name'];?>
</a></li>

					<?php } ?>

				</ul>

			</div>


			<div class="blank10"></div>


			<div class="clearfix allcate indexcatebox-sj">

				<div class="newbox-title">快审站点</div>

               	<ul>
					<?php  $_smarty_tpl->tpl_vars['quick'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['quick']->_loop = false;
 $_from = get_websites(0,10,true); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['quick']->key => $_smarty_tpl->tpl_vars['quick']->value) {
$_smarty_tpl->tpl_vars['quick']->_loop = true;
?>



					<li><a href="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['quick']->value['web_name'];?>
</a></li>



                   	<?php } ?>

					

				</ul>

			</div>
			
			<!--<div class="blank10"></div>-->
   <!--         <div class="newbox">-->
			<!--	<div class="newbox-title">数据归档</div>-->
			<!--	<div class="blank11"></div>-->
			<!--	<ul class="arcbox-list">-->

			<!--		<?php  $_smarty_tpl->tpl_vars['arr'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['arr']->_loop = false;
 $_smarty_tpl->tpl_vars['year'] = new Smarty_Variable;
 $_from = get_archives(); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['arr']->key => $_smarty_tpl->tpl_vars['arr']->value) {
$_smarty_tpl->tpl_vars['arr']->_loop = true;
 $_smarty_tpl->tpl_vars['year']->value = $_smarty_tpl->tpl_vars['arr']->key;
?>-->

   <!--             	<li><b><?php echo $_smarty_tpl->tpl_vars['year']->value;?>
年</b><?php  $_smarty_tpl->tpl_vars['item'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['item']->_loop = false;
 $_smarty_tpl->tpl_vars['month'] = new Smarty_Variable;
 $_from = $_smarty_tpl->tpl_vars['arr']->value; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['item']->key => $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->_loop = true;
 $_smarty_tpl->tpl_vars['month']->value = $_smarty_tpl->tpl_vars['item']->key;
?><a href="<?php echo $_smarty_tpl->tpl_vars['item']->value['arc_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['year']->value;?>
年<?php echo $_smarty_tpl->tpl_vars['month']->value;?>
月共有<?php echo $_smarty_tpl->tpl_vars['item']->value['site_count'];?>
个站点"><?php echo $_smarty_tpl->tpl_vars['month']->value;?>
月</a><?php } ?></li>-->

			<!--		<?php } ?>-->

			<!--	</ul>-->

			<!--</div>-->
			

        </div>



        <div class="mainbox-right">   



			<div class="newbox">



            	<div class="newbox-title">最新点入</div>



                <ul class="newbox-list">



                	<?php  $_smarty_tpl->tpl_vars['instat'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['instat']->_loop = false;
 $_from = get_websites(0,8,false,false,'instat'); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['instat']->key => $_smarty_tpl->tpl_vars['instat']->value) {
$_smarty_tpl->tpl_vars['instat']->_loop = true;
?>



					<li><span><?php echo $_smarty_tpl->tpl_vars['instat']->value['web_ctime'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['instat']->value['web_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['instat']->value['web_name'];?>
</a></li>



                   	<?php } ?>



                </ul>



            </div>

			

			<div class="blank10"></div>



			<div class="newbox">



            	<div class="newbox-title">最新点出</div>



                <ul class="newbox-list">



                	<?php  $_smarty_tpl->tpl_vars['outstat'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['outstat']->_loop = false;
 $_from = get_websites(0,8,false,false,'outstat'); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['outstat']->key => $_smarty_tpl->tpl_vars['outstat']->value) {
$_smarty_tpl->tpl_vars['outstat']->_loop = true;
?>



					<li><span><?php echo $_smarty_tpl->tpl_vars['outstat']->value['web_ctime'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['outstat']->value['web_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['outstat']->value['web_name'];?>
</a></li>



                   	<?php } ?>



                </ul>



            </div>

			

			<div class="blank10"></div>



			<div class="newbox">



            	<div class="newbox-title">最新收录</div>



                <ul class="newbox-list">



                	<?php  $_smarty_tpl->tpl_vars['new'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['new']->_loop = false;
 $_from = get_websites(0,8,false,false,'ctime'); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['new']->key => $_smarty_tpl->tpl_vars['new']->value) {
$_smarty_tpl->tpl_vars['new']->_loop = true;
?>



					<li><span><?php echo $_smarty_tpl->tpl_vars['new']->value['web_ctime'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['new']->value['web_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['new']->value['web_name'];?>
</a></li>



                   	<?php } ?>



                </ul>



            </div>

			

			<div class="blank10"></div>

			<div class="newbox">



            	<div class="newbox-title">等待审核</div>



                <ul class="newbox-list">



					<?php  $_smarty_tpl->tpl_vars['new'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['new']->_loop = false;
 $_from = get_websites_status(0,8); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['new']->key => $_smarty_tpl->tpl_vars['new']->value) {
$_smarty_tpl->tpl_vars['new']->_loop = true;
?>



					<li><span><?php echo $_smarty_tpl->tpl_vars['new']->value['web_ctime'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['new']->value['web_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['new']->value['web_name'];?>
</a></li>



                   	<?php } ?>



                </ul>



            </div>

			

			<div class="blank10"></div>

			<div class="newbox">



            	<div class="newbox-title">人气网站</div>



                <ul class="newbox-list">



                	<?php  $_smarty_tpl->tpl_vars['hot'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['hot']->_loop = false;
 $_from = get_websites(0,8,false,false,'views'); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['hot']->key => $_smarty_tpl->tpl_vars['hot']->value) {
$_smarty_tpl->tpl_vars['hot']->_loop = true;
?>



					<li><span><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_ctime'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
</a></li>



                   	<?php } ?>



                </ul>



            </div>
            



        </div>



    </div>

	<div class="blank10"></div>

	<div class="clearfix allcate indexcatebox-sj yq">

		<div class="newbox-title">友情链接<i class="yl-i">网站权重等于零的已移动到<a rel="nofollow" href="/addurl/" title="网站提交入口">内页</a></i></div>

		<ul>

			<?php  $_smarty_tpl->tpl_vars['link'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['link']->_loop = false;
 $_from = get_links(); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['link']->key => $_smarty_tpl->tpl_vars['link']->value) {
$_smarty_tpl->tpl_vars['link']->_loop = true;
?>

			<li><a href="<?php echo $_smarty_tpl->tpl_vars['link']->value['link_url'];?>
" target="_blank"><?php echo $_smarty_tpl->tpl_vars['link']->value['link_name'];?>
</a></li>

            <?php } ?>

		</ul>

	</div>

</div>



<?php echo $_smarty_tpl->getSubTemplate ("footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>




</body>



</html><?php }} ?>
