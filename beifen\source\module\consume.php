<?php
/** consume list */
function get_consume_list($where = 1, $field = 'time', $order = 'DESC', $start = 0, $pagesize = 0) {
	global $DB, $constypes;
	
	if (!in_array($field, array('id', 'time'))) $field = 'time';
	switch ($field) {
		case 'id' :
			$sortby = "c.cons_id";
			break;
		case 'time' :
			$sortby = "c.cons_time";
			break;
		default :
			$sortby = "c.cons_time";
			break;
	}
	$order = strtoupper($order);
	$sql = "SELECT c.cons_id, c.order_id, c.cons_name, c.cons_type, c.cons_money, c.cons_score, c.cons_status, c.cons_time FROM ".$DB->table('consumes')." c LEFT JOIN ".$DB->table('users')." u ON c.user_id=u.user_id WHERE $where ORDER BY $sortby $order LIMIT $start, $pagesize";
	$query = $DB->query($sql);
	$results = array();
	while ($row = $DB->fetch_array($query)) {
		switch ($row['cons_status']) {
			case 1 :
				$status = '<a href="?mod=consume&act=jump&cid='.$row['cons_id'].'" target="_blank" style="color: #f60;">立即支付</a>';
				break;
			case 2 :
				$status = '<font color="#ff6600">等待发货</font>';
				break;
			case 3 :
				$status = '<a href="http://www.alipay.com/" target="_blank"><font color="#ff6600">确认收货</font></a>';
				break;
			case 4 :
				$status = '<font color="#008833">交易完成</font>';
				break;
			default :
				$status = '<a href="?mod=consume&act=jump&cid='.$row['cons_id'].'" target="_blank" style="color: #f60;">立即支付</a>';
				break;
		}
		$row['cons_type'] = $constypes[$row['cons_type']];
		$row['cons_attr'] = $status;
		$row['cons_time'] = date('Y-m-d H:i:s', $row['cons_time']);
		$results[] = $row;
	}
	$DB->free_result($query);
		
	return $results;
}

/** consume type option */
function get_constype_option($type_id = 0) {
	global $constypes;
	
	$optstr = '';
	foreach ($constypes as $key => $row) {
		$optstr .= '<option value="'.$key.'"';
		if ($type_id > 0 && $type_id == $key) $optstr .= ' selected';
		$optstr .= '>'.$row;
		$optstr .= '</option>';
	}
		
	return $optstr;
}
?>