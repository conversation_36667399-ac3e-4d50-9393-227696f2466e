<?php /* Smarty version Smarty-3.1.18, created on 2023-07-14 09:23:38
         compiled from "/www/wwwroot/digg58.com/templet/default/siteinfo.html" */ ?>
<?php /*%%SmartyHeaderCode:66792547463baa5af8d8d28-30684803%%*/if(!defined('SMARTY_DIR')) exit('no direct access allowed');
$_valid = $_smarty_tpl->decodeProperties(array (
  'file_dependency' => 
  array (
    '2eda88b4ef4f96e8b2b0b825b3ea6400438be16c' => 
    array (
      0 => '/www/wwwroot/digg58.com/templet/default/siteinfo.html',
      1 => 1689297815,
      2 => 'file',
    ),
  ),
  'nocache_hash' => '66792547463baa5af8d8d28-30684803',
  'function' => 
  array (
  ),
  'version' => 'Smarty-3.1.18',
  'unifunc' => 'content_63baa5afba1402_17806258',
  'variables' => 
  array (
    'web' => 0,
    'site_name' => 0,
    'css_path' => 0,
    'site_root' => 0,
    'option' => 0,
    'js_path' => 0,
    'crumb' => 0,
    'rssfeed' => 0,
    'related_website' => 0,
    'rel' => 0,
    'hot' => 0,
    'tags' => 0,
    'item' => 0,
    'new' => 0,
    'best' => 0,
    'year' => 0,
    'arr' => 0,
    'month' => 0,
    'scate' => 0,
  ),
  'has_nocache_code' => false,
),false); /*/%%SmartyHeaderCode%%*/?>
<?php if ($_valid && !is_callable('content_63baa5afba1402_17806258')) {function content_63baa5afba1402_17806258($_smarty_tpl) {?><!DOCTYPE HTML>

<html>

<head>

<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

<title><?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
<?php if ($_smarty_tpl->tpl_vars['web']->value['web_status']==3) {?>_<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
<?php }?> - <?php echo $_smarty_tpl->tpl_vars['web']->value['cate_name'];?>
 - <?php echo $_smarty_tpl->tpl_vars['site_name']->value;?>
</title>

<meta name="description" content="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_description'];?>
" />

<meta name="keywords" content="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
,<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
,<?php echo $_smarty_tpl->tpl_vars['web']->value['cate_name'];?>
" />


<?php echo $_smarty_tpl->getSubTemplate ("header-public.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>

<link href="<?php echo $_smarty_tpl->tpl_vars['css_path']->value;?>
default/style.css" rel="stylesheet" type="text/css" />

<script type="text/javascript">var $root = '<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
'; var $linktype = '<?php echo $_smarty_tpl->tpl_vars['option']->value['link_struct'];?>
';</script>

<script type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['js_path']->value;?>
jquery.min.js"></script>

<script type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['js_path']->value;?>
common.js"></script>

</head>

<body>

<?php echo $_smarty_tpl->getSubTemplate ("header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>


<div class="wrapper">

	<div class="crumb"><?php echo $_smarty_tpl->tpl_vars['crumb']->value;?>
 › <?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
&nbsp;<?php echo $_smarty_tpl->tpl_vars['rssfeed']->value;?>
</div>

	<?php echo get_adcode(8);?>

	<div class="blank10"></div>

    <div class="mainbox">

    	<div class="mainbox-left">
<?php if ($_smarty_tpl->tpl_vars['web']->value['web_status']==3) {?>
<div class="siteinfo">
<?php }?>
<?php if ($_smarty_tpl->tpl_vars['web']->value['web_status']==2) {?>
<i class="wait"></i>
<div class="siteinfo" id="grayscale100">
            	
				<?php }?>
				<h1 class="wtitle">
					<?php if ($_smarty_tpl->tpl_vars['web']->value['web_status']==2) {?><a href="/website/<?php echo $_smarty_tpl->tpl_vars['web']->value['web_id'];?>
.html"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
</a><?php }?>
					<?php if ($_smarty_tpl->tpl_vars['web']->value['web_status']==3) {?>
					<?php if ($_smarty_tpl->tpl_vars['web']->value['web_ispay']==1) {?><a href="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_furl'];?>
" target="_blank" onClick="clickout(<?php echo $_smarty_tpl->tpl_vars['web']->value['web_id'];?>
)"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
</a><?php }?> <?php if ($_smarty_tpl->tpl_vars['web']->value['web_ispay']==0) {?><a><?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
</a><?php }?>
					<?php }?>
            	</h1>

				<ul class="wdata"><li class="line"><em class="red"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_views'];?>
</em>人气指数</li><li class="line"><em class="green"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_grank'];?>
</em>PageRank</li><li class="line"><em class="green"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_brank'];?>
</em>百度权重</li><li class="line"><em class="green"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_srank'];?>
</em>Sogou Rank</li><li class="line"><em class="green"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_arank'];?>
</em>AlexaRank</li><li class="line"><em><?php echo $_smarty_tpl->tpl_vars['web']->value['web_instat'];?>
</em>入站次数</li><li class="line"><em><?php echo $_smarty_tpl->tpl_vars['web']->value['web_outstat'];?>
</em>出站次数</li><li><em class="red"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_ltime'];?>
</em>更新日期</li></ul>

				<div class="clearfix params">
					
						<?php if ($_smarty_tpl->tpl_vars['web']->value['web_status']==3) {?>
            <img src="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_pic'];?>
" onerror="javascript:this.src='/images/imglogo.jpg'" width="130" height="110" alt="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
" class="wthumb" />
            <?php }?>
            <?php if ($_smarty_tpl->tpl_vars['web']->value['web_status']==2) {?>
            <img src="/images/Alexa.jpg" width="130" height="110" alt="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
" class="wthumb" />
            <?php }?>
					
					<ul class="siteitem">
		
				
					<li><strong>网站地址：</strong><?php if ($_smarty_tpl->tpl_vars['web']->value['web_ispay']==1) {?><em class="jump-url" onclick="clickout(<?php echo $_smarty_tpl->tpl_vars['web']->value['web_id'];?>
)">http://<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
</em><?php }?><?php if ($_smarty_tpl->tpl_vars['web']->value['web_ispay']!=1) {?>网址未显示<?php }?></li>
					
            			<li><strong>服务器IP：</strong><?php echo $_smarty_tpl->tpl_vars['web']->value['web_ip'];?>
</li>

                        <li><strong>备案信息：</strong><script type="text/javascript" src="https://icp.aizhan.com/geticp/?host=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
&amp;style=text" charset="utf-8"></script></li>
						
						<li><strong>相关查询：</strong> <a rel="external nofollow" href="http://www.baidu.com/s?wd=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
" target="_blank" title="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
">百度搜索</a> | <a rel="external nofollow" href="http://www.baidu.com/s?wd=site%3A<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
" target="_blank" title="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
百度收录">百度收录</a> | <a rel="external nofollow" href="http://seo.chinaz.com/?host=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
" target="_blank" title="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
网站查询">网站综合查询</a> | <a rel="external nofollow" href="http://whois.chinaz.com/?DomainName=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
" target="_blank" title="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
网域名查询">域名查询</a> | <a rel="external nofollow" href="http://webscan.360.cn/index/checkwebsite?url=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
" target="_blank" title="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
网站安全检测">网站安全检测</a></li>


					</ul>
					
					<div class="intro"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_intro'];?>
</div>
					<div class="intro"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
于<?php echo $_smarty_tpl->tpl_vars['web']->value['web_xtime'];?>
发布于点我目录，并永久归类相关网址导航类别中，本站只是硬性分析 "<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
" 的网站价值及网站可信度，包括Alexa排名、流量估计、网站外链、域名年龄等。网站真正的价值在于它是否为社会的发展带来积极促进作用。"<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
" 的价值还取决于各种因素的综合分析，以网站的流量或收入多少来衡量站点价值当然不够准确。<span class="report" onclick="uptime(<?php echo $_smarty_tpl->tpl_vars['web']->value['web_id'];?>
)">更新日期</span></div>
                    <div class="intro"><strong>特别提示：</strong><br>本网页并非“<i><?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
</i>”官网，页面内容是由【点我目录】编录于互联网，只作展示之用；如果有与“<i><?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
</i>”相关业务事宜，请访问其网站并获取联系方式；【点我目录】与“<i><?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
</i>”无任何关系，对于“<i><?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
</i>”网站中信息，请用户谨慎辨识其真伪。</div>
                
				</div>

				<div class="siteHonour">
					<ul>
						<?php if ($_smarty_tpl->tpl_vars['web']->value['web_ispay']==1) {?><li class="kuaishen">快审</li><?php }?>
						<?php if ($_smarty_tpl->tpl_vars['web']->value['web_isbest']==1) {?><li class="tuijian">推荐</li><?php }?>
						<li class="lanmu"><?php echo $_smarty_tpl->tpl_vars['web']->value['cate_name'];?>
</li>
					</ul>
				</div>

            </div>
			
			<div class="blank10"></div>
			<?php echo get_adcode(11);?>

			
			
			<div class="blank10"></div>
			
        	<div class="clearfix relsite allcate">

            	<div class="newbox-title">Alexa排名/搜索流量占比</div>

               	<ul class="relsite-list-Alexa">

               		<li><img src="/images/Alexa.jpg" width="230" height="180" alt="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
Alexa排名"></li>

					<li><img rel="nofollow" src="/images/Alexa.jpg" width="230" height="180" alt="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
搜索流量占比"></li>

              	</ul>

            </div>
            
           

            <div class="blank10"></div>

        	<div class="clearfix relsite allcate">

            	<div class="newbox-title">相关站点</div>

               	<ul class="relsite-list">

              		<?php  $_smarty_tpl->tpl_vars['rel'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['rel']->_loop = false;
 $_from = $_smarty_tpl->tpl_vars['related_website']->value; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['rel']->key => $_smarty_tpl->tpl_vars['rel']->value) {
$_smarty_tpl->tpl_vars['rel']->_loop = true;
?>

               		<li><a href="<?php echo $_smarty_tpl->tpl_vars['rel']->value['web_link'];?>
" target="_blank" title="<?php echo $_smarty_tpl->tpl_vars['rel']->value['web_name'];?>
"><img src="<?php echo $_smarty_tpl->tpl_vars['rel']->value['web_pic'];?>
" width="129" height="100" alt="<?php echo $_smarty_tpl->tpl_vars['rel']->value['web_name'];?>
" /><p><?php echo $_smarty_tpl->tpl_vars['rel']->value['web_name'];?>
</p></a></li>

               		<?php } ?>

              	</ul>

            </div>
            
            <div class="blank10"></div>

         <div class="clearfix allcate indexcatebox-sj">

            	<div class="newbox-title">本类排行</div>

              

				<ul class="arcbox-list">

              		<?php  $_smarty_tpl->tpl_vars['hot'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['hot']->_loop = false;
 $_from = get_websites($_smarty_tpl->tpl_vars['web']->value['cate_id'],30,false,false,'views'); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['hot']->key => $_smarty_tpl->tpl_vars['hot']->value) {
$_smarty_tpl->tpl_vars['hot']->_loop = true;
?>

            <li><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
</a></li>

            <?php } ?>

              	</ul>

            </div>

        </div>

        <div class="mainbox-right">
			
			<div class="newbox">

            	<div class="newbox-title">相关标签</div>

                <ul id="tag">

                	<?php  $_smarty_tpl->tpl_vars['item'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['item']->_loop = false;
 $_from = $_smarty_tpl->tpl_vars['tags']->value; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['item']->key => $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->_loop = true;
?>

					<li><a href="<?php echo $_smarty_tpl->tpl_vars['item']->value['tag_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['item']->value['tag_name'];?>
</a></li>

                   	<?php } ?>

                </ul>

            </div>

			<div class="blank10"></div>

			<div class="newbox">

            	<div class="newbox-title">最新收录</div>

                <ul class="newbox-list">

                	<?php  $_smarty_tpl->tpl_vars['new'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['new']->_loop = false;
 $_from = get_websites(0,8); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['new']->key => $_smarty_tpl->tpl_vars['new']->value) {
$_smarty_tpl->tpl_vars['new']->_loop = true;
?>

					<li><span><?php echo $_smarty_tpl->tpl_vars['new']->value['web_ctime'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['new']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['new']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['new']->value['web_name'];?>
</a></li>

                   	<?php } ?>

                </ul>

            </div>
			<div class="blank10"></div>

			<div class="newbox">

				<div class="newbox-title">推荐站点</div>

					<ul class="newbox-list">

						<?php  $_smarty_tpl->tpl_vars['best'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['best']->_loop = false;
 $_from = get_websites(0,8,false,true); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['best']->key => $_smarty_tpl->tpl_vars['best']->value) {
$_smarty_tpl->tpl_vars['best']->_loop = true;
?>

						<li><span><?php echo $_smarty_tpl->tpl_vars['best']->value['web_ctime'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['best']->value['web_name'];?>
</a></li>

						<?php } ?>

					</ul>

			</div>
	
			
			<div class="blank10"></div>
			<div class="newbox">

            	<div class="newbox-title">随机网站</div>

                <ul class="newbox-list">

                	<?php  $_smarty_tpl->tpl_vars['rel'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['rel']->_loop = false;
 $_from = get_websites_lunbo($_smarty_tpl->tpl_vars['web']->value['cate_id'],8); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['rel']->key => $_smarty_tpl->tpl_vars['rel']->value) {
$_smarty_tpl->tpl_vars['rel']->_loop = true;
?>
						<li><span><?php echo $_smarty_tpl->tpl_vars['rel']->value['web_ctime'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['rel']->value['web_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['rel']->value['web_name'];?>
</a></li>
					<?php } ?>
                </ul>

            </div>

			<div class="blank10"></div>
			<div class="newbox">

				<ul class="arcbox-list">

					<?php  $_smarty_tpl->tpl_vars['arr'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['arr']->_loop = false;
 $_smarty_tpl->tpl_vars['year'] = new Smarty_Variable;
 $_from = get_archives(); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['arr']->key => $_smarty_tpl->tpl_vars['arr']->value) {
$_smarty_tpl->tpl_vars['arr']->_loop = true;
 $_smarty_tpl->tpl_vars['year']->value = $_smarty_tpl->tpl_vars['arr']->key;
?>

                	<li><b><?php echo $_smarty_tpl->tpl_vars['year']->value;?>
年</b><?php  $_smarty_tpl->tpl_vars['item'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['item']->_loop = false;
 $_smarty_tpl->tpl_vars['month'] = new Smarty_Variable;
 $_from = $_smarty_tpl->tpl_vars['arr']->value; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['item']->key => $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->_loop = true;
 $_smarty_tpl->tpl_vars['month']->value = $_smarty_tpl->tpl_vars['item']->key;
?><a href="<?php echo $_smarty_tpl->tpl_vars['item']->value['arc_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['year']->value;?>
年<?php echo $_smarty_tpl->tpl_vars['month']->value;?>
月共有<?php echo $_smarty_tpl->tpl_vars['item']->value['site_count'];?>
个站点"><?php echo $_smarty_tpl->tpl_vars['month']->value;?>
月</a><?php } ?></li>

					<?php } ?>

				</ul>

			</div>

        </div>
		
		<div class="blank10"></div>

		<div class="clearfix allcate indexcatebox-sj hotdir">
			<div class="newbox-title">热门目录</div>
			<ul>
				<?php  $_smarty_tpl->tpl_vars['scate'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['scate']->_loop = false;
 $_from = get_best_categories(); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['scate']->key => $_smarty_tpl->tpl_vars['scate']->value) {
$_smarty_tpl->tpl_vars['scate']->_loop = true;
?>
						<li><a href="<?php echo $_smarty_tpl->tpl_vars['scate']->value['cate_link'];?>
" target="_blank" title="<?php echo $_smarty_tpl->tpl_vars['scate']->value['cate_name'];?>
网站大全"><?php echo $_smarty_tpl->tpl_vars['scate']->value['cate_name'];?>
</a></li>
				<?php } ?>
			</ul>
		</div>

    </div>

</div>

<?php echo $_smarty_tpl->getSubTemplate ("footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>


</body>

</html><?php }} ?>
