<?php /* Smarty version Smarty-3.1.18, created on 2023-03-20 21:26:33
         compiled from "/www/wwwroot/digg58.com/templet/member/article.html" */ ?>
<?php /*%%SmartyHeaderCode:6887775564185f09a75941-78835201%%*/if(!defined('SMARTY_DIR')) exit('no direct access allowed');
$_valid = $_smarty_tpl->decodeProperties(array (
  'file_dependency' => 
  array (
    '44537034acd6e430a4a3f26e44acbd06f126cef4' => 
    array (
      0 => '/www/wwwroot/digg58.com/templet/member/article.html',
      1 => 1418038320,
      2 => 'file',
    ),
  ),
  'nocache_hash' => '6887775564185f09a75941-78835201',
  'function' => 
  array (
  ),
  'variables' => 
  array (
    'page_name' => 0,
    'action' => 0,
    'articles' => 0,
    'row' => 0,
    'showpage' => 0,
    'option' => 0,
    'myself' => 0,
    'cate_pids' => 0,
    'item' => 0,
    'do' => 0,
    'js_path' => 0,
  ),
  'has_nocache_code' => false,
  'version' => 'Smarty-3.1.18',
  'unifunc' => 'content_64185f09b49254_05676807',
),false); /*/%%SmartyHeaderCode%%*/?>
<?php if ($_valid && !is_callable('content_64185f09b49254_05676807')) {function content_64185f09b49254_05676807($_smarty_tpl) {?><?php echo $_smarty_tpl->getSubTemplate ("header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>


		

<div class="title"><?php echo $_smarty_tpl->tpl_vars['page_name']->value;?>
</div>

<div class="content">

	<?php if ($_smarty_tpl->tpl_vars['action']->value=='list') {?>

	<div class="listbox">

		<table width="100%" border="0" cellspacing="1" cellpadding="0">

			<tr>

				<th>ID</th>

				<th>所属分类</th>

				<th>文章标题</th>

				<th>属性状态</th>

				<th>发布时间</th>

				<th>操作选项</th>

			</tr>

			<?php  $_smarty_tpl->tpl_vars['row'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['row']->_loop = false;
 $_from = $_smarty_tpl->tpl_vars['articles']->value; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['row']->key => $_smarty_tpl->tpl_vars['row']->value) {
$_smarty_tpl->tpl_vars['row']->_loop = true;
?>

			<tr>

				<td><?php echo $_smarty_tpl->tpl_vars['row']->value['art_id'];?>
</td>

				<td><?php echo $_smarty_tpl->tpl_vars['row']->value['cate_name'];?>
</td>

				<td class="textleft"><?php echo $_smarty_tpl->tpl_vars['row']->value['art_title'];?>
</td>

				<td><?php echo $_smarty_tpl->tpl_vars['row']->value['art_attr'];?>
</td>

				<td><?php echo $_smarty_tpl->tpl_vars['row']->value['art_ctime'];?>
</td>

				<td><a href="?mod=article&act=edit&aid=<?php echo $_smarty_tpl->tpl_vars['row']->value['art_id'];?>
">编辑</a></td>

			</tr>

			<?php }
if (!$_smarty_tpl->tpl_vars['row']->_loop) {
?>

			<tr><td colspan="6">您还未发布任何文章！</td></tr>

			<?php } ?>

		</table>

	</div>

	<div class="showpage"><?php echo $_smarty_tpl->tpl_vars['showpage']->value;?>
</div>

	<?php }?>



	<?php if ($_smarty_tpl->tpl_vars['action']->value=='add'||$_smarty_tpl->tpl_vars['action']->value=='edit') {?>

	<div class="formbox">

		<form name="myfrom" id="myfrom" method="post" action="?mod=article">

		<ul>

			<?php if ($_smarty_tpl->tpl_vars['option']->value['post_article_score']>0) {?><li style="color: #083;"><strong>&nbsp;</strong>当前发布文章，需要花费 <b><?php echo $_smarty_tpl->tpl_vars['option']->value['post_article_score'];?>
</b> 个积分，可用积分：<b><?php echo $_smarty_tpl->tpl_vars['myself']->value['user_score'];?>
</b></li><?php }?>

			<li><strong>选择分类：</strong><?php  $_smarty_tpl->tpl_vars['item'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['item']->_loop = false;
 $_from = $_smarty_tpl->tpl_vars['cate_pids']->value; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['item']->key => $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->_loop = true;
?><select name="level_<?php echo $_smarty_tpl->tpl_vars['item']->value;?>
" id="level_<?php echo $_smarty_tpl->tpl_vars['item']->value;?>
"></select><?php }
if (!$_smarty_tpl->tpl_vars['item']->_loop) {
?><select name="level_1" id="level_1"></select><?php } ?><input type="hidden" name="cate_id" id="cate_id" value="<?php echo !$_smarty_tpl->tpl_vars['row']->value['cate_id'] ? '0' : $_smarty_tpl->tpl_vars['row']->value['cate_id'];?>
"></li>

			<li><strong>文章标题：</strong><input name="art_title" type="text" class="ipt" id="art_title" size="50" maxlength="100" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['art_title'];?>
" /></li>

			<li><strong>TAG标签：</strong><input name="art_tags" type="text" class="ipt" id="art_tags" size="50" maxlength="50" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['art_tags'];?>
" onBlur="javascript:this.value=this.value.replace(/，/ig,',');" /></li>

			<li><strong>内容来源：</strong><input name="copy_from" type="text" class="ipt" id="copy_from" size="50" maxlength="50" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['copy_from'];?>
" /></li>

			<li><strong>来源地址：</strong><input name="copy_url" type="text" class="ipt" id="copy_url" size="50" maxlength="200" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['copy_url'];?>
" /></li>

			<li><strong>内容摘要：</strong><textarea name="art_intro" cols="52" rows="3" class="ipt" id="art_intro"><?php echo $_smarty_tpl->tpl_vars['row']->value['art_intro'];?>
</textarea></li>

			<li><strong>文章内容：</strong>

			<script type="text/javascript">

			var editor;

			KindEditor.ready(function(K) {

			editor = K.create('textarea[name="art_content"]', {

				resizeType : 1,

				allowPreviewEmoticons : false,

				allowImageUpload : true,

				uploadJson : '?mod=upload',

				items : [

					'source', '|', 'fontname', 'fontsize', '|', 'forecolor', 'hilitecolor', 'bold', 'italic', 'underline',

					'removeformat', '|', 'justifyleft', 'justifycenter', 'justifyright', 'insertorderedlist',

					'insertunorderedlist', '|', 'emoticons', 'image', 'link', '|', 'fullscreen']

				});

			});

    		</script>

    		<textarea name="art_content" id="art_content" cols="50" rows="6" class="ipt" style="width: 650px; height: 450px; visibility: hidden;"><?php echo $_smarty_tpl->tpl_vars['row']->value['art_content'];?>
</textarea></li>

			<li><strong>&nbsp;</strong><?php if ($_smarty_tpl->tpl_vars['action']->value=='edit') {?><input type="hidden" name="art_id" id="art_id" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['art_id'];?>
"><?php }?><input type="hidden" name="do" id="do" value="<?php echo $_smarty_tpl->tpl_vars['do']->value;?>
"><input type="submit" class="btn" value="提 交"> &nbsp;<input type="reset" class="btn" value="重 填"></li>

		</ul>

		</form>

	</div>

	<?php }?>

	<script src="<?php echo $_smarty_tpl->tpl_vars['js_path']->value;?>
linkage.select.js" type="text/javascript"></script>

	<script type="text/javascript">

	var options = {

		ajax : '?mod=ajaxget&type=arccate',

		auto : true,

		field_name : '[name=cate_id]'

	}

	

	var sel = new LinkageSelect(options);

	<?php if ($_smarty_tpl->tpl_vars['action']->value=='add') {?>

	sel.bind('#level_1');

	<?php } else { ?>

	<?php  $_smarty_tpl->tpl_vars['item'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['item']->_loop = false;
 $_smarty_tpl->tpl_vars['key'] = new Smarty_Variable;
 $_from = $_smarty_tpl->tpl_vars['cate_pids']->value; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['item']->key => $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->_loop = true;
 $_smarty_tpl->tpl_vars['key']->value = $_smarty_tpl->tpl_vars['item']->key;
?>

	sel.bind('#level_<?php echo $_smarty_tpl->tpl_vars['item']->value;?>
', <?php echo $_smarty_tpl->tpl_vars['item']->value;?>
);

	<?php } ?>

	<?php }?>

	</script>

</div>

            

<?php echo $_smarty_tpl->getSubTemplate ("footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>
<?php }} ?>
