version: '3.8'

services:
  # Nginx Web Server
  nginx:
    image: nginx:alpine
    container_name: directory_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - ./public:/var/www/html/public
      - ./docker/ssl:/etc/nginx/ssl
    depends_on:
      - php
      - frontend
    networks:
      - directory_network
    restart: unless-stopped

  # PHP-FPM Application Server
  php:
    build:
      context: .
      dockerfile: docker/php/Dockerfile
    container_name: directory_php
    volumes:
      - .:/var/www/html
      - ./docker/php/php.ini:/usr/local/etc/php/php.ini
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_DATABASE=directory_db
      - DB_USERNAME=directory_user
      - DB_PASSWORD=directory_pass
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - <PERSON>LASTICSEARCH_HOST=elasticsearch
      - ELASTICSEARCH_PORT=9200
    depends_on:
      - mysql
      - redis
      - elasticsearch
    networks:
      - directory_network
    restart: unless-stopped

  # Frontend Development Server
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: directory_frontend
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://localhost/api
    networks:
      - directory_network
    restart: unless-stopped

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: directory_mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: directory_db
      MYSQL_USER: directory_user
      MYSQL_PASSWORD: directory_pass
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/my.cnf
    networks:
      - directory_network
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: directory_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/etc/redis/redis.conf
    networks:
      - directory_network
    restart: unless-stopped
    command: redis-server /etc/redis/redis.conf

  # Elasticsearch Search Engine
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: directory_elasticsearch
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - directory_network
    restart: unless-stopped

  # Kibana (Optional - for Elasticsearch management)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: directory_kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - directory_network
    restart: unless-stopped

  # phpMyAdmin (Optional - for database management)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: directory_phpmyadmin
    ports:
      - "8080:80"
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: directory_user
      PMA_PASSWORD: directory_pass
    depends_on:
      - mysql
    networks:
      - directory_network
    restart: unless-stopped

  # Redis Commander (Optional - for Redis management)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: directory_redis_commander
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    depends_on:
      - redis
    networks:
      - directory_network
    restart: unless-stopped

  # Queue Worker (for background jobs)
  queue:
    build:
      context: .
      dockerfile: docker/php/Dockerfile
    container_name: directory_queue
    volumes:
      - .:/var/www/html
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_DATABASE=directory_db
      - DB_USERNAME=directory_user
      - DB_PASSWORD=directory_pass
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - mysql
      - redis
    networks:
      - directory_network
    restart: unless-stopped
    command: php artisan queue:work --sleep=3 --tries=3

  # Scheduler (for cron jobs)
  scheduler:
    build:
      context: .
      dockerfile: docker/php/Dockerfile
    container_name: directory_scheduler
    volumes:
      - .:/var/www/html
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_DATABASE=directory_db
      - DB_USERNAME=directory_user
      - DB_PASSWORD=directory_pass
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - mysql
      - redis
    networks:
      - directory_network
    restart: unless-stopped
    command: php artisan schedule:work

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  elasticsearch_data:
    driver: local

networks:
  directory_network:
    driver: bridge
