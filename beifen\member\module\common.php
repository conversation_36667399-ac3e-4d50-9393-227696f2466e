<?php
if (!defined('IN_MEMBER')) exit('Access Denied');

function smarty_output($template, $cache_id = NULL, $compile_id = NULL) {
	global $smarty, $options;
	
	template_exists($template);

	$smarty->assign('js_path', $options['site_root'].'public/scripts/'); #js path
	$smarty->assign('img_path', $options['site_root'].'public/images/'); #img path
	$smarty->assign('css_path', $options['site_root'].'public/style/'); #img path
	$smarty->assign('editor_path', $options['site_root'].'public/editor/'); #editor path
	$smarty->assign('theme_path', $options['site_root'].THEME_DIR.'/'); #theme path
	$smarty->assign('site_name', $options['site_name']);
	$smarty->assign('site_title', $options['site_title']);
	$smarty->assign('site_url', $options['site_url']);
	$smarty->assign('site_root', $options['site_root']);
	$smarty->assign('site_keywords', $options['site_keywords']);
	$smarty->assign('site_description', $options['site_description']);
	$smarty->assign('site_copyright', $options['site_copyright']);
	$smarty->assign('option', $options); #options
	$smarty->assign('script_time', get_scripttime()); #script time
	
	#parse template and output
	$content = $smarty->fetch($template, $cache_id, $compile_id);
	echo $content;
}

function get_scripttime() {
	global $DB, $options, $start_time;
	
	$mtime = explode(' ', microtime());
	$end_time = $mtime[1] + $mtime[0];
	$exec_time = number_format(($end_time - $start_time), 6);
	$gzip = $options['is_enabled_gzip'] == 'yes' ? 'Enabled' : 'Disabled';
	
	return 'Processed in '.$exec_time.' second(s), '.$DB->queries.' Queries, Gzip '.$gzip;
}

function insert_script_time() {
	return get_scripttime();
}
	
/** site path */
function get_crumb() {
	global $options;
	
	$strpath = '当前位置：<a href="'.$options['site_url'].'">'.$options['site_name'].'</a> &rsaquo; <a href="'.$options['site_root'].'member/?mod=home">会员中心</a>';
	
	return $strpath;
}
?>