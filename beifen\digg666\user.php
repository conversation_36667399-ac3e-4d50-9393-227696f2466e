<?php

require('common.php');

require(CORE_PATH.'module/user.php');



$fileurl = 'user.php';

$tplfile = 'user.html';

$table = $DB->table('users');



if (!isset($action)) $action = 'list';



/** list */

if ($action == 'list') {

	$page_name = '会员列表';

	

	$root_id = intval($_GET['root_id']);

	$user_type = trim($_GET['user_type']);

	$keywords = addslashes(trim($_POST['keywords'] ? $_POST['keywords'] : $_GET['keywords']));

	$keyurl = !empty($keywords) ? '?keywords='.urlencode($keywords) : '';

	$page_url = $fileurl.$keyurl;

	

	$where = '1';

	$where .= $root_id > 0 ? " AND root_id='$root_id'" : "";

	$where .= !empty($user_type) ? " AND user_type='$user_type'" : "";

	$where .= !empty($keywords) ? " AND user_email like '%$keywords%' OR nick_name like '%$keywords%'" : "";

	$result = get_user_list($where, 'join_time', 'DESC', $start, $pagesize);

	$users = array();

	foreach ($result as $row) {

		$row['user_type'] = $usertypes[$row['user_type']];

		$row['join_time'] = date('Y-m-d H:i:s', $row['join_time']);

		$row['user_status'] = $row['user_status'] == 1 ? '<span class="gre">正常</span>' : '<span class="red">待验证</span>';

		$row['user_opera'] = '<a href="'.$fileurl.'?act=edit&user_id='.$row['user_id'].'">编辑</a>&nbsp;|&nbsp;<a href="'.$fileurl.'?act=del&user_id='.$row['user_id'].'" onClick="return confirm(\'确认删除此内容吗？\');">删除</a>';

		$users[] = $row;

	}

	

	$total = $DB->get_count($table, $where);	

	$showpage = showpage($page_url, $total, $curpage, $pagesize);

	

	$smarty->assign('usertype_option', get_usertype_option($user_type));

	$smarty->assign('user_type', $user_type);

	$smarty->assign('keywords', $keywords);

	$smarty->assign('users', $users);

	$smarty->assign('showpage', $showpage);

}



/** add */

if ($action == 'add') {

	$page_name = '添加会员';

	

	$smarty->assign('usertype_option', get_usertype_option());

	$smarty->assign('status', 1);

	$smarty->assign('h_action', 'saveadd');

}



/** edit */

if ($action == 'edit') {

	$page_name = '编辑会员';

	

	$user_id = intval($_GET['user_id']);

	$row = get_one_user($user_id);

	if (!$row) {

		alert('指定的会员不存在！');

	}

	

	$smarty->assign('usertype_option', get_usertype_option($row['user_type']));

	$smarty->assign('status', $row['user_status']);

	$smarty->assign('row', $row);

	$smarty->assign('h_action', 'saveedit');

}



/** save data */

if (in_array($action, array('saveadd', 'saveedit'))) {

	$user_type = trim($_POST['user_type']);

	$user_email = trim($_POST['user_email']);

	$user_pass = trim($_POST['user_pass']);

	$nick_name = trim($_POST['nick_name']);

	$user_qq = trim($_POST['user_qq']);

	$user_score = intval($_POST['user_score']);

	$user_status = intval($_POST['user_status']);

	$join_time = time();

	

	if (empty($user_type)) {

		alert('请选择会员类型！');

	}

	

	if (empty($user_email)) {

		alert('请输入电子邮箱！');

	} else {

		if (!is_valid_email($user_email)) {

			alert('请输入正确的电子邮箱！');

		}

	}

	

	if (!empty($user_pass)) {

		alert('请输入登录密码！');

	}	

	

	$data = array(

		'user_type' => $user_type,

		'user_email' => $user_email,
		
        'user_pass' => md5($user_pass),
				
		'nick_name' => $nick_name,

		'user_qq' => $user_qq,

		'user_score' => $user_score,

		'user_status' => $user_status,

		'join_time' => $join_time,

	);

	

	if ($action == 'saveadd') {

    	$query = $DB->query("SELECT user_id FROM $table WHERE user_email='$user_email'");

    	if ($DB->num_rows($query)) {

        	alert('您所添加的会员已存在！');

    	}

		

		$data['user_pass'] = md5($user_pass);

		$DB->insert($table, $data);

		

		alert('会员添加成功！', $fileurl.'?act=add');	

	} elseif ($action == 'saveedit') {

		$user_id = intval($_POST['user_id']);

		$where = array('user_id' => $user_id);



		if (!empty($user_pass)) {

			$data['user_pass'] = md5($user_pass);

		}

		unset($data['join_time']);		

		$DB->update($table, $data, $where);

		

		alert('会员编辑成功！', $fileurl);

	}

}



/** del */

if ($action == 'del') {

	$user_ids = (array) ($_POST['user_id'] ? $_POST['user_id'] : $_GET['user_id']);

	

	$DB->delete($table, 'user_id IN ('.dimplode($user_ids).')');

	

	alert('会员删除成功！', $fileurl);

}



/** set pass */

if ($action == 'setpass') {

	$user_ids = (array) ($_POST['user_id'] ? $_POST['user_id'] : $_GET['user_id']);

	

	$DB->update($table, array('user_status' => 1), 'user_id IN ('.dimplode($user_ids).')');

	

	alert('所选内容设置成功！', $fileurl);

}



/** del */

if ($action == 'nopass') {

	$user_ids = (array) ($_POST['user_id'] ? $_POST['user_id'] : $_GET['user_id']);

	

	$DB->update($table, array('user_status' => 0), 'user_id IN ('.dimplode($user_ids).')');

	

	alert('所选内容设置成功！', $fileurl);

}



smarty_output($tplfile);

?>