<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$page_name = '链接交换';
$page_url = '?mod=weblink';
$tplfile = 'weblink.html';
$tpldir = 'weblink';
$table = $DB->table('weblinks');

/** 缓存设置 */
$smarty->compile_dir .= $tpldir;
$smarty->cache_dir .= $tpldir;
$smarty->cache_lifetime = $options['cache_time_list'] * 3600;

$pagesize = 30;
$curpage = intval($_GET['page']);
if ($curpage > 1) {
	$start = ($curpage - 1) * $pagesize;
} else {
	$start = 0;
	$curpage = 1;
}
$pagestr = ($curpage > 0) ? ' - 第'.$curpage.'页': '';
		
$deal_type = intval($_GET['type']);
$cache_id = $deal_type.'-'.$curpage;

if (!$smarty->isCached($tplfile, $cache_id)) {	
	$where = "l.link_hide=0";
	if ($deal_type > 0) {
		$page_url .= '&type='.$deal_type;
		if ($deal_type > 0) $where .= " AND l.deal_type='$deal_type'";
	}
	
	$results = get_weblink_list($where, 'time', 'DESC', $start, $pagesize);
	$weblinks = array();
	foreach($results as $row) {
		$user = get_one_user($row['user_id']);
		$row['user_qq'] = $user['user_qq'];
		$row['deal_type'] = $dealtypes[$row['deal_type']];
		$row['link_price'] = ($row['link_price'] > 0 ? $row['link_price'] : '面议');
		$row['link_time'] = date('Y-m-d', $row['link_time']);
		$row['cate_link'] = get_category_url('website', $row['cate_id']);
		$row['web_link'] = get_linkinfo_url($row['link_id']);
		$weblinks[] = $row;
	}
	$total = $DB->get_count($table.' l', $where);
	$showpage = showpage($page_url, $total, $curpage, $pagesize);
			
	$smarty->assign('page_name', $page_name);
	$smarty->assign('pagestr', $pagestr);
	$smarty->assign('crumb', get_crumb('weblink'));
	$smarty->assign('rssfeed', get_rssfeed('website'));
	$smarty->assign('total', $total);
	$smarty->assign('weblinks', $weblinks);
	$smarty->assign('showpage', $showpage);
}

smarty_output($tplfile, $cache_id);
?>