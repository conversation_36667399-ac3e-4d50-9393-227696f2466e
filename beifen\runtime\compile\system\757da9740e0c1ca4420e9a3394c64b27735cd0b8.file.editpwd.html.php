<?php /* Smarty version Smarty-3.1.18, created on 2023-11-06 21:15:19
         compiled from "/www/wwwroot/www.8t.lv/templet/system/editpwd.html" */ ?>
<?php /*%%SmartyHeaderCode:3839722876548e6e793bf18-57261248%%*/if(!defined('SMARTY_DIR')) exit('no direct access allowed');
$_valid = $_smarty_tpl->decodeProperties(array (
  'file_dependency' => 
  array (
    '757da9740e0c1ca4420e9a3394c64b27735cd0b8' => 
    array (
      0 => '/www/wwwroot/www.8t.lv/templet/system/editpwd.html',
      1 => 1418038320,
      2 => 'file',
    ),
  ),
  'nocache_hash' => '3839722876548e6e793bf18-57261248',
  'function' => 
  array (
  ),
  'variables' => 
  array (
    'page_name' => 0,
    'fileurl' => 0,
    'myself' => 0,
  ),
  'has_nocache_code' => false,
  'version' => 'Smarty-3.1.18',
  'unifunc' => 'content_6548e6e797eaf2_95687299',
),false); /*/%%SmartyHeaderCode%%*/?>
<?php if ($_valid && !is_callable('content_6548e6e797eaf2_95687299')) {function content_6548e6e797eaf2_95687299($_smarty_tpl) {?><?php echo $_smarty_tpl->getSubTemplate ("header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>




    <h3 class="title"><em><?php echo $_smarty_tpl->tpl_vars['page_name']->value;?>
</em></h3>

	<div class="formbox">

       	<form name="mform" method="post" action="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">

		<table width="100%" border="0" cellspacing="1" cellpadding="0">

			<tr>

				<th>电子邮箱：</th>

				<td><input name="user_email" type="text" class="ipt" id="user_email" size="30" maxlength="30" value="<?php echo $_smarty_tpl->tpl_vars['myself']->value['user_email'];?>
" /></td>

			</tr>

			<tr>

				<th>用户昵称：</th>

				<td><input name="nick_name" type="text" class="ipt" id="nick_name" size="30" maxlength="30" value="<?php echo $_smarty_tpl->tpl_vars['myself']->value['nick_name'];?>
" /></td>

			</tr>

			<tr>

				<th>原始密码：</th>

				<td><input name="user_pass" type="password" class="ipt" id="user_pass" size="30" maxlength="30" value="" /></td>

			</tr>

			<tr>

				<th>新 密 码：</th>

				<td><input name="new_pass" type="password" class="ipt" id="new_pass" size="30" maxlength="30" value="" /></td>

			</tr>

			<tr>

				<th>确认密码：</th>

				<td><input name="new_pass1" type="password" class="ipt" id="new_pass1" size="30" maxlength="30" value="" /></td>

			</tr>

			<tr class="btnbox">

            	<th>&nbsp;</th>

				<td>

                	<input name="user_id" type="hidden" id="user_id" value="<?php echo $_smarty_tpl->tpl_vars['myself']->value['user_id'];?>
">

					<input name="act" type="hidden" id="act" value="saveedit">

					<input type="submit" class="btn" value="确认修改">

				</td>

			</tr>

		</table>

        </form>

	</div>



<?php echo $_smarty_tpl->getSubTemplate ("footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>
<?php }} ?>
