<?php

require('common.php');



$page_name = '修改密码';

$fileurl = 'editpwd.php';

$tplfile = 'editpwd.html';

$table = $DB->table('users');



if ($action == 'saveedit') {

	$user_id = intval($_POST['user_id']);

	$user_email = trim($_POST['user_email']);

	$nick_name = trim($_POST['nick_name']);

	$user_pass = trim($_POST['user_pass']);

	$new_pass = trim($_POST['new_pass']);

	$new_pass1 = trim($_POST['new_pass1']);

	

	if (empty($user_email) || !is_valid_email($user_email)) {

		alert('请输入正确的电子邮箱！');

	}

	

	if (empty($nick_name)) {

		alert('请输入昵称！');

	}

	

	if (empty($user_pass)) {

		alert('请输入原始密码！');

	}

	

	if (empty($new_pass)) {

		alert('请输入新密码！');

	}

	

	if (empty($new_pass1)) {

		alert('请输入确认密码！');

	}

	

	if ($new_pass != $new_pass1) {

		alert('您两次输入的密码不一致！');

	}

	

	$user_pass = md5($user_pass);

	$new_pass = md5($new_pass);

	

	$user = $DB->fetch_one("SELECT user_id, user_pass FROM $table WHERE user_id='$user_id'");

	if (!$user) {

		alert('不存在此用户！');

	} else {

		if ($user_pass != $user['user_pass']) {

			alert('您输入的原始密码不正确！');

		}

		$DB->update($table, array('user_email' => $user_email, 'nick_name' => $nick_name, 'user_pass' => $new_pass), array('user_id' => $user['user_id']));

	}

	

	alert('帐号密码修改成功！', $fileurl);

}



smarty_output($tplfile);

?>