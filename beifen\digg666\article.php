<?php

require('common.php');

require(CORE_PATH.'module/arccate.php');

require(CORE_PATH.'module/article.php');

require(CORE_PATH.'module/prelink.php');



$fileurl = 'article.php';

$tplfile = 'article.html';

$table = $DB->table('articles');



if (!isset($action)) $action = 'list';



/** list */

if ($action == 'list') {

	$page_name = '文章列表';

	

	$status = intval($_GET['status']);

	$user_id = intval($_GET['user_id']);

	$cate_id = intval($_GET['cate_id']);

	$sort = intval($_GET['sort']);

	$order = strtoupper(trim($_GET['order']));

	$keywords = addslashes(trim($_POST['keywords'] ? $_POST['keywords'] : $_GET['keywords']));

	if (empty($order)) $order = 'DESC';

	

	$page_url = $fileurl.'?status='.$status.'&user_id='.$user_id.'&cate_id='.$cate_id.'&sort='.$sort.'&order='.$order;

	$keyurl = !empty($keywords) ? '&keywords='.urlencode($keywords) : '';

	$page_url .= $keyurl;

	

	$category_option = get_arccate_option(0, $cate_id, 0);

	

	$smarty->assign('status', $status);

	$smarty->assign('user_id', $user_id);

	$smarty->assign('cate_id', $cate_id);

	$smarty->assign('sort', $sort);

	$smarty->assign('order', $order);

	$smarty->assign('keywords', $keywords);

	$smarty->assign('keyurl', $keyurl);

	$smarty->assign('category_option', $category_option);

	

	$where = "";

	$sql = "SELECT a.art_id, a.user_id, a.cate_id, a.art_title, a.art_tags, a.art_intro, a.art_views, a.art_istop, a.art_isbest, a.art_status, a.art_ctime, c.cate_name, u.nick_name FROM ".$DB->table('articles')." a LEFT JOIN ".$DB->table('arccate')." c ON a.cate_id=c.cate_id LEFT JOIN ".$DB->table('users')." u ON a.user_id=u.user_id WHERE";

	switch ($status) {

		case 1 :

			$where .= " a.art_status=1";

			break;

		case 2 :

			$where .= " a.art_status=2";

			break;

		case 3 :

			$where .= " a.art_status=3";

			break;

		default :

			$where .= " a.art_status>-1";

			break;

	}

	

	if ($user_id > 0) {

		$where .= " AND a.user_id = '$user_id'";

	}

	

	if ($cate_id > 0) {

		$cate = get_one_arccate($cate_id);

		$where .= " AND a.cate_id IN (".$cate['cate_arrchildid'].")";

	}

	

	if ($keywords) $where .= " AND a.art_title like '%$keywords%'";

	

	switch ($sort) {

		case 1 :

			$field = "a.art_ctime";

			break;

		case 2 :

			$field = "a.art_views";

			break;

		default :

			$field = "a.art_ctime";

			break;

	}

	

	$sql .= $where." ORDER BY a.art_istop DESC, $field $order LIMIT $start, $pagesize";

	$query = $DB->query($sql);

	$articles = array();

	while ($row = $DB->fetch_array($query)) {

		switch ($row['art_status']) {

			case 1 :

				$art_status = '<font color="#333333">草稿</font>';

				break;

			case 2 :

				$art_status = '<font color="#ff3300">待审核</font>';

				break;

			case 3 :

				$art_status = '<font color="#008800">已审核</font>';

				break;

		}

		$art_istop = $row['art_istop'] > 0 ? '<font color="#ff0000">置顶</font>' : '<font color="#cccccc">置顶</font>';

		$art_isbest = $row['art_isbest'] > 0 ? '<font color="#ff3300">推荐</font>' : '<font color="#cccccc">推荐</font>';

		$row['art_attr'] = $art_istop.' - '.$art_isbest.' - '.$art_status;

		$row['art_cate'] = '<a href="'.$fileurl.'?cate_id='.$row['cate_id'].'">'.$row['cate_name'].'</a>';

		$row['nick_name'] = '<a href="'.$fileurl.'?user_id='.$row['user_id'].'" title="查看该用户发布的所有文章">'.$row['nick_name'].'</a>';

		$row['art_ctime'] = date('Y-m-d', $row['art_ctime']);

		$row['art_opera'] = '<a href="'.$fileurl.'?act=edit&art_id='.$row['art_id'].'">编辑</a>&nbsp;|&nbsp;<a href="'.$fileurl.'?act=del&art_id='.$row['art_id'].'" onClick="return confirm(\'确认删除此内容吗？\');">删除</a>';

		$articles[] = $row;

	}

	

	$total = $DB->get_count($table.' a', $where);	

	$showpage = showpage($page_url, $total, $curpage, $pagesize);

	

	$smarty->assign('keywords', $keywords);

	$smarty->assign('articles', $articles);

	$smarty->assign('showpage', $showpage);

}



/** add */

if ($action == 'add') {

	$page_name = '添加文章';



	$cate_id = intval($_GET['cate_id']);

	$token = str_replace(array('+', '/'), array('@', '$'), $_COOKIE['authcode']);

	

	$smarty->assign('token', $token);

	$smarty->assign('status', 3);

	$smarty->assign('h_action', 'saveadd');

}



/** edit */

if ($action == 'edit') {

	$page_name = '编辑文章';

	

	$art_id = intval($_GET['art_id']);

	$where = "a.art_id='$art_id'";

	$row = get_one_article($where);

	if (!$row) {

		alert('指定的内容不存在！');

	}

	

	$token = str_replace(array('+', '/'), array('@', '$'), $_COOKIE['authcode']);

	$cate_pids = array();

	$crow = get_one_arccate($row['cate_id']);

	if ($crow) {

		$parent_cids = $crow['cate_arrparentid'].','.$row['cate_id']; #分类父ID

		if (strpos($parent_cids, ',') !== false) {

			$cate_pids = explode(',', $parent_cids);

			array_shift($cate_pids);

		} else {

			$cate_pids = (array) $parent_cids;

		}		

	}

	

	$row['art_content'] = str_replace('[upload_dir]', $options['site_root'].$options['upload_dir'].'/article/', $row['art_content']);

	

	$smarty->assign('token', $token);

	$smarty->assign('cate_pids', $cate_pids);

	$smarty->assign('ispay', $row['art_ispay']);

	$smarty->assign('istop', $row['art_istop']);

	$smarty->assign('isbest', $row['art_isbest']);

	$smarty->assign('status', $row['art_status']);

	$smarty->assign('row', $row);

	$smarty->assign('h_action', 'saveedit');

}



/** move */

if ($action == 'move') {

	$page_name = '移动文章';

			

	$art_ids = (array) ($_POST['art_id'] ? $_POST['art_id'] : $_GET['art_id']);

	if (empty($art_ids)) {

		alert('请选择要移动的文章！');

	}

	$aids = dimplode($art_ids);

	

	$category_option = get_arccate_option(0, 0, 0);

	$articles = $DB->fetch_all("SELECT art_id, art_title FROM $table WHERE art_id IN ($aids)");

	

	$smarty->assign('category_option', $category_option);

	$smarty->assign('articles', $articles);

	$smarty->assign('h_action', 'savemove');

}



/** attr */

if ($action == 'attr') {

	$page_name = '属性设置';

	

	$art_ids = (array) ($_POST['art_id'] ? $_POST['art_id'] : $_GET['art_id']);

	if (empty($art_ids)) {

		alert('请选择要设置的文章！');

	}	

	$aids = dimplode($art_ids);

	

	$category_option = get_arccate_option(0, 0, 0);

	$articles = $DB->fetch_all("SELECT art_id, art_title FROM $table WHERE art_id IN ($aids)");

	

	$smarty->assign('category_option', $category_option);

	$smarty->assign('articles', $articles);

	$smarty->assign('h_action', 'saveattr');

}



/** save data */

if (in_array($action, array('saveadd', 'saveedit'))) {

	$cate_id = intval($_POST['cate_id']);

	$art_title = trim($_POST['art_title']);

	$art_tags = addslashes(trim($_POST['art_tags']));

	$copy_from = trim($_POST['copy_from']);

	$copy_url = trim($_POST['copy_url']);

	$art_intro = strip_tags(trim($_POST['art_intro']));

	$art_content = $_POST['art_content'];

	$art_views = intval($_POST['art_views']);

	$art_ispay = intval($_POST['art_ispay']);

	$art_istop = intval($_POST['art_istop']);

	$art_isbest = intval($_POST['art_isbest']);

	$art_status = intval($_POST['art_status']);

	$art_time = time();

	

	if ($cate_id <= 0) {

		alert('请选择文章所属分类！');

	} else {

		$row = get_one_arccate($cate_id);

		if ($row['cate_childcount'] > 0) {

			alert('指定的分类下有子分类，请选择子分类进行操作！');

		}

	}

	

	if (empty($art_title)) {

		alert('请输入文章标题！');

	}

	

	if (empty($art_tags)) {

		alert('请输入TAG标签！');

	} else {

		$art_tags = str_replace('|', ',', $art_tags);

		$art_tags = str_replace('、', ',', $art_tags);

		$art_tags = str_replace('，', ',', $art_tags);

		$art_tags = str_replace(',,', ',', $art_tags);

		if (substr($art_tags, -1) == ',') {

			$art_tags = substr($art_tags, 0, strlen($art_tags) - 1);

		}

	}

	

	if (empty($copy_from)) $copy_from = '本站原创';

	if (empty($copy_url)) $copy_url = $options['site_url'];

	

	if (empty($art_intro)) {

		alert('请输入内容摘要！');

	}

	

	$art_content = str_replace($options['site_root'].$options['upload_dir'].'/article/', '[upload_dir]', $art_content);

	

	$art_data = array(

		'cate_id' => $cate_id,

		'art_title' => $art_title,

		'art_tags' => $art_tags,

		'copy_from' => $copy_from,

		'copy_url' => $copy_url,

		'art_intro' => $art_intro,

		'art_content' => $art_content,

		'art_views' => $art_views,

		'art_ispay' => $art_ispay,

		'art_istop' => $art_istop,

		'art_isbest' => $art_isbest,

		'art_status' => $art_status,

		'art_ctime' => $art_time,

	);

	

	if ($action == 'saveadd') {

    	$query = $DB->query("SELECT art_id FROM $table WHERE art_title='$art_title'");

    	if ($DB->num_rows($query)) {

        	alert('您所添加的文章已存在！');

    	}

		

		$art_data['user_id'] = $myself['user_id'];

		$DB->insert($table, $art_data);

		$DB->query("UPDATE ".$DB->table('arccate')." SET cate_postcount=cate_postcount+1 WHERE cate_id='$cate_id'");

		

		alert('文章添加成功！', $fileurl.'?act=add&cate_id='.$cate_id);	

	} elseif ($action == 'saveedit') {

		$art_id = intval($_POST['art_id']);

		$where = array('art_id' => $art_id);

		unset($art_data['art_ctime']);

		

		$DB->update($table, $art_data, $where);

		$DB->query("UPDATE ".$DB->table('arccate')." SET cate_postcount=cate_postcount+1 WHERE cate_id='$cate_id'");

		

		alert('文章编辑成功！', $fileurl);

	}

}



/** del */

if ($action == 'del') {

	$art_ids = (array) ($_POST['art_id'] ? $_POST['art_id'] : $_GET['art_id']);

	

	$DB->delete($table, 'art_id IN ('.dimplode($art_ids).')');

	

	alert('文章删除成功！', $fileurl);

}



/** move */

if ($action == 'savemove') {

	$art_ids = (array) $_POST['art_id'];

	$cate_id = intval($_POST['cate_id']);

	if (empty($art_ids)) {

		alert('请选择要移动的内容！');

	}

	if ($cate_id <= 0) {

		alert('请选择分类！');

	} else {

		$cate = get_one_arccate($cate_id);

		if ($cate['cate_childcount'] > 0) {

			alert('指定的分类下有子分类，请选择子分类进行操作！');

		}

	}

	

	$DB->update($table, array('cate_id' => $cate_id), 'art_id IN ('.dimplode($art_ids).')');

	

	alert('文章移动成功！', $fileurl);

}



/** attr */

if ($action == 'saveattr') {

	$art_ids = (array) $_POST['art_id'];

	$art_ispay = intval($_POST['art_ispay']);

	$art_istop = intval($_POST['art_istop']);

	$art_isbest = intval($_POST['art_isbest']);

	$art_status = intval($_POST['art_status']);

	if (empty($art_ids)) {

		alert('请选择要设置的内容！');

	}

	

	$DB->update($table, array('art_ispay' => $art_ispay, 'art_istop' => $art_istop, 'art_isbest' => $art_isbest, 'art_status' => $art_status), 'art_id IN ('.dimplode($art_ids).')');

	

	alert('文章属性设置成功！', $fileurl);

}



smarty_output($tplfile);

?>