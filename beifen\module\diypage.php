<?php

if (!defined('IN_IWEBDIR')) exit('Access Denied');



$page_name = '';

$page_url = '?mod=diypage';

$tplfile = 'diypage.html';

$tpldir = 'other';

$table = $DB->table('pages');



/** 缓存设置 */

$smarty->compile_dir .= $tpldir;

$smarty->cache_dir .= $tpldir;

$smarty->cache_lifetime = $options['cache_time_other'] * 3600;



$page_id = intval($_GET['pid']);

$cache_id = $page_id;

		

if (!$smarty->isCached($tplfile, $cache_id)) {

	$page = get_one_page($page_id);

	if (!$page) {

		_404();

	}

	

	$smarty->assign('crumb', get_crumb('website').' &rsaquo; '.$page['page_name']);

	$smarty->assign('rssfeed', get_rssfeed('website'));

    $smarty->assign('page_id', $page_id);

	$smarty->assign('page', $page);

}



smarty_output($tplfile, $cache_id);

?>