<?php
if (!defined('IN_MEMBER')) exit('Access Denied');

if ($options['is_enabled_register'] == 'no') {
	$msg = <<<EOT
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>提示信息 - $options[site_name]</title>
<style type="text/css">
body {background: #f5f5f5;}
#msgbox {background: #fff; border: solid 3px #f1f1f1; font: normal 16px/30px normal; margin: 100px auto; padding: 100px 0; text-align: center; width: 500px;}
</style>
</head>

<body>
<div id="msgbox">抱歉，目前站点禁止新用户注册！<br /><a href="javascript:history.back();">[点击这里返回上一页]</a></div>
</body>
</html>
EOT;

	exit($msg);
}

if ($options['is_enabled_connect'] == 'yes') {
	if (empty($options['qq_appid']) || empty($options['qq_appkey'])) {
		alert('QQ一键登录未正确设置，请设置后再使用！');	
	}
} else {
	alert('未开启QQ一键登录或注册功能！');
}

$table = $DB->table('users');

$oper = trim($_GET['oper']);
if (empty($oper)) $oper = 'init';

require(CORE_PATH.'vendor/connect/oauth_qq.php');

$config = array('appid' => $options['qq_appid'], 'appkey' => $options['qq_appkey'], 'callback' => APP_URL.'?mod=connect&oper=callback');
$oauth = oauth_qq::get_instance($config);
//login
if ($oper == 'init') $oauth->login();

//callback
if ($oper == 'callback') {
	$oauth->callback();
	$oauth->get_openid();
	$user = $oauth->get_user_info();
	
	$open_id = $_SESSION['openid'];
	if (empty($open_id)) {
		alert('QQ一键登录授权失败，请采用普通方式注册和登录！', '?mod=login');
	} else {
		$row = $DB->fetch_one("SELECT user_id, user_pass, login_count FROM $table WHERE open_id='$open_id'");
		if ($row) {
			$ip_address = sprintf("%u", ip2long(get_client_ip()));
        	$login_count = $row['login_count'] + 1;
					
			$data = array(
				'login_time' => time(),
				'login_ip' => $ip_address,
				'login_count' => $login_count,
			);
			$where = array('user_id' => $row['user_id']);
			$DB->update($table, $data, $where);
			
			$authcode = authcode("$row[user_id]\t$row[user_pass]\t$login_count", "ENCODE", AUTH_KEY);
			$expire = time() + 3600 * 24;
			setcookie('authcode', $authcode, $expire, $options['site_root']);
				
			redirect('?mod=home');
		} else {
			/** check login  */
			$authcode = $_COOKIE['authcode'];
			$myself = check_user_login($authcode);
			if (!empty($myself)) {
				$DB->update($table, array('open_id' => $openid), array('user_id' => $myself['user_id']));
			} else {
				$tplfile = 'connect.html';
				
				$smarty->assign('nick_name', $user['nickname']);
				$smarty->assign('open_id', $open_id);
				smarty_output($tplfile);
			}
		}
	}
}
?>