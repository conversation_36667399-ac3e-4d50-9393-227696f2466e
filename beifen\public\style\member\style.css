@charset "utf-8";
/* global */
body, h1, h2, h3, h4, h5, h6, hr, p, blockquote, dl, dt, dd, ul, ol, li, pre, form, fieldset, legend, button, input, textarea, th, td {margin: 0; padding: 0;}
body, button, input, select, textarea {font: 12px/1.5 "Segoe UI", tahoma, arial, \5b8b\4f53, sans-serif;}
h1, h2, h3, h4, h5, h6 {font-size: 100%;}
address, cite, dfn, em, var {font-style: normal;}
code, kbd, pre, samp {font-family: courier new, courier, monospace;}
small {font-size: 12px;}
ul, ol {list-style: none;}
a {text-decoration: none;}
a:hover {text-decoration: underline;}
sup {vertical-align: text-top;}
sub {vertical-align: text-bottom;}
legend {color: #000;}
fieldset, img {border: 0;}
button, input, select, textarea {font-size: 100%;}
table {border-collapse: collapse; border-spacing: 0;}
/* clear float */
.clearfix:after {clear: both; content: '.'; display: block; font-size: 0; height: 1; visibility: hidden;}
*html .clearfix {zoom: 1;}
*:first-child+html .clearfix {zoom: 1;}

/* custom */
body {background: #fff; color: #555;}
body, button, input, select, textarea {color: #1c1c1c; font-family: Arial, '宋体';}
a {color: #1864c7; text-decoration: none;}
a:hover {color: #1c1c1c; text-decoration: underline;}
.blank10 {clear: both; display: block; font-size: 10px; height: 10px; line-height: 10px; width: 100%;}
.ipt {background: url(ipt.png) no-repeat; border: solid 1px #c9c9c9; font-size: 14px; padding: 4px;}
.btn {background: #5c97de; border: 0; color: #fff; height: 30px; padding: 0 8px;}
.sel {font-size: 13px; height: 25px;}
.over {background: #ffc;}
.tips {color: #999; padding-left: 10px;}
.textleft {text-align: left;}
/* wrapper */
.wrapper {margin: 0 auto;}
/* header */
.header {background: #5c97de; height: 70px;}
.logo {background: url(slogodigg.png); display: block; float: left; height: 70px; width: 200px;    background-size: 192px 70px;}
.uinfo {color: #fff; margin-left: 200px; padding-top:  25px;}
.uinfo a {color: #fff; margin: 0 8px;}
/* sidebar */
.sidebar {background: #5c97de; float: left; width: 180px;}
.sidebar ul h3 {background: #3d84da; border-bottom: solid 1px #4c89d3; color: #fff; padding: 8px;}
.sidebar ul li {border-bottom: solid 1px #4c89d3; margin: 0 10px; padding: 7px 0 7px 25px;}
.sidebar ul li a {color: #fff;}
/* icon */
.icon1 {background: url(icon.gif) 0px 0px no-repeat; padding-left: 20px;}
.icon2 {background: url(icon.gif) 0px -16px no-repeat; padding-left: 20px;}
.icon3 {background: url(icon.gif) 0px -48px no-repeat; padding-left: 20px;}
.icon4 {background: url(icon.gif) 0px -31px no-repeat; padding-left: 20px;}
.icon5 {background: url(icon.gif) 0px -76px no-repeat; padding-left: 20px;}
.icon6 {background: url(icon.gif) 0px -63px no-repeat; padding-left: 20px;}
.icon7 {background: url(icon.gif) 0px -92px no-repeat; padding-left: 20px;}
.icon8 {background: url(icon.gif) 0px -109px no-repeat; padding-left: 20px;}
.icon9 {background: url(icon.gif) 0px -125px no-repeat; padding-left: 20px;}
.icon10 {background: url(icon.gif) 0px -141px no-repeat; padding-left: 20px;}
/* content */
.body {margin-left:  190px;}
.title {border-bottom: solid 1px #e8e8e8; font: bold 24px notice; padding: 10px 0;}
.content {padding: 10px;}
/* notice */
.notice {background: #ffc; border: dashed 1px #f60; padding: 15px;}
.userinfo {margin-top: 15px;}
.userinfo h3 {font-size: 14px; padding-bottom: 10px;}
.userinfo li {font-size: 13px; padding: 6px 0;}
.userinfo li strong {color: #f30; font: normal 20px Arial;}
.homebox {padding: 10px;}
.homebox h2 {font: bold 14px normal;}
.homebox ol {padding: 10px 0;}
.homebox ol li {font-size: 13px; padding: 5px 0;}
.homebox ol li strong {color: #f00;}
/* listbox */
.listbox {}
.listbox table {}
.listbox table tr th {background: #f9f9f9; height: 30px;}
.listbox table tr td {border-bottom: solid 1px #ececec; height: 30px; text-align: center;}
.listbox table tr .ltext {text-align: left;}
.listbox table tr .data {font-size: 10px;}
.red {color: #f00;}
.gre {color: #080;}
.org {color: #f60;}
/* showpage */
.showpage {display: block; font-size: 13px; text-align: left; padding: 10px 0;}
.total_page, .jump_page {background: #5c97de; border: solid 1px #5c97de; color: #fff; display: block; float: left; margin-right: 5px; padding: 2px 8px;}
.first_page, .last_page, .prev_page, .next_page, .pages {background: #fff; border: 1px solid #e8e8e8; color: #333; display: block; float: left; margin-right: 5px; padding: 2px 8px; text-decoration: none;}
.current {background: #5c97de; color: #fff; display: block; float: left; margin-right: 5px; padding: 3px 8px;}
/* formbox */
.formbox {margin-left: 50px;}
.formbox ul {padding-bottom: 10px;}
.formbox li {padding: 10px;}
.formbox li strong {display: block; float: left; font: normal 13px normal; text-align: right; width: 120px;}
.formbox li p {color: #ccc; float: left; padding: 8px 0 0 90px;}
.formbox li select {font-size: 13px; height: 25px;}
/* claimbox */
.claimbox {}
.claimbox table {border: solid 1px #e8e8e8;}
.claimbox table tr th {background: #eff9ff; font-weight: normal; padding: 8px; text-align: left;}
.claimbox table tr td {background: #fefefe; line-height: 25px; padding: 8px;}
/* step */
.step {}
.step dt {border-bottom: solid 1px #e1e1e1; font: bold 20px normal; padding: 10px;}
.step dd {padding: 10px;}
/* footer */
.footer {background: url(fbg.gif) no-repeat; color: #555; height: 80px; padding: 10px; text-align: center;}