* {font-size: 12px;line-height: 1.5;}
body {font-size: 12px;line-height: 1.5;}
select,textarea{vertical-align:middle;background: url(input.png) repeat-x scroll 0px 0px;}
a:link { font-size: 12px; color: #000000; text-decoration: underline }
a:visited{ font-size: 12px; color: #000000; text-decoration: underline }
a:hover {font-size: 12px;color: red}
div,form,h1,h2,h3,h4,h5,h6{	margin: 0; padding:0;}
.m1{border-left: 1px solid #DFDFDB; border-top: 1px solid #DFDFDB; border-bottom: 1px solid #808080}
.m2{border-left: 1px solid #DFDFDB; border-bottom: 1px solid #808080; border-top: 1px solid #DFDFDB;border-right: 1px solid #DFDFDB;}
.m3{border-left: 1px solid #DFDFDB; border-top: 1px solid #DFDFDB;border-right: 1px solid #DFDFDB;}
.article{FONT-SIZE: 10pt; LINE-HEIGHT: 160%；table-layout:fixed;word-break:break-all}
.bn{color:#FFFFFF;font-size:0.1pt;line-height:50%}
.contents{font-size:1pt;color:#F7F6F8}
.nb{border: 1px solid #000000;height:18px}
.coolbg {border-right: 2px solid #ACACAC; border-bottom: 2px solid #ACACAC; background-color: #E6E6E6}
.ctfield{ padding: 3px; line-height: 150%}
.nndiv{ width: 175px; height:20px; margin: 0px;padding: 0px;word-break: break-all;overflow: hidden; }
.alltxt {
	border-width:1px;
	border-style:solid;
	border-color:#707070 #CECECE #CECECE #707070;
	padding:2px 4px;
	height:18px;
	line-height:18px;
	vertical-align:middle;
}
