<?php /* Smarty version Smarty-3.1.18, created on 2023-11-06 21:11:33
         compiled from "/www/wwwroot/www.8t.lv/templet/system/login.html" */ ?>
<?php /*%%SmartyHeaderCode:5218423656548e605558799-06765805%%*/if(!defined('SMARTY_DIR')) exit('no direct access allowed');
$_valid = $_smarty_tpl->decodeProperties(array (
  'file_dependency' => 
  array (
    '3971eac4426b4b2b016295230faf72ad2ff4777e' => 
    array (
      0 => '/www/wwwroot/www.8t.lv/templet/system/login.html',
      1 => 1418038320,
      2 => 'file',
    ),
  ),
  'nocache_hash' => '5218423656548e605558799-06765805',
  'function' => 
  array (
  ),
  'variables' => 
  array (
    'page_name' => 0,
    'fileurl' => 0,
  ),
  'has_nocache_code' => false,
  'version' => 'Smarty-3.1.18',
  'unifunc' => 'content_6548e605581a71_94968827',
),false); /*/%%SmartyHeaderCode%%*/?>
<?php if ($_valid && !is_callable('content_6548e605581a71_94968827')) {function content_6548e605581a71_94968827($_smarty_tpl) {?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

<head>

<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

<title><?php echo $_smarty_tpl->tpl_vars['page_name']->value;?>
</title>

<link href="../public/style/system/global.css" rel="stylesheet" type="text/css" />

<link href="../public/style/system/login.css" rel="stylesheet" type="text/css" />

<script type="text/javascript" src="../static/scripts/jquery.js"></script>

<script type="text/javascript" src="../static/scripts/admin.js"></script>

</head>



<body>

<div id="loginbox">

	<h2>登录入口</h2>

    <div>

    <form name="mform" method="post" action="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">

    	<div id="formbox">

		<table>

        	<tr>

				<th>电子邮件：</th>

				<td><input name="email" type="text" class="ipt" id="email" size="20" maxlength="25" autocomplete="off" /></td>

			</tr>

			<tr>

				<th>登录密码：</th>

				<td><input name="pass" type="password" class="ipt" id="pass" size="20" maxlength="25" /></td>

			</tr>

		</table>

        </div>

    	<div id="btnbox">

		<input name="act" type="hidden" id="act" value="login">

		<input name="submit" type="submit" class="btn" value="登 陆">&nbsp;

		<input name="reset" type="reset" class="btn" value="重 填">

    	</div>

    </form>

    </div>

</div>

</body>

</html><?php }} ?>
