<?php
//File name: category_57.php
//Creation time: 2023-11-07 01:09:37

if (!defined('IN_IWEBDIR')) exit('Access Denied');

$static_data = array(
	'cate_id' => '57',
	'root_id' => '3',
	'cate_mod' => 'website',
	'cate_name' => '硬件',
	'cate_dir' => 'yingjian',
	'cate_url' => '',
	'cate_isbest' => '0',
	'cate_keywords' => '硬件,硬件网址大全',
	'cate_description' => '硬件网址大全是为您精心挑选出国内外最优秀的硬件网站，并保持定期更新和检查，确保您以最安全、最方便的方式，找到您的需要；硬件网址大全下设分类：硬件资讯，服务器资讯，硬件评测，硬件论坛，驱动程序，硬件测试，硬件相关',
	'cate_arrparentid' => '0,3',
	'cate_arrchildid' => '57',
	'cate_childcount' => '0',
	'cate_postcount' => '125',
);
?>