<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');


echo 232323;
//error_reporting(E_ALL);
$page_name = '文章资讯';
$page_url = '?mod=article';
$tplfile = 'article.html';
$tpldir = 'article';
$table = $DB->table('articles');

/** 缓存设置 */
$smarty->compile_dir .= $tpldir;
$smarty->cache_dir .= $tpldir;
$smarty->cache_lifetime = $options['cache_time_list'] * 3600;

$pagesize = 10;
$curpage = intval($_GET['page']);
if ($curpage > 1) {
	$start = ($curpage - 1) * $pagesize;
} else {
	$start = 0;
	$curpage = 1;
}
$pagestr = ($curpage > 0) ? ' - 第'.$curpage.'页': '';
		
$cate_id = intval($_GET['cid']);
$cache_id = $cate_id.'-'.$curpage;

$page_url .= '&cid='.$cate_id;

if (!$smarty->isCached($tplfile, $cache_id)) {	
	$where = "a.art_status=3";
	if ($cate_id > 0) {
		$crow = get_one_category($cate_id);	
		$cate_id = $crow['cate_id'];
		$cate_name = $crow['cate_name'];	
		if ($crow['cate_childcount'] > 0) {
			$where .= " AND a.cate_id IN (".$crow['cate_arrchildid'].")";
			$categories = get_arccate($crow['cate_id']);
		} else {
			$where .= " AND a.cate_id='".$crow['cate_id']."'";
			$categories = get_arccate($crow['root_id']);
		}
		
		$crumb = get_article_crumb($crow['cate_mod'], $crow['cate_id'].','.$crow['cate_arrparentid']);
		$rssfeed = get_rssfeed($crow['cate_mod'], $crow['cate_id']);
	} else {
		$cate_id = 0;
		$cate_name = '';
		$categories = get_arccate();
		$crumb = get_article_crumb('article');
		$rssfeed = get_rssfeed('article');
	}
	
	$articles = get_article_list($where, 'ctime', 'DESC', $start, $pagesize);
	$total = $DB->get_count($table.' a', $where);
	$showpage = showpage($page_url, $total, $curpage, $pagesize);
	
	$smarty->assign('page_name', $page_name);
	$smarty->assign('pagestr', $pagestr);
	$smarty->assign('crumb', $crumb);
	$smarty->assign('rssfeed', $rssfeed);
	$smarty->assign('cate_id', $cate_id);
	$smarty->assign('cate_name', $cate_name);
	$smarty->assign('categories', $categories);
	$smarty->assign('total', $total);
	$smarty->assign('articles', $articles);
	$smarty->assign('showpage', $showpage);
}

smarty_output($tplfile, $cache_id);
?>