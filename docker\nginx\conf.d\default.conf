server {
    listen 80;
    server_name localhost;
    root /var/www/html/public;
    index index.php index.html index.htm;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;

    # Handle frontend routes
    location / {
        try_files $uri $uri/ @frontend;
    }

    # API routes
    location /api {
        try_files $uri $uri/ /index.php?$query_string;
        
        # Rate limiting for API
        limit_req zone=api burst=20 nodelay;
    }

    # Admin routes
    location /admin {
        try_files $uri $uri/ /index.php?$query_string;
        
        # Additional security for admin
        limit_req zone=login burst=5 nodelay;
    }

    # PHP-FPM handling
    location ~ \.php$ {
        fastcgi_pass php:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        
        # Security
        fastcgi_hide_header X-Powered-By;
        fastcgi_read_timeout 300;
    }

    # Frontend fallback
    location @frontend {
        proxy_pass http://frontend:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Static files caching
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|txt|tar|gz)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Security: Deny access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ /(config|storage|vendor|node_modules) {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Favicon
    location = /favicon.ico {
        access_log off;
        log_not_found off;
    }

    # Robots.txt
    location = /robots.txt {
        access_log off;
        log_not_found off;
    }

    # Error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}

# HTTPS configuration (uncomment when SSL certificates are available)
# server {
#     listen 443 ssl http2;
#     server_name localhost;
#     root /var/www/html/public;
#     index index.php index.html index.htm;
# 
#     # SSL configuration
#     ssl_certificate /etc/nginx/ssl/cert.pem;
#     ssl_certificate_key /etc/nginx/ssl/key.pem;
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;
# 
#     # HSTS
#     add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
# 
#     # Same location blocks as HTTP configuration
#     # ... (copy from above)
# }

# Redirect HTTP to HTTPS (uncomment when SSL is enabled)
# server {
#     listen 80;
#     server_name localhost;
#     return 301 https://$server_name$request_uri;
# }
