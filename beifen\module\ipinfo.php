<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$page_name = 'IP地址查询';
$page_url = '?mod=ipinfo';
$tplfile = 'ipinfo.html';
$tpldir = 'ipinfo';

/** 缓存设置 */
$smarty->compile_dir .= $tpldir;
$smarty->cache_dir .= $tpldir;
$smarty->cache_lifetime = $options['cache_time_info'] * 3600;

$web_ip = trim($_GET['ip']);
$cache_id = $web_ip;
		
if (!$smarty->isCached($tplfile, $cache_id)) {
	$smarty->assign('page_name', $page_name);
	$smarty->assign('crumb', get_crumb().' &rsaquo; '.$page_name);
	$smarty->assign('rssfeed', get_rssfeed('website'));
	
	require(CORE_PATH.'include/location.php');
	$filepath = ROOT_PATH.'data/ipdata/qqwry.dat';
	if (!is_file($filepath)) {
		exit("错误：丢失IP数据库文件“qqwry.dat”，路径为“".ROOT_PATH."data/ipdata/”。");
	}
	
	$ip = new ip_location($filepath);
	if (!empty($web_ip)) {
		$loc = $ip->get_location($web_ip);
	} else {
		$client_ip = get_client_ip();
		$loc = $ip->get_location($client_ip);
	}
	$location = $loc['ip'].' - '.$loc['country'].' '.$loc['area'];
	$location = iconv('gb2312', 'utf-8', $location);
	$smarty->assign('web_ip', $web_ip);
	$smarty->assign('location', $location);
}
		
smarty_output($tplfile, $cache_id);
?>