<?php
if (!defined('IN_MEMBER')) exit('Access Denied');

$page_name = '个人资料';
$page_url = '?mod=profile';
$tplfile = 'profile.html';
$table = $DB->table('users');

if (!$smarty->isCached($tplfile)) {
	$smarty->assign('page_name', $page_name);
	$smarty->assign('crumb', get_crumb().' &rsaquo; '.$page_name);
	
	if ($_POST['do'] == 'save') {
		$nick_name = trim($_POST['nick_name']);
		$user_qq = trim($_POST['user_qq']);
		
		$data = array(
			'nick_name' => $nick_name,
			'user_qq' => $user_qq,
		);
		
		$where = array(
			'user_id' => $myself['user_id'],
		);
		
		$DB->update($table, $data, $where);
		alert('个人资料修改成功！', $page_url);
	}
}

smarty_output($tplfile);
?>