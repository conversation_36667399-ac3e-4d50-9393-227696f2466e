<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$page_name = '文章详细';
$page_url = '?mod=artinfo';
$tplfile = 'artinfo.html';
$tpldir = 'artinfo';
$table = $DB->table('articles');

/** 缓存设置 */
$smarty->compile_dir .= $tpldir;
$smarty->cache_dir .= $tpldir;
$smarty->cache_lifetime = $options['cache_time_info'] * 3600;

$art_id = intval($_GET['aid']);
$cache_id = $art_id;
		
		
if (!$smarty->isCached($tplfile, $cache_id)) {
	$where = "a.art_status=3 AND a.art_id='$art_id'";
	$art = get_one_article($where);
	$crow = get_one_arccate($art['cate_id']);	
	
	if (!$art) {
		_404();
	}
	
	$DB->query("UPDATE $table SET art_views=art_views+1 WHERE art_id='".$art['art_id']."' LIMIT 1");
	
	$art['art_content'] = str_replace('[upload_dir]', $options['site_root'].$options['upload_dir'].'/article/', $art['art_content']);
	$art['art_ctime'] = date('Y-m-d', $art['art_ctime']);
	$art['cate_name'] = $crow['cate_name'];
	
	$smarty->assign('page_name', $page_name);
	$smarty->assign('crumb', get_article_crumb($art['cate_mod'], $crow['cate_id'].','.$crow['cate_arrparentid']).' &rsaquo; '.$page_name);
	$smarty->assign('rssfeed', get_rssfeed($art['cate_mod'], $art['cate_id']));
	$smarty->assign('art', $art);
	$smarty->assign('prev', get_prev_article($art['art_id']));
	$smarty->assign('next', get_next_article($art['art_id']));
	$smarty->assign('related_article', get_articles($art['cate_id'], 8, false, 'ctime'));
}
		
smarty_output($tplfile, $cache_id);
?>