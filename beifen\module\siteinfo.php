<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$page_name = '站点详细';
$page_url = '?mod=siteinfo';
$tplfile = 'siteinfo.html';
$tpldir = 'siteinfo';
$table = $DB->table('webdata');

/** 缓存设置 */
$smarty->compile_dir .= $tpldir;
$smarty->cache_dir .= $tpldir;
$smarty->cache_lifetime = $options['cache_time_info'] * 3600;

$web_id = intval($_GET['wid']);
$cache_id = $web_id;
		
if (!$smarty->isCached($tplfile, $cache_id)) {
	$where = "w.web_status>=2 AND w.web_id='$web_id'";
	$web = get_one_website($where);
	if (!$web) {
unset($web);
		redirect('/');
	}
	
	$DB->query("UPDATE $table SET web_views=web_views+1 WHERE web_id='".$web['web_id']."' LIMIT 1");
	
	$web['web_furl'] = format_url($web['web_url']);
	$web['web_pic'] = get_webthumb($web['web_url'], $web['web_pic']);
	$web['web_ip'] = long2ip($web['web_ip']);
	$web['web_ctime'] = date('Y-m-d H:i:s', $web['web_ctime']);
	$web['web_ltime'] = date('Y-m-d H:i:s',strtotime("-2 hours "));
	$web['web_xtime'] = date('Y-m-d H:i:s', $web['web_utime']);
	$web['ip_link'] = get_ipinfo_url($web['web_ip']);

	$tags = get_format_tags($web['web_tags']); /** tags */
	$web['web_description'] = strip_tags($web['web_intro'],'');
	
	$smarty->assign('page_name', $page_name);
	$smarty->assign('crumb', get_crumb($web['cate_mod'], $web['cate_id'].','.$web['cate_arrparentid']));
	$smarty->assign('rssfeed', get_rssfeed($web['cate_mod'], $web['cate_id']));
    $smarty->assign('web', $web);
	$smarty->assign('tags', $tags);
	$smarty->assign('related_website', get_websites($web['cate_id'], 10, false, false, 'ctime'));
}
		
smarty_output($tplfile, $cache_id);
?>