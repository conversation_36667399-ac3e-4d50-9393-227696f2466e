<?php
//File name: category_24.php
//Creation time: 2023-11-07 01:09:36

if (!defined('IN_IWEBDIR')) exit('Access Denied');

$static_data = array(
	'cate_id' => '24',
	'root_id' => '2',
	'cate_mod' => 'website',
	'cate_name' => '新闻',
	'cate_dir' => 'xinwen',
	'cate_url' => '',
	'cate_isbest' => '1',
	'cate_keywords' => '新闻网站,英语新闻网站,新闻网站大全,新闻网站排名,国外新闻网站',
	'cate_description' => '新闻网址大全为您精心挑选出国内外最优秀的新闻网站，并保持定期更新和检查，确保您以最安全、最方便的方式，找到您的需要；新闻网址大全下有英语新闻网站,新闻网站大全,新闻网站排名,国外新闻网站。',
	'cate_arrparentid' => '0,2',
	'cate_arrchildid' => '24',
	'cate_childcount' => '0',
	'cate_postcount' => '541',
);
?>