<?php
define('IN_MEMBER', true);

define('ROOT_PATH', str_replace("\\", '/', substr(__FILE__, 0, strrpos(dirname(__FILE__), DIRECTORY_SEPARATOR))).'/');
define('CORE_PATH', ROOT_PATH.'source/');
define('MOD_PATH', ROOT_PATH.'member/module/');

require(CORE_PATH.'init.php');
require(CORE_PATH.'module/prelink.php');
require(CORE_PATH.'module/rewrite.php');

$module = $_GET['mod'] ? $_GET['mod'] : $_POST['mod'];
if (!isset($module)) $module = 'home';

require(CORE_PATH.'module/user.php');
require(MOD_PATH.'common.php');

$verify_module = array('home', 'logout', 'verify', 'website', 'claim', 'article', 'weblink', 'favorite', 'invite', 'consume', 'payment', 'profile', 'editpwd', 'upload', 'ajaxget');
if (in_array($module, $verify_module)) {
	/** check login  */
	$authcode = $_COOKIE['authcode'];
	$myself = check_user_login($authcode);
	if (empty($myself)) {
		alert('您还未登录或无权限！', '?mod=login');
	} else {
		$smarty->assign('myself', $myself);
		
		if ($myself['user_status'] == 0) {
			$msg = <<<EOT
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>提示信息 - $options[site_name]</title>
<style type="text/css">
body {background: #f5f5f5;}
#msgbox {background: #fff; border: solid 3px #f1f1f1; font: normal 16px/30px normal; margin: 100px auto; padding: 100px 0; text-align: center; width: 500px;}
</style>
</head>

<body>
<div id="msgbox">你还未通过E-mail验证！<br /><a href="?mod=verify">[点击发送验证邮件]</a></div>
</body>
</html>
EOT;
			exit($msg);
		}
	}	
}
	
$modpath = MOD_PATH.$module.'.php';
if (is_file($modpath)) {
	require($modpath);
} else {
	_404();
}
?>