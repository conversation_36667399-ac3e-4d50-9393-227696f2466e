<?php /* Smarty version Smarty-3.1.18, created on 2023-01-08 19:22:49
         compiled from "/www/wwwroot/digg58.com/templet/default/archives.html" */ ?>
<?php /*%%SmartyHeaderCode:77809667863baa7893d9a18-61780796%%*/if(!defined('SMARTY_DIR')) exit('no direct access allowed');
$_valid = $_smarty_tpl->decodeProperties(array (
  'file_dependency' => 
  array (
    'ed6982a343ef27af32d328d7c7afc4c2b9d845d2' => 
    array (
      0 => '/www/wwwroot/digg58.com/templet/default/archives.html',
      1 => 1647173827,
      2 => 'file',
    ),
  ),
  'nocache_hash' => '77809667863baa7893d9a18-61780796',
  'function' => 
  array (
  ),
  'variables' => 
  array (
    'date' => 0,
    'datestr' => 0,
    'page_name' => 0,
    'site_name' => 0,
    'total' => 0,
    'css_path' => 0,
    'site_root' => 0,
    'option' => 0,
    'js_path' => 0,
    'crumb' => 0,
    'rssfeed' => 0,
    'year' => 0,
    'arr' => 0,
    'item' => 0,
    'month' => 0,
    'websites' => 0,
    'w' => 0,
    'img_path' => 0,
    'showpage' => 0,
    'new' => 0,
    'best' => 0,
    'cate' => 0,
    'rel' => 0,
  ),
  'has_nocache_code' => false,
  'version' => 'Smarty-3.1.18',
  'unifunc' => 'content_63baa789541891_06590857',
),false); /*/%%SmartyHeaderCode%%*/?>
<?php if ($_valid && !is_callable('content_63baa789541891_06590857')) {function content_63baa789541891_06590857($_smarty_tpl) {?><!DOCTYPE HTML>

<html>

<head>

<title><?php if (!empty($_smarty_tpl->tpl_vars['date']->value)) {?><?php echo $_smarty_tpl->tpl_vars['datestr']->value;?>
<?php }?><?php echo $_smarty_tpl->tpl_vars['page_name']->value;?>
 - <?php echo $_smarty_tpl->tpl_vars['site_name']->value;?>
</title>

<meta charset="utf-8">

<meta name="Keywords" content="网站收录存档，网站目录存档，网站数据归档" />

<meta name="Description" content="<?php if (!empty($_smarty_tpl->tpl_vars['date']->value)) {?><?php echo $_smarty_tpl->tpl_vars['site_name']->value;?>
<?php echo $_smarty_tpl->tpl_vars['datestr']->value;?>
共收录网站<?php echo $_smarty_tpl->tpl_vars['total']->value;?>
个。<?php } else { ?>可根据年份、月份来查询，让你及时了解某一时间段内网站的收录情况。<?php }?>" />

<link href="<?php echo $_smarty_tpl->tpl_vars['css_path']->value;?>
default/style.css" rel="stylesheet" type="text/css" />

<script type="text/javascript">var $root = '<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
'; var $linktype = '<?php echo $_smarty_tpl->tpl_vars['option']->value['link_struct'];?>
';</script>

<script type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['js_path']->value;?>
jquery.min.js"></script>

<script type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['js_path']->value;?>
common.js"></script>
<?php echo $_smarty_tpl->getSubTemplate ("header-public.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>

</head>



<body>

<?php echo $_smarty_tpl->getSubTemplate ("header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>


<div class="wrapper">

    <div class="crumb"><?php echo $_smarty_tpl->tpl_vars['crumb']->value;?>
&nbsp;<?php echo $_smarty_tpl->tpl_vars['rssfeed']->value;?>
</div>

    <div class="mainbox">

        <div class="mainbox-left">

            <div class="clearfix arcbox allcate">

                <ul class="arcbox-list">

                    <?php  $_smarty_tpl->tpl_vars['arr'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['arr']->_loop = false;
 $_smarty_tpl->tpl_vars['year'] = new Smarty_Variable;
 $_from = get_archives(); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['arr']->key => $_smarty_tpl->tpl_vars['arr']->value) {
$_smarty_tpl->tpl_vars['arr']->_loop = true;
 $_smarty_tpl->tpl_vars['year']->value = $_smarty_tpl->tpl_vars['arr']->key;
?>

                    <li><strong><?php echo $_smarty_tpl->tpl_vars['year']->value;?>
年</strong><?php  $_smarty_tpl->tpl_vars['item'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['item']->_loop = false;
 $_smarty_tpl->tpl_vars['month'] = new Smarty_Variable;
 $_from = $_smarty_tpl->tpl_vars['arr']->value; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['item']->key => $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->_loop = true;
 $_smarty_tpl->tpl_vars['month']->value = $_smarty_tpl->tpl_vars['item']->key;
?><a href="<?php echo $_smarty_tpl->tpl_vars['item']->value['arc_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['year']->value;?>
年<?php echo $_smarty_tpl->tpl_vars['month']->value;?>
月共有<?php echo $_smarty_tpl->tpl_vars['item']->value['site_count'];?>
个站点"><?php echo $_smarty_tpl->tpl_vars['month']->value;?>
月</a><?php } ?></li>

                    <?php } ?>

                </ul>

            </div>

            <div class="blank10"></div>

            <div class="clearfix listbox allcate">

                <h3 class="listbox-title"><?php if (!empty($_smarty_tpl->tpl_vars['date']->value)) {?><?php echo $_smarty_tpl->tpl_vars['datestr']->value;?>
<?php }?><?php echo $_smarty_tpl->tpl_vars['page_name']->value;?>
</h3>

                <ul class="sitelist">

                    <?php  $_smarty_tpl->tpl_vars['w'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['w']->_loop = false;
 $_from = $_smarty_tpl->tpl_vars['websites']->value; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['w']->key => $_smarty_tpl->tpl_vars['w']->value) {
$_smarty_tpl->tpl_vars['w']->_loop = true;
?>

                    <li><a href="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_link'];?>
" target="_blank"><img src="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_pic'];?>
" width="120" height="95" alt="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_name'];?>
" class="thumb" /></a><div class="info"><h3><a href="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_name'];?>
" target="_blank"><?php echo $_smarty_tpl->tpl_vars['w']->value['web_name'];?>
</a> <?php if ($_smarty_tpl->tpl_vars['w']->value['web_ispay']==1) {?><img src="<?php echo $_smarty_tpl->tpl_vars['img_path']->value;?>
attr/audit.gif" border="0"><?php }?> <?php if ($_smarty_tpl->tpl_vars['w']->value['web_istop']==1) {?><img src="<?php echo $_smarty_tpl->tpl_vars['img_path']->value;?>
attr/top.gif" border="0"><?php }?> <?php if ($_smarty_tpl->tpl_vars['w']->value['web_isbest']==1) {?><img src="<?php echo $_smarty_tpl->tpl_vars['img_path']->value;?>
attr/best.gif" border="0"><?php }?></h3><p><?php echo $_smarty_tpl->tpl_vars['w']->value['web_intro'];?>
</p><cite><?php echo $_smarty_tpl->tpl_vars['w']->value['web_ctime'];?>
</cite></div></li>

                    <?php }
if (!$_smarty_tpl->tpl_vars['w']->_loop) {
?>

                    <li>该目录下无任何内容！</li>

                    <?php } ?>

                </ul>

                <div class="clearfix showpage"><?php echo $_smarty_tpl->tpl_vars['showpage']->value;?>
</div>

            </div>

        </div>

        <div class="mainbox-right">

            <div class="newbox">

                <div class="newbox-title">最新收录</div>

                <ul class="newbox-list">

                    <?php  $_smarty_tpl->tpl_vars['new'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['new']->_loop = false;
 $_from = get_websites(0,13,false,false,'ctime'); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['new']->key => $_smarty_tpl->tpl_vars['new']->value) {
$_smarty_tpl->tpl_vars['new']->_loop = true;
?>

                    <li><span><?php echo $_smarty_tpl->tpl_vars['new']->value['web_ctime'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['new']->value['web_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['new']->value['web_name'];?>
</a></li>

                    <?php } ?>

                </ul>

            </div>
            
            <div class="blank10"></div>
            <div class="newbox">

                <div class="newbox-title">推荐站点</div>

                <ul class="newbox-list">

                    <?php  $_smarty_tpl->tpl_vars['best'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['best']->_loop = false;
 $_from = get_websites(0,13,false,true); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['best']->key => $_smarty_tpl->tpl_vars['best']->value) {
$_smarty_tpl->tpl_vars['best']->_loop = true;
?>

                    <li><span><?php echo $_smarty_tpl->tpl_vars['best']->value['web_ctime'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['best']->value['web_name'];?>
</a></li>

                    <?php } ?>

                </ul>

            </div>
            
            <div class="blank10"></div>
            <div class="newbox">

                <div class="newbox-title">随机站点</div>

                <ul class="newbox-list">

                    <?php  $_smarty_tpl->tpl_vars['rel'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['rel']->_loop = false;
 $_from = get_websites_lunbo($_smarty_tpl->tpl_vars['cate']->value['cate_id'],14); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['rel']->key => $_smarty_tpl->tpl_vars['rel']->value) {
$_smarty_tpl->tpl_vars['rel']->_loop = true;
?>
                        <li><a href="<?php echo $_smarty_tpl->tpl_vars['rel']->value['web_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['rel']->value['web_name'];?>
</a></li>
                    <?php } ?>

                </ul>

            </div>

        </div>

    </div>

</div>

<?php echo $_smarty_tpl->getSubTemplate ("footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>


</body>

</html><?php }} ?>
