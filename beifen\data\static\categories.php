<?php
//File name: categories.php
//Creation time: 2023-11-07 01:09:37

if (!defined('IN_IWEBDIR')) exit('Access Denied');

$static_data = array(
	'1' => array(
		'cate_id' => '1',
		'root_id' => '0',
		'cate_mod' => 'website',
		'cate_name' => '娱乐休闲',
		'cate_dir' => 'yule',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '娱乐休闲,娱乐休闲网,娱乐休闲中心',
		'cate_description' => '娱乐休闲网站大全 - 我们为您搜集休闲娱乐会所、休闲娱乐项目、室内休闲娱乐项目、休闲娱乐方式、休闲娱乐产品等等，找网站就来点我网站目录。',
		'cate_arrparentid' => '0',
		'cate_arrchildid' => '1,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,152,161,168,174',
		'cate_childcount' => '22',
		'cate_postcount' => '2537'
	),
	'2' => array(
		'cate_id' => '2',
		'root_id' => '0',
		'cate_mod' => 'website',
		'cate_name' => '生活资讯',
		'cate_dir' => 'shenghuo',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '生活资讯,生活资讯网,生活资讯网站',
		'cate_description' => '生活资讯网站大全 - 我们为您搜集生活资讯频道、生活资讯节目、生活资讯杂志、时尚生活资讯、生活资讯网站等等，找网站就来点我网站目录。',
		'cate_arrparentid' => '0',
		'cate_arrchildid' => '2,24,25,26,27,28,29,30,31,32,33,34,35,36,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,105,106,107,109,111,112,113,114,115,116,117,159,163,167',
		'cate_childcount' => '45',
		'cate_postcount' => '4630'
	),
	'3' => array(
		'cate_id' => '3',
		'root_id' => '0',
		'cate_mod' => 'website',
		'cate_name' => '电脑网络',
		'cate_dir' => 'wangluo',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '电脑,网络,互联网,it,网络应用,网络技术',
		'cate_description' => '电脑网络网站大全 - 我们为您搜集IT机构、门户、IT综合、硬件、数码、程序、编程、网络通讯、域名、主机、设计、建站、网赚等等，找网站就来点我网站目录。',
		'cate_arrparentid' => '0',
		'cate_arrchildid' => '3,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,118,129,145,146,149,150,154,156,158,164,166,169,175',
		'cate_childcount' => '37',
		'cate_postcount' => '2956'
	),
	'4' => array(
		'cate_id' => '4',
		'root_id' => '0',
		'cate_mod' => 'website',
		'cate_name' => '教育学习',
		'cate_dir' => 'study',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '教育,教学,教导,学校,学科,网络教学,课件',
		'cate_description' => '教育学习网站大全 - 我们为您搜集教育、培训、教育机构、幼儿教育、基础教育、成人教育、职业教育、特殊教育、远程教育、教育综合等等，找网站就来点我网站目录。',
		'cate_arrparentid' => '0',
		'cate_arrchildid' => '4,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,119,170',
		'cate_childcount' => '18',
		'cate_postcount' => '1889'
	),
	'5' => array(
		'cate_id' => '5',
		'root_id' => '0',
		'cate_mod' => 'website',
		'cate_name' => '综合其他',
		'cate_dir' => 'zonghe',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '交通网站提交,网站收录,网站目录,农业网址提交,中文网站目录',
		'cate_description' => '综合其他酷站目录,优秀网站目录分享网站价值,提供中文网站，欢迎您登录提交网站，找网站就来点我网站目录。',
		'cate_arrparentid' => '0',
		'cate_arrchildid' => '5,96,97,98,99,100,102,103,104,120,121,122,123,125,126,127,165,128,153,162',
		'cate_childcount' => '19',
		'cate_postcount' => '1680'
	),
	'6' => array(
		'cate_id' => '6',
		'root_id' => '1',
		'cate_mod' => 'website',
		'cate_name' => '视频',
		'cate_dir' => 'shipin',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '电影,视频,电影网,网络电视,在线影院',
		'cate_description' => '为您提供电视剧、电影、动漫、综艺节目网站，推荐和排行榜并可在线免费观看百度影音伦理电影、动作片、喜剧片、爱情片、搞笑片高清下载等,高清视频电影网站尽在点我网站目录。',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '6',
		'cate_childcount' => '0',
		'cate_postcount' => '217'
	),
	'7' => array(
		'cate_id' => '7',
		'root_id' => '1',
		'cate_mod' => 'website',
		'cate_name' => '音乐',
		'cate_dir' => 'yinyue',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '音乐,流行歌曲,好听的歌,在线听歌,歌曲',
		'cate_description' => '音乐网站大全 - 我们为您搜集流行歌曲、好听的歌、mp3下载、mp3歌曲、在线听歌、网络歌曲、非主流音乐等等，找网站就来点我网站目录。',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '7',
		'cate_childcount' => '0',
		'cate_postcount' => '118'
	),
	'8' => array(
		'cate_id' => '8',
		'root_id' => '1',
		'cate_mod' => 'website',
		'cate_name' => '游戏',
		'cate_dir' => 'youxi',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '游戏,网游,单机游戏,网络游戏',
		'cate_description' => '游戏网站,提供中文游戏网站、游戏资讯网站、游戏补丁网站等;保证所有游戏都安全无毒;是广大游戏爱好者最信赖的游戏网站大全。',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '8',
		'cate_childcount' => '0',
		'cate_postcount' => '213'
	),
	'9' => array(
		'cate_id' => '9',
		'root_id' => '1',
		'cate_mod' => 'website',
		'cate_name' => '动漫',
		'cate_dir' => 'dongman',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '动漫,卡通,动漫网,童话,童话故事',
		'cate_description' => '动漫网站大全是为您精心挑选出国内外最优秀的动漫网站，并保持定期更新和检查，确保您以最安全、最方便的方式，找到您的需要，动漫网址大全',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '9',
		'cate_childcount' => '0',
		'cate_postcount' => '170'
	),
	'10' => array(
		'cate_id' => '10',
		'root_id' => '1',
		'cate_mod' => 'website',
		'cate_name' => '小说',
		'cate_dir' => 'xiaoshuo',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '小说,免费小说,热门小说',
		'cate_description' => '小说网站，最全的小说网站大全，热门小说站点排行榜，小说分类大全，小说类贴吧，免费小说精选，提供给您最全面的小说阅读',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '10',
		'cate_childcount' => '0',
		'cate_postcount' => '230'
	),
	'11' => array(
		'cate_id' => '11',
		'root_id' => '1',
		'cate_mod' => 'website',
		'cate_name' => '搞笑',
		'cate_dir' => 'gaoxiao',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '搞笑,嘻嘻哈哈,腾讯笑话,来福岛,八目妖,糗事百科,搞搞吧',
		'cate_description' => '搞笑网站大全，最热的爆笑笑话、搞笑图片、动态图、冷笑话、糗事笑话、成人笑话、经典笑话、内涵段子等笑话网址大全',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '11',
		'cate_childcount' => '0',
		'cate_postcount' => '104'
	),
	'12' => array(
		'cate_id' => '12',
		'root_id' => '1',
		'cate_mod' => 'website',
		'cate_name' => '美图',
		'cate_dir' => 'meitu',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '美图,唯美图片,美图网站,美图网站大全',
		'cate_description' => '美图网站大全 - 我们为您搜集欧美图片、唯美图片、欧美图库、美图看看、优美图片、精美图片、美图录等等，找网站就来点我网站目录。',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '12',
		'cate_childcount' => '0',
		'cate_postcount' => '126'
	),
	'13' => array(
		'cate_id' => '13',
		'root_id' => '1',
		'cate_mod' => 'website',
		'cate_name' => '星座',
		'cate_dir' => 'xingzuo',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '星座网站,星座查询,十二星座,星座配对',
		'cate_description' => '星座网站大全 - 我们为您搜集今日运势查询、星座运势2015年、星座运势、美国神婆、神婆网、十二星座、星座查询、星座配对、星座爱情、星座排行、星座之最、星座性格分析等等，找网站就来点我网站目录。',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '13',
		'cate_childcount' => '0',
		'cate_postcount' => '117'
	),
	'14' => array(
		'cate_id' => '14',
		'root_id' => '1',
		'cate_mod' => 'website',
		'cate_name' => 'QQ',
		'cate_dir' => 'qq',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => 'qq网站,qq技术网站,qq头像网站,qq个性网站',
		'cate_description' => 'QQ网站大全 - 我们为您搜集qq网站、qq技术网站、qq头像网站、qq个性网站、qq团购网站等等，找网站就来点我网站目录。',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '14',
		'cate_childcount' => '0',
		'cate_postcount' => '97'
	),
	'15' => array(
		'cate_id' => '15',
		'root_id' => '1',
		'cate_mod' => 'website',
		'cate_name' => '交友',
		'cate_dir' => 'jiaoyou',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '交友网站,同城交友网站,免费交友网站,交友网站大全',
		'cate_description' => '交友网站是基于网络平台的广泛性、互通性、娱乐性、经济性、安全性等优点，于本世纪初出现在网络交流方式中的互动型服务网站。按照类型，可以简单的将它们分为婚恋交友类网站和社交交友类网站两种。',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '15',
		'cate_childcount' => '0',
		'cate_postcount' => '114'
	),
	'16' => array(
		'cate_id' => '16',
		'root_id' => '1',
		'cate_mod' => 'website',
		'cate_name' => '社区',
		'cate_dir' => 'shequ',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '社交网站,sns社交网站,社交平台,社交网',
		'cate_description' => '全称Social Network Site，即“社交网站”或“社交网”。注意要与“社区网站”区分，两者有本质区别。社会性网络（Social Networking）是指个人之间的关系网络，这种基于社会网络关系系统思想的网站就是社会性网络网站(SNS网站)。SNS的全称也可以是Social Networking Services，即社会性网络服务，专指旨在帮助人们建立社会性网络的互联网应用服务。',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '16',
		'cate_childcount' => '0',
		'cate_postcount' => '160'
	),
	'17' => array(
		'cate_id' => '17',
		'root_id' => '1',
		'cate_mod' => 'website',
		'cate_name' => '体育',
		'cate_dir' => 'tiyu',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '体育,体育网站大全,体育网站',
		'cate_description' => '体育（physical education，缩写PE或P.E.），是一种复杂的社会文化现象，它以身体与智力活动为基本手段，根据人体生长发育、技能形成和机能提高等规律，达到促进全面发育、提高身体素质与全面教育水平、增强体质与提高运动能力、改善生活方式与提高生活质量的一种有意识、有目的、有组织的社会活动。',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '17',
		'cate_childcount' => '0',
		'cate_postcount' => '113'
	),
	'18' => array(
		'cate_id' => '18',
		'root_id' => '1',
		'cate_mod' => 'website',
		'cate_name' => 'NBA',
		'cate_dir' => 'nba',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => 'nba录像,NBA直播,nba中文网,NBA',
		'cate_description' => 'NBA是美国男子职业篮球联赛（National Basketball Association）的简称，于1946年6月6日在纽约成立，是由北美三十支队伍组成的男子职业篮球联盟，美国四大职业体育联盟之一。其中诞生了迈克尔·乔丹、科比·布莱恩特、勒布朗·詹姆斯等球星，是世界上水平最高的篮球赛事。',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '18',
		'cate_childcount' => '0',
		'cate_postcount' => '67'
	),
	'19' => array(
		'cate_id' => '19',
		'root_id' => '1',
		'cate_mod' => 'website',
		'cate_name' => '摄影',
		'cate_dir' => 'sheying',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '摄影,婚纱摄影,写真摄影,儿童摄影,服装摄影',
		'cate_description' => '摄影一词是源于希腊语 φῶς phos（光线）和 γραφι graphis（绘画、绘图）或γραφή graphê，两字一起的意思是”以光线绘图”。摄影是指使用某种专门设备进行影像记录的过程，一般我们使用机械照相机或者数码照相机进行摄影。有时摄影也会被称为照相，也就是通过物体所反射的光线使感光介质曝光的过程。有人说过的一句精辟的语言：摄影家的能力是把日常生活中稍纵即逝的平凡事物转化为不朽的视觉图像。',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '19',
		'cate_childcount' => '0',
		'cate_postcount' => '112'
	),
	'20' => array(
		'cate_id' => '20',
		'root_id' => '1',
		'cate_mod' => 'website',
		'cate_name' => '棋牌',
		'cate_dir' => 'qipai',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '棋牌,棋牌游戏,网络棋牌,真人棋牌游戏',
		'cate_description' => '棋牌是棋类和牌类娱乐项目的总称，包括中国象棋、围棋、国际象棋、蒙古象棋、五子棋、跳棋、国际跳棋（已列入首届世界智力运动会项目）、军棋、桥牌、扑克、麻将等等诸多传统或新兴娱乐项目。棋牌是十分有趣味的娱乐活动，很多人为此废寝忘食，这种过度沉迷于其中的做法是极不健康的。要使下棋打牌完全为养生所用，先了解其中的禁忌是十分必要的。',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '20',
		'cate_childcount' => '0',
		'cate_postcount' => '51'
	),
	'21' => array(
		'cate_id' => '21',
		'root_id' => '1',
		'cate_mod' => 'website',
		'cate_name' => '曲艺',
		'cate_dir' => 'quyi',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '曲艺,微曲艺曲,艺杂谈,曲艺网',
		'cate_description' => '曲艺是中华民族各种“说唱艺术”的统称，它是由民间口头文学和歌唱艺术经过长期发展演变形成的一种独特的艺术形式。据不完全统计，至今活在中国民间的各族曲艺曲种约有400个左右。',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '21',
		'cate_childcount' => '0',
		'cate_postcount' => '73'
	),
	'22' => array(
		'cate_id' => '22',
		'root_id' => '1',
		'cate_mod' => 'website',
		'cate_name' => '爱好',
		'cate_dir' => 'aihao',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '22',
		'cate_childcount' => '0',
		'cate_postcount' => '106'
	),
	'23' => array(
		'cate_id' => '23',
		'root_id' => '1',
		'cate_mod' => 'website',
		'cate_name' => '娱乐',
		'cate_dir' => 'yule',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '娱乐,娱乐网站,日本娱乐网站,韩国娱乐网站,中国娱乐网站',
		'cate_description' => '娱乐网站大全 - 我们为您搜集香港娱乐网站、高端娱乐网站、台湾娱乐网站、网上娱乐网站、24小时娱乐网站等等，找网站就来点我网站目录。',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '23',
		'cate_childcount' => '0',
		'cate_postcount' => '111'
	),
	'24' => array(
		'cate_id' => '24',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '新闻',
		'cate_dir' => 'xinwen',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '新闻网站,英语新闻网站,新闻网站大全,新闻网站排名,国外新闻网站',
		'cate_description' => '新闻网址大全为您精心挑选出国内外最优秀的新闻网站，并保持定期更新和检查，确保您以最安全、最方便的方式，找到您的需要；新闻网址大全下有英语新闻网站,新闻网站大全,新闻网站排名,国外新闻网站。',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '24',
		'cate_childcount' => '0',
		'cate_postcount' => '541'
	),
	'25' => array(
		'cate_id' => '25',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '军事',
		'cate_dir' => 'junshi',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '军事网站,军事网站大全,中国军事网站,军事网站导航',
		'cate_description' => '国内目前最全面,最权威的军事网址大全,最新的军事热点新闻,最全的军事热点专题,最给力的军事热帖,最精彩的军事图片,军事热点事件,军事网站大全,军事相关资源等',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '25',
		'cate_childcount' => '0',
		'cate_postcount' => '101'
	),
	'26' => array(
		'cate_id' => '26',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '财经',
		'cate_dir' => 'caijin',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '26',
		'cate_childcount' => '0',
		'cate_postcount' => '110'
	),
	'27' => array(
		'cate_id' => '27',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '证券',
		'cate_dir' => 'zhengquan',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '27',
		'cate_childcount' => '0',
		'cate_postcount' => '105'
	),
	'28' => array(
		'cate_id' => '28',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '基金',
		'cate_dir' => 'jijin',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '28',
		'cate_childcount' => '0',
		'cate_postcount' => '81'
	),
	'29' => array(
		'cate_id' => '29',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '银行',
		'cate_dir' => 'yinhang',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '29',
		'cate_childcount' => '0',
		'cate_postcount' => '94'
	),
	'30' => array(
		'cate_id' => '30',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '保险',
		'cate_dir' => 'baoxian',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '30',
		'cate_childcount' => '0',
		'cate_postcount' => '95'
	),
	'31' => array(
		'cate_id' => '31',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '地产',
		'cate_dir' => 'fangdichan',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '31',
		'cate_childcount' => '0',
		'cate_postcount' => '131'
	),
	'32' => array(
		'cate_id' => '32',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '汽车',
		'cate_dir' => 'qiche',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '32',
		'cate_childcount' => '0',
		'cate_postcount' => '130'
	),
	'33' => array(
		'cate_id' => '33',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '家居',
		'cate_dir' => 'jiaju',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '33',
		'cate_childcount' => '0',
		'cate_postcount' => '110'
	),
	'34' => array(
		'cate_id' => '34',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '购物',
		'cate_dir' => 'gouwu',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '34',
		'cate_childcount' => '0',
		'cate_postcount' => '125'
	),
	'35' => array(
		'cate_id' => '35',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '团购',
		'cate_dir' => 'gouwu',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '35',
		'cate_childcount' => '0',
		'cate_postcount' => '106'
	),
	'36' => array(
		'cate_id' => '36',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '导购',
		'cate_dir' => 'daogou',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '36',
		'cate_childcount' => '0',
		'cate_postcount' => '113'
	),
	'38' => array(
		'cate_id' => '38',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '生活',
		'cate_dir' => 'shenghuo',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '38',
		'cate_childcount' => '0',
		'cate_postcount' => '103'
	),
	'39' => array(
		'cate_id' => '39',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '天气',
		'cate_dir' => 'tianqi',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '39',
		'cate_childcount' => '0',
		'cate_postcount' => '110'
	),
	'40' => array(
		'cate_id' => '40',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '地图',
		'cate_dir' => 'ditu',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '40',
		'cate_childcount' => '0',
		'cate_postcount' => '88'
	),
	'41' => array(
		'cate_id' => '41',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '旅游',
		'cate_dir' => 'lvyou',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '41',
		'cate_childcount' => '0',
		'cate_postcount' => '126'
	),
	'42' => array(
		'cate_id' => '42',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '机票',
		'cate_dir' => 'jipiao',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '42',
		'cate_childcount' => '0',
		'cate_postcount' => '72'
	),
	'43' => array(
		'cate_id' => '43',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '酒店',
		'cate_dir' => 'jiudian',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '43',
		'cate_childcount' => '0',
		'cate_postcount' => '127'
	),
	'44' => array(
		'cate_id' => '44',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '美食',
		'cate_dir' => 'meishi',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '44',
		'cate_childcount' => '0',
		'cate_postcount' => '129'
	),
	'45' => array(
		'cate_id' => '45',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '健康',
		'cate_dir' => 'jiankang',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '45',
		'cate_childcount' => '0',
		'cate_postcount' => '97'
	),
	'46' => array(
		'cate_id' => '46',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '母婴',
		'cate_dir' => 'muying',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '46',
		'cate_childcount' => '0',
		'cate_postcount' => '112'
	),
	'47' => array(
		'cate_id' => '47',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '女性',
		'cate_dir' => 'nvxing',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '47',
		'cate_childcount' => '0',
		'cate_postcount' => '96'
	),
	'48' => array(
		'cate_id' => '48',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '男士',
		'cate_dir' => 'nanshi',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '48',
		'cate_childcount' => '0',
		'cate_postcount' => '61'
	),
	'49' => array(
		'cate_id' => '49',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '时尚',
		'cate_dir' => 'shishang',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '49',
		'cate_childcount' => '0',
		'cate_postcount' => '101'
	),
	'50' => array(
		'cate_id' => '50',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '美容',
		'cate_dir' => 'meirong',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '50',
		'cate_childcount' => '0',
		'cate_postcount' => '107'
	),
	'51' => array(
		'cate_id' => '51',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '减肥',
		'cate_dir' => 'jianfei',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '51',
		'cate_childcount' => '0',
		'cate_postcount' => '46'
	),
	'52' => array(
		'cate_id' => '52',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '报纸',
		'cate_dir' => 'baozhi',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '52',
		'cate_childcount' => '0',
		'cate_postcount' => '150'
	),
	'53' => array(
		'cate_id' => '53',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '电视',
		'cate_dir' => 'dianshi',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '53',
		'cate_childcount' => '0',
		'cate_postcount' => '156'
	),
	'54' => array(
		'cate_id' => '54',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '杂志',
		'cate_dir' => 'zazhi',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '54',
		'cate_childcount' => '0',
		'cate_postcount' => '87'
	),
	'55' => array(
		'cate_id' => '55',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '宠物',
		'cate_dir' => 'chongwu',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '55',
		'cate_childcount' => '0',
		'cate_postcount' => '111'
	),
	'56' => array(
		'cate_id' => '56',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '资讯',
		'cate_dir' => 'ITzixun',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => 'IT，IT资讯网址大全',
		'cate_description' => 'IT网址大全是为您精心挑选出国内外最优秀的IT网站，并保持定期更新和检查，确保您以最安全、最方便的方式，找到您的需要；IT网址大全下设分类：IT资讯，IT博客，网络编辑。',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '56',
		'cate_childcount' => '0',
		'cate_postcount' => '160'
	),
	'57' => array(
		'cate_id' => '57',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '硬件',
		'cate_dir' => 'yingjian',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '硬件,硬件网址大全',
		'cate_description' => '硬件网址大全是为您精心挑选出国内外最优秀的硬件网站，并保持定期更新和检查，确保您以最安全、最方便的方式，找到您的需要；硬件网址大全下设分类：硬件资讯，服务器资讯，硬件评测，硬件论坛，驱动程序，硬件测试，硬件相关',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '57',
		'cate_childcount' => '0',
		'cate_postcount' => '125'
	),
	'58' => array(
		'cate_id' => '58',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '数码',
		'cate_dir' => 'shuma',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '数码，数码网址大全',
		'cate_description' => '数码网址大全是为您精心挑选出国内外最优秀的数码网站，并保持定期更新和检查，确保您以最安全、最方便的方式，找到您的需要；数码网址大全下设分类：数码综合，数码论坛，数码影像，数码相关。',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '58',
		'cate_childcount' => '0',
		'cate_postcount' => '96'
	),
	'59' => array(
		'cate_id' => '59',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '软件',
		'cate_dir' => 'ruanjian',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '软件，软件网址大全',
		'cate_description' => '软件网址大全是为您精心挑选出国内外最优秀的软件网站，并保持定期更新和检查，确保您以最安全、最方便的方式，找到您的需要；软件网址大全下设分类：常用软件，实用技巧，软件下载，驱动下载，装机软件，软件论坛。',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '59',
		'cate_childcount' => '0',
		'cate_postcount' => '174'
	),
	'60' => array(
		'cate_id' => '60',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '下载',
		'cate_dir' => 'xiazai',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '60',
		'cate_childcount' => '0',
		'cate_postcount' => '163'
	),
	'61' => array(
		'cate_id' => '61',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '编程',
		'cate_dir' => 'biancheng',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '编程，编程网址大全',
		'cate_description' => '编程网址大全是为您精心挑选出国内外最优秀的编程网站，并保持定期更新和检查，确保您以最安全、最方便的方式，找到您的需要；编程网址大全下设分类：编程开发，程序设计，CCC，Visual Basic，Delphi，Power Builder，Java，LinuxUnix，ASP，PHP，源码下载。',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '61',
		'cate_childcount' => '0',
		'cate_postcount' => '101'
	),
	'62' => array(
		'cate_id' => '62',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '搜索',
		'cate_dir' => 'sousuo',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '搜索引擎,搜索引擎排名',
		'cate_description' => '搜索网址大全是为您精心挑选出国内外最优秀的建站网站，并保持定期更新和检查，确保您以最安全、最方便的方式，找到您的需要；搜索网址大全下设分类：搜索引擎，搜索引擎排名，全国搜索引擎网站,。',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '62',
		'cate_childcount' => '0',
		'cate_postcount' => '90'
	),
	'63' => array(
		'cate_id' => '63',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '网址',
		'cate_dir' => 'url',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '63',
		'cate_childcount' => '0',
		'cate_postcount' => '201'
	),
	'64' => array(
		'cate_id' => '64',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '联盟',
		'cate_dir' => 'lianmeng',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '64',
		'cate_childcount' => '0',
		'cate_postcount' => '81'
	),
	'65' => array(
		'cate_id' => '65',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '博客',
		'cate_dir' => 'boke',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '博客，博客网址大全',
		'cate_description' => '博客网址大全是为您精心挑选出国内外最优秀的博客网站，并保持定期更新和检查，确保您以最安全、最方便的方式，找到您的需要；博客网址大全下设分类：博客，微博，博客周边，网摘/书签。',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '65',
		'cate_childcount' => '0',
		'cate_postcount' => '116'
	),
	'66' => array(
		'cate_id' => '66',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '微博',
		'cate_dir' => 'weibo',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '66',
		'cate_childcount' => '0',
		'cate_postcount' => '94'
	),
	'67' => array(
		'cate_id' => '67',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '建站',
		'cate_dir' => 'jianzhan',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '建站，建站网址大全',
		'cate_description' => '建站网址大全是为您精心挑选出国内外最优秀的建站网站，并保持定期更新和检查，确保您以最安全、最方便的方式，找到您的需要；建站网址大全下设分类：网站推广，网页制作，站长资讯，论坛 CMS，网页制作辅助。',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '67',
		'cate_childcount' => '0',
		'cate_postcount' => '146'
	),
	'68' => array(
		'cate_id' => '68',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '域名',
		'cate_dir' => 'yuming',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '68',
		'cate_childcount' => '0',
		'cate_postcount' => '93'
	),
	'69' => array(
		'cate_id' => '69',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => 'DNS',
		'cate_dir' => 'dns',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '69',
		'cate_childcount' => '0',
		'cate_postcount' => '35'
	),
	'70' => array(
		'cate_id' => '70',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '设计',
		'cate_dir' => 'sheji',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '设计网站大全,国外设计网站大全,国外的设计网站大全,室内设计网站大全',
		'cate_description' => '设计网站大全:最专业权威的设计网址导航。及时收录包括网页设计、平面设计、室内设计、包装设计、建筑设计、工业设计、服装设计、设计竞标、品牌设计公司、设计工作室等...',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '70',
		'cate_childcount' => '0',
		'cate_postcount' => '129'
	),
	'71' => array(
		'cate_id' => '71',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '素材',
		'cate_dir' => 'sucai',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '素材网站大全,国外素材网站大全,设计素材网站大全,免费素材网站大全',
		'cate_description' => '素材网站大全_免费素材共享平台.图片素材图库提供海量素材,图片下载,设计素材,PSD源文件,矢量图,AI,CDR,EPS等高清图片下载',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '71',
		'cate_childcount' => '0',
		'cate_postcount' => '87'
	),
	'72' => array(
		'cate_id' => '72',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '桌面',
		'cate_dir' => 'zhuomian',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '72',
		'cate_childcount' => '0',
		'cate_postcount' => '51'
	),
	'73' => array(
		'cate_id' => '73',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '手机',
		'cate_dir' => 'shouji',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '手机网站大全,批发的手机网站大全,手机网站大全wap,国外手机网站大全',
		'cate_description' => '手机网址大全是为您精心挑选出国内外最优秀的手机H网站,并保持定期更新和检查,确保您以最安全、最方便的方式,找到您的需要。',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '73',
		'cate_childcount' => '0',
		'cate_postcount' => '85'
	),
	'74' => array(
		'cate_id' => '74',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '通讯',
		'cate_dir' => 'tongxun',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '74',
		'cate_childcount' => '0',
		'cate_postcount' => '123'
	),
	'75' => array(
		'cate_id' => '75',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '相册',
		'cate_dir' => 'xiangce',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '75',
		'cate_childcount' => '0',
		'cate_postcount' => '29'
	),
	'76' => array(
		'cate_id' => '76',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '邮箱',
		'cate_dir' => 'email',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '76',
		'cate_childcount' => '0',
		'cate_postcount' => '79'
	),
	'77' => array(
		'cate_id' => '77',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '网盘',
		'cate_dir' => 'wangpan',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '77',
		'cate_childcount' => '0',
		'cate_postcount' => '43'
	),
	'78' => array(
		'cate_id' => '78',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '杀毒',
		'cate_dir' => 'shadu',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '78',
		'cate_childcount' => '0',
		'cate_postcount' => '65'
	),
	'79' => array(
		'cate_id' => '79',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '安全',
		'cate_dir' => 'anquan',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '79',
		'cate_childcount' => '0',
		'cate_postcount' => '70'
	),
	'80' => array(
		'cate_id' => '80',
		'root_id' => '4',
		'cate_mod' => 'website',
		'cate_name' => '教育',
		'cate_dir' => 'jiaoyu',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '教育网站大全,教育网站大全大全,小学教育网站大全,免费教育网站大全',
		'cate_description' => '教育网站大全_为您精心挑选出国内外最优秀的教育网站,并保持定期更新和检查,确保您以最安全、最方便的方式,找到您的需要。',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '80',
		'cate_childcount' => '0',
		'cate_postcount' => '206'
	),
	'81' => array(
		'cate_id' => '81',
		'root_id' => '4',
		'cate_mod' => 'website',
		'cate_name' => '考试',
		'cate_dir' => 'kaoshi',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '81',
		'cate_childcount' => '0',
		'cate_postcount' => '116'
	),
	'82' => array(
		'cate_id' => '82',
		'root_id' => '4',
		'cate_mod' => 'website',
		'cate_name' => '招生',
		'cate_dir' => 'zhaosheng',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '82',
		'cate_childcount' => '0',
		'cate_postcount' => '97'
	),
	'83' => array(
		'cate_id' => '83',
		'root_id' => '4',
		'cate_mod' => 'website',
		'cate_name' => '学校',
		'cate_dir' => 'xuexiao',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '83',
		'cate_childcount' => '0',
		'cate_postcount' => '408'
	),
	'84' => array(
		'cate_id' => '84',
		'root_id' => '4',
		'cate_mod' => 'website',
		'cate_name' => '图书',
		'cate_dir' => 'book',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '84',
		'cate_childcount' => '0',
		'cate_postcount' => '95'
	),
	'85' => array(
		'cate_id' => '85',
		'root_id' => '4',
		'cate_mod' => 'website',
		'cate_name' => '外语',
		'cate_dir' => 'waiyu',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '85',
		'cate_childcount' => '0',
		'cate_postcount' => '73'
	),
	'86' => array(
		'cate_id' => '86',
		'root_id' => '4',
		'cate_mod' => 'website',
		'cate_name' => '留学',
		'cate_dir' => 'liuxue',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '86',
		'cate_childcount' => '0',
		'cate_postcount' => '108'
	),
	'87' => array(
		'cate_id' => '87',
		'root_id' => '4',
		'cate_mod' => 'website',
		'cate_name' => '翻译',
		'cate_dir' => 'fanyi',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '87',
		'cate_childcount' => '0',
		'cate_postcount' => '64'
	),
	'88' => array(
		'cate_id' => '88',
		'root_id' => '4',
		'cate_mod' => 'website',
		'cate_name' => '招聘',
		'cate_dir' => 'zhaopin',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '招聘网站大全,惠州招聘网站大全,上海招聘网站大全,沈阳招聘网站大全,免费招聘网站大全,长春招聘网站大全,宁波招聘网站大全',
		'cate_description' => '招聘网站大全收录整理了中国各地区的人才网、招聘网、求职网、人才市场等人才招聘相关的网站信息,方便职场人士快速查找专业的招聘网站大全、求职网站大全.',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '88',
		'cate_childcount' => '0',
		'cate_postcount' => '121'
	),
	'89' => array(
		'cate_id' => '89',
		'root_id' => '4',
		'cate_mod' => 'website',
		'cate_name' => '职教',
		'cate_dir' => 'zhijiao',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '89',
		'cate_childcount' => '0',
		'cate_postcount' => '130'
	),
	'90' => array(
		'cate_id' => '90',
		'root_id' => '4',
		'cate_mod' => 'website',
		'cate_name' => '论文',
		'cate_dir' => 'lunwen',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '90',
		'cate_childcount' => '0',
		'cate_postcount' => '81'
	),
	'91' => array(
		'cate_id' => '91',
		'root_id' => '4',
		'cate_mod' => 'website',
		'cate_name' => '百科',
		'cate_dir' => 'baike',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '91',
		'cate_childcount' => '0',
		'cate_postcount' => '87'
	),
	'92' => array(
		'cate_id' => '92',
		'root_id' => '4',
		'cate_mod' => 'website',
		'cate_name' => '文库',
		'cate_dir' => 'wenku',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '92',
		'cate_childcount' => '0',
		'cate_postcount' => '64'
	),
	'93' => array(
		'cate_id' => '93',
		'root_id' => '4',
		'cate_mod' => 'website',
		'cate_name' => '知识',
		'cate_dir' => 'zhishi',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '93',
		'cate_childcount' => '0',
		'cate_postcount' => '112'
	),
	'94' => array(
		'cate_id' => '94',
		'root_id' => '4',
		'cate_mod' => 'website',
		'cate_name' => '天文',
		'cate_dir' => 'tianwen',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '94',
		'cate_childcount' => '0',
		'cate_postcount' => '31'
	),
	'95' => array(
		'cate_id' => '95',
		'root_id' => '4',
		'cate_mod' => 'website',
		'cate_name' => '历史',
		'cate_dir' => 'lishi',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '95',
		'cate_childcount' => '0',
		'cate_postcount' => '62'
	),
	'96' => array(
		'cate_id' => '96',
		'root_id' => '5',
		'cate_mod' => 'website',
		'cate_name' => '门户',
		'cate_dir' => 'menhu',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '96',
		'cate_childcount' => '0',
		'cate_postcount' => '161'
	),
	'97' => array(
		'cate_id' => '97',
		'root_id' => '5',
		'cate_mod' => 'website',
		'cate_name' => '酷站',
		'cate_dir' => 'kuzhan',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '97',
		'cate_childcount' => '0',
		'cate_postcount' => '51'
	),
	'98' => array(
		'cate_id' => '98',
		'root_id' => '5',
		'cate_mod' => 'website',
		'cate_name' => '政府',
		'cate_dir' => 'zhengfu',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '98',
		'cate_childcount' => '0',
		'cate_postcount' => '141'
	),
	'99' => array(
		'cate_id' => '99',
		'root_id' => '5',
		'cate_mod' => 'website',
		'cate_name' => '法律',
		'cate_dir' => 'falv',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '99',
		'cate_childcount' => '0',
		'cate_postcount' => '106'
	),
	'100' => array(
		'cate_id' => '100',
		'root_id' => '5',
		'cate_mod' => 'website',
		'cate_name' => '行业',
		'cate_dir' => 'hangye',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '100',
		'cate_childcount' => '0',
		'cate_postcount' => '80'
	),
	'101' => array(
		'cate_id' => '101',
		'root_id' => '0',
		'cate_mod' => 'website',
		'cate_name' => '公司企业',
		'cate_dir' => 'qiye',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '公司,企业,企业网站,公司企业文化,有限公司',
		'cate_description' => '公司企业网站大全 - 最权威的公司与企业，工艺礼品，排行榜,聚合了众多优质公司与企业，服务，并根据网站的综合值进行排名，找网站就来点我网站目录。',
		'cate_arrparentid' => '0',
		'cate_arrchildid' => '101,108,110,124,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,147,148,155,157,171,172,173',
		'cate_childcount' => '25',
		'cate_postcount' => '2420'
	),
	'102' => array(
		'cate_id' => '102',
		'root_id' => '5',
		'cate_mod' => 'website',
		'cate_name' => '国外',
		'cate_dir' => 'guowai',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '102',
		'cate_childcount' => '0',
		'cate_postcount' => '209'
	),
	'103' => array(
		'cate_id' => '103',
		'root_id' => '5',
		'cate_mod' => 'website',
		'cate_name' => '宗教',
		'cate_dir' => 'zongjiao',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '103',
		'cate_childcount' => '0',
		'cate_postcount' => '70'
	),
	'104' => array(
		'cate_id' => '104',
		'root_id' => '5',
		'cate_mod' => 'website',
		'cate_name' => '公益',
		'cate_dir' => 'gongyi',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '104',
		'cate_childcount' => '0',
		'cate_postcount' => '81'
	),
	'105' => array(
		'cate_id' => '105',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '借贷',
		'cate_dir' => 'jiedai',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '借贷',
		'cate_description' => '借贷',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '105',
		'cate_childcount' => '0',
		'cate_postcount' => '110'
	),
	'106' => array(
		'cate_id' => '106',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '奢侈',
		'cate_dir' => 'shechi',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '奢侈品',
		'cate_description' => '奢侈品',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '106',
		'cate_childcount' => '0',
		'cate_postcount' => '74'
	),
	'107' => array(
		'cate_id' => '107',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '婚庆',
		'cate_dir' => 'hunqing',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '婚庆',
		'cate_description' => '婚庆',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '107',
		'cate_childcount' => '0',
		'cate_postcount' => '89'
	),
	'108' => array(
		'cate_id' => '108',
		'root_id' => '101',
		'cate_mod' => 'website',
		'cate_name' => '礼品',
		'cate_dir' => 'lipin',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '广告礼品网，广告礼品大全，创意礼品',
		'cate_description' => '点我分类目录为您提供大量广告礼品信息，您可以免费查询广告礼品,广告礼品定制,广告礼品回收,广告礼品收购,广告礼品批发,广告礼品网等信息，同时您可以免费发布广告礼品信息',
		'cate_arrparentid' => '0,101',
		'cate_arrchildid' => '108',
		'cate_childcount' => '0',
		'cate_postcount' => '77'
	),
	'109' => array(
		'cate_id' => '109',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '鲜花',
		'cate_dir' => 'xianhua',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '鲜花',
		'cate_description' => '鲜花',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '109',
		'cate_childcount' => '0',
		'cate_postcount' => '57'
	),
	'110' => array(
		'cate_id' => '110',
		'root_id' => '101',
		'cate_mod' => 'website',
		'cate_name' => '服务',
		'cate_dir' => 'fuwu',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '礼仪公司服务项目，实用礼仪大全',
		'cate_description' => '点我分类目录为您提供优质高效的礼仪庆典服务实用礼仪网站大全',
		'cate_arrparentid' => '0,101',
		'cate_arrchildid' => '110',
		'cate_childcount' => '0',
		'cate_postcount' => '299'
	),
	'111' => array(
		'cate_id' => '111',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '眼镜',
		'cate_dir' => 'yanjing',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '眼镜',
		'cate_description' => '眼镜',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '111',
		'cate_childcount' => '0',
		'cate_postcount' => '64'
	),
	'112' => array(
		'cate_id' => '112',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '珠宝',
		'cate_dir' => 'zhubao',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '珠宝',
		'cate_description' => '珠宝',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '112',
		'cate_childcount' => '0',
		'cate_postcount' => '81'
	),
	'113' => array(
		'cate_id' => '113',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '租赁',
		'cate_dir' => 'zulin',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '租赁',
		'cate_description' => '租赁',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '113',
		'cate_childcount' => '0',
		'cate_postcount' => '74'
	),
	'114' => array(
		'cate_id' => '114',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '保健',
		'cate_dir' => 'baojian',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '114',
		'cate_childcount' => '0',
		'cate_postcount' => '72'
	),
	'115' => array(
		'cate_id' => '115',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '物流',
		'cate_dir' => 'kuaidi',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '物流',
		'cate_description' => '物流',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '115',
		'cate_childcount' => '0',
		'cate_postcount' => '109'
	),
	'116' => array(
		'cate_id' => '116',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '起名',
		'cate_dir' => 'qiming',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '星座/起名',
		'cate_description' => '星座/起名',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '116',
		'cate_childcount' => '0',
		'cate_postcount' => '73'
	),
	'117' => array(
		'cate_id' => '117',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '搬家',
		'cate_dir' => 'banjia',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '搬家',
		'cate_description' => '搬家',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '117',
		'cate_childcount' => '0',
		'cate_postcount' => '69'
	),
	'118' => array(
		'cate_id' => '118',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '工具',
		'cate_dir' => 'gongju',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '工具',
		'cate_description' => '工具',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '118',
		'cate_childcount' => '0',
		'cate_postcount' => '118'
	),
	'119' => array(
		'cate_id' => '119',
		'root_id' => '4',
		'cate_mod' => 'website',
		'cate_name' => '驾校',
		'cate_dir' => 'jiaxiao',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '驾校',
		'cate_description' => '驾校',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '119',
		'cate_childcount' => '0',
		'cate_postcount' => '71'
	),
	'120' => array(
		'cate_id' => '120',
		'root_id' => '5',
		'cate_mod' => 'website',
		'cate_name' => '税务',
		'cate_dir' => 'shuiwu',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '税务',
		'cate_description' => '税务',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '120',
		'cate_childcount' => '0',
		'cate_postcount' => '58'
	),
	'121' => array(
		'cate_id' => '121',
		'root_id' => '5',
		'cate_mod' => 'website',
		'cate_name' => '社会',
		'cate_dir' => 'shehui',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '社会',
		'cate_description' => '社会',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '121',
		'cate_childcount' => '0',
		'cate_postcount' => '93'
	),
	'122' => array(
		'cate_id' => '122',
		'root_id' => '5',
		'cate_mod' => 'website',
		'cate_name' => '民俗',
		'cate_dir' => 'minsu',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '民俗',
		'cate_description' => '民俗',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '122',
		'cate_childcount' => '0',
		'cate_postcount' => '21'
	),
	'123' => array(
		'cate_id' => '123',
		'root_id' => '5',
		'cate_mod' => 'website',
		'cate_name' => '绘画',
		'cate_dir' => 'huihua',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '绘画',
		'cate_description' => '绘画',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '123',
		'cate_childcount' => '0',
		'cate_postcount' => '50'
	),
	'124' => array(
		'cate_id' => '124',
		'root_id' => '101',
		'cate_mod' => 'website',
		'cate_name' => '配件',
		'cate_dir' => 'peijian',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '模具配件供应商，模具配件厂',
		'cate_description' => '模具配件点我网站大全，点我网航提供模具配件网址大全，收集和整理模具配件点我网站大全列表信息，让用户最快的找到模具配件自己所需模具配件网址。更多的模具配件网站尽在点我分类目录。',
		'cate_arrparentid' => '0,101',
		'cate_arrchildid' => '124',
		'cate_childcount' => '0',
		'cate_postcount' => '97'
	),
	'125' => array(
		'cate_id' => '125',
		'root_id' => '5',
		'cate_mod' => 'website',
		'cate_name' => '艺术',
		'cate_dir' => 'yishu',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '艺术',
		'cate_description' => '艺术',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '125',
		'cate_childcount' => '0',
		'cate_postcount' => '104'
	),
	'126' => array(
		'cate_id' => '126',
		'root_id' => '5',
		'cate_mod' => 'website',
		'cate_name' => '农业',
		'cate_dir' => 'nongye',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '农业,农业网站大全,中国农业网站大全,农业网址导航,农业网站',
		'cate_description' => '农业网站大全汇集水果、蔬菜、畜牧、水产、种子、化肥、饲料、农机、花木、粮油、茶叶、农药等农产品、农资的企业、行业或政府网站，为中国农业服务。农业网址大全，农业网站大全，农业网址导航，农业网站，农业网站导航，农业网，中国农业网。',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '126',
		'cate_childcount' => '0',
		'cate_postcount' => '114'
	),
	'127' => array(
		'cate_id' => '127',
		'root_id' => '5',
		'cate_mod' => 'website',
		'cate_name' => '医药',
		'cate_dir' => 'yaopin',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '127',
		'cate_childcount' => '0',
		'cate_postcount' => '95'
	),
	'128' => array(
		'cate_id' => '128',
		'root_id' => '5',
		'cate_mod' => 'website',
		'cate_name' => '交通',
		'cate_dir' => 'jiaotong',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '交通/汽车',
		'cate_description' => '交通/汽车',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '128',
		'cate_childcount' => '0',
		'cate_postcount' => '105'
	),
	'129' => array(
		'cate_id' => '129',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => 'SEO',
		'cate_dir' => 'SEO',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => 'SEO优化',
		'cate_description' => 'SEO优化，网站推广',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '129',
		'cate_childcount' => '0',
		'cate_postcount' => '129'
	),
	'130' => array(
		'cate_id' => '130',
		'root_id' => '101',
		'cate_mod' => 'website',
		'cate_name' => '工艺',
		'cate_dir' => 'gongyi',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '公司与企业，艺礼品，排行榜',
		'cate_description' => '工艺礼品,金属工艺制品厂排行榜,聚合了众多优质礼品公司与工艺礼品厂，并根据网站的综合值进行排名.找最好的公司与，广告礼品厂家定做',
		'cate_arrparentid' => '0,101',
		'cate_arrchildid' => '130',
		'cate_childcount' => '0',
		'cate_postcount' => '82'
	),
	'131' => array(
		'cate_id' => '131',
		'root_id' => '101',
		'cate_mod' => 'website',
		'cate_name' => '工业',
		'cate_dir' => 'gongye',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '工业用品，公司与企业，排行榜',
		'cate_description' => '工业用品网址大全，收集和整理工业用品网站信息，让用户最快的找到工业用品自己所需机械设备网址',
		'cate_arrparentid' => '0,101',
		'cate_arrchildid' => '131',
		'cate_childcount' => '0',
		'cate_postcount' => '154'
	),
	'132' => array(
		'cate_id' => '132',
		'root_id' => '101',
		'cate_mod' => 'website',
		'cate_name' => '辅料',
		'cate_dir' => 'fuliao',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '服装辅料，服装辅料知名门户，辅料网，服装辅料网，服装辅料资讯，中国辅料网，服装辅料采购，服装行业展会，服装辅料企业合作，服装辅料商城，服饰配件，织带厂',
		'cate_description' => '点我网站目录收集和整理供应批发、求购、辅料企业、辅料行业资讯、辅料行业展会等信息服务网站，让用户最快的找到自己所需服装辅料网址',
		'cate_arrparentid' => '0,101',
		'cate_arrchildid' => '132',
		'cate_childcount' => '0',
		'cate_postcount' => '19'
	),
	'133' => array(
		'cate_id' => '133',
		'root_id' => '101',
		'cate_mod' => 'website',
		'cate_name' => '电气',
		'cate_dir' => 'dianqi',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '电气/电工',
		'cate_description' => '电气/电工',
		'cate_arrparentid' => '0,101',
		'cate_arrchildid' => '133',
		'cate_childcount' => '0',
		'cate_postcount' => '95'
	),
	'134' => array(
		'cate_id' => '134',
		'root_id' => '101',
		'cate_mod' => 'website',
		'cate_name' => '机械',
		'cate_dir' => 'jixie',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '机械设备，机械设备网，机械设备有限公司，机械设备厂家',
		'cate_description' => '机械设备云目录，整理和收集网站网址，方便和快速的找到机械设备网站大全信息，更多的相关机械设备有限公司网站尽在分类目录',
		'cate_arrparentid' => '0,101',
		'cate_arrchildid' => '134',
		'cate_childcount' => '0',
		'cate_postcount' => '280'
	),
	'135' => array(
		'cate_id' => '135',
		'root_id' => '101',
		'cate_mod' => 'website',
		'cate_name' => '家具',
		'cate_dir' => 'jiajupin',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '家具品牌官网，家具网上商城，原木家具，高档家具品牌有哪些',
		'cate_description' => '家私家具云目录，整理和收集网站网址，方便和快速的找到家私家具网站大全信息，更多的相关家私家具网站尽在分类目录',
		'cate_arrparentid' => '0,101',
		'cate_arrchildid' => '135',
		'cate_childcount' => '0',
		'cate_postcount' => '92'
	),
	'136' => array(
		'cate_id' => '136',
		'root_id' => '101',
		'cate_mod' => 'website',
		'cate_name' => '建筑',
		'cate_dir' => 'jianzhu',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '建筑设计，建筑装饰装修，建筑装饰材料，建筑装修装饰工程',
		'cate_description' => '建筑装饰云目录，整理和收集网站网址，方便和快速的找到建筑装饰网站大全信息，更多的相关建筑装饰网站尽在分类目录!',
		'cate_arrparentid' => '0,101',
		'cate_arrchildid' => '136',
		'cate_childcount' => '0',
		'cate_postcount' => '198'
	),
	'137' => array(
		'cate_id' => '137',
		'root_id' => '101',
		'cate_mod' => 'website',
		'cate_name' => '电子',
		'cate_dir' => 'dianzi',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '电子与通信,电子与通信工程排名',
		'cate_description' => '电子与通信云目录，整理和收集网站网址，方便和快速的找到电子与通信网站大全信息，更多的相关电子与通信网站尽在分类目录',
		'cate_arrparentid' => '0,101',
		'cate_arrchildid' => '137',
		'cate_childcount' => '0',
		'cate_postcount' => '155'
	),
	'138' => array(
		'cate_id' => '138',
		'root_id' => '101',
		'cate_mod' => 'website',
		'cate_name' => '包装',
		'cate_dir' => 'baozhuang',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '印刷包装，机械，包装印刷厂，包装印刷有限公司',
		'cate_description' => '印刷包装设备，印刷包装厂目录，整理和收集网站网址，方便和快速的找到印刷包装设备网站大全信息，更多的相关印刷包装设备网站尽在分类目录！',
		'cate_arrparentid' => '0,101',
		'cate_arrchildid' => '138',
		'cate_childcount' => '0',
		'cate_postcount' => '83'
	),
	'139' => array(
		'cate_id' => '139',
		'root_id' => '101',
		'cate_mod' => 'website',
		'cate_name' => '纺织',
		'cate_dir' => 'fangzhi',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,101',
		'cate_arrchildid' => '139',
		'cate_childcount' => '0',
		'cate_postcount' => '109'
	),
	'140' => array(
		'cate_id' => '140',
		'root_id' => '101',
		'cate_mod' => 'website',
		'cate_name' => '办公',
		'cate_dir' => 'bangong',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '办公用品网站，办公用品供应商，办公设备有哪些',
		'cate_description' => '办公用品云目录，整理和收集网站网址，方便和快速的找到办公用品网站大全信息，更多的相关办公设备网站尽在分类目录！',
		'cate_arrparentid' => '0,101',
		'cate_arrchildid' => '140',
		'cate_childcount' => '0',
		'cate_postcount' => '67'
	),
	'141' => array(
		'cate_id' => '141',
		'root_id' => '101',
		'cate_mod' => 'website',
		'cate_name' => '仪器',
		'cate_dir' => 'yiqi',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '仪器仪表网，测量仪器，仪器信息网',
		'cate_description' => '仪器仪表，仪器仪表云目录，草谷子整理和收集网站网址，方便和快速的找到仪器仪表网站大全信息，更多的相关仪器仪表网站尽在分类目录！',
		'cate_arrparentid' => '0,101',
		'cate_arrchildid' => '141',
		'cate_childcount' => '0',
		'cate_postcount' => '88'
	),
	'142' => array(
		'cate_id' => '142',
		'root_id' => '101',
		'cate_mod' => 'website',
		'cate_name' => '食品',
		'cate_dir' => 'shipin',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '烟酒，食品，饮料，烟酒专卖店加盟，食品安全',
		'cate_description' => '烟酒，食品，饮料云目录，整理和收集网站网址，方便和快速的找到烟酒食品网站大全信息，更多的相关烟酒，食品网站尽在分类目录！',
		'cate_arrparentid' => '0,101',
		'cate_arrchildid' => '142',
		'cate_childcount' => '0',
		'cate_postcount' => '118'
	),
	'143' => array(
		'cate_id' => '143',
		'root_id' => '101',
		'cate_mod' => 'website',
		'cate_name' => '器材',
		'cate_dir' => 'qicai',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '影音器材',
		'cate_description' => '影音器材，视听播放 音响系统 影音器材 舞台音响 影音配件',
		'cate_arrparentid' => '0,101',
		'cate_arrchildid' => '143',
		'cate_childcount' => '0',
		'cate_postcount' => '87'
	),
	'144' => array(
		'cate_id' => '144',
		'root_id' => '101',
		'cate_mod' => 'website',
		'cate_name' => '石油',
		'cate_dir' => 'shiyou',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,101',
		'cate_arrchildid' => '144',
		'cate_childcount' => '0',
		'cate_postcount' => '78'
	),
	'145' => array(
		'cate_id' => '145',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '电商',
		'cate_dir' => 'retailers',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '电商',
		'cate_description' => '电商',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '145',
		'cate_childcount' => '0',
		'cate_postcount' => '105'
	),
	'146' => array(
		'cate_id' => '146',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '资源',
		'cate_dir' => 'ziyuan',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '146',
		'cate_childcount' => '0',
		'cate_postcount' => '100'
	),
	'147' => array(
		'cate_id' => '147',
		'root_id' => '101',
		'cate_mod' => 'website',
		'cate_name' => '木材',
		'cate_dir' => 'mucai',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,101',
		'cate_arrchildid' => '147',
		'cate_childcount' => '0',
		'cate_postcount' => '38'
	),
	'148' => array(
		'cate_id' => '148',
		'root_id' => '101',
		'cate_mod' => 'website',
		'cate_name' => '化工',
		'cate_dir' => 'huaxue',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,101',
		'cate_arrchildid' => '148',
		'cate_childcount' => '0',
		'cate_postcount' => '132'
	),
	'149' => array(
		'cate_id' => '149',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '存储',
		'cate_dir' => 'cunchu',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '免费云存储，图片云存储、音频云存储、视频云存储等',
		'cate_description' => '提供安全、稳定、高速、开放的文件云存储、图片云存储、音频云存储、视频云存储等各类专业的云存储服务',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '149',
		'cate_childcount' => '0',
		'cate_postcount' => '32'
	),
	'150' => array(
		'cate_id' => '150',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '微信',
		'cate_dir' => 'weixin',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '微信，微信公众号，微信公众账号，微信公众，微信公众帐号，微信网页版，网页版微信，微信网页，网页微信，微信推广，微信公众平台，微信公众平台登录，微信pc版，微信电脑版，微信群，微信推广，微信导航，微信营',
		'cate_description' => '微信公众帐号大全收录了微信公共账号,微信美女账号,微信明星帐号,微信搞笑账号等各种微信公众账号以及微信、微信网页版的使用方法。草谷子是微信公众账号推广最好的平台',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '150',
		'cate_childcount' => '0',
		'cate_postcount' => '77'
	),
	'152' => array(
		'cate_id' => '152',
		'root_id' => '1',
		'cate_mod' => 'website',
		'cate_name' => '直播',
		'cate_dir' => 'zhibo',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '直播,直播网站,韩国直播网站,足球直播网站,lol直播网站',
		'cate_description' => '',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '152',
		'cate_childcount' => '0',
		'cate_postcount' => '116'
	),
	'153' => array(
		'cate_id' => '153',
		'root_id' => '5',
		'cate_mod' => 'website',
		'cate_name' => '医院',
		'cate_dir' => 'yiyuan',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '153',
		'cate_childcount' => '0',
		'cate_postcount' => '98'
	),
	'154' => array(
		'cate_id' => '154',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '支付',
		'cate_dir' => 'zhifu',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '154',
		'cate_childcount' => '0',
		'cate_postcount' => '59'
	),
	'155' => array(
		'cate_id' => '155',
		'root_id' => '101',
		'cate_mod' => 'website',
		'cate_name' => '服装',
		'cate_dir' => 'fuzhuang',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,101',
		'cate_arrchildid' => '155',
		'cate_childcount' => '0',
		'cate_postcount' => '122'
	),
	'156' => array(
		'cate_id' => '156',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => 'b2b',
		'cate_dir' => 'b2b',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => 'B2B网站大全,b2b,b2b网站',
		'cate_description' => 'B2B网站大全收集了企业营销人员最常用的B2B信息发布网站并对B2B网站进行了详细的介绍,其中B2B网站排名是对国内B2B网站科学排列,欢迎使用B2B网站大全!',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '156',
		'cate_childcount' => '0',
		'cate_postcount' => '68'
	),
	'157' => array(
		'cate_id' => '157',
		'root_id' => '101',
		'cate_mod' => 'website',
		'cate_name' => '黄页',
		'cate_dir' => 'huangye',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '黄页,黄页网站,黄页网站大全',
		'cate_description' => '',
		'cate_arrparentid' => '0,101',
		'cate_arrchildid' => '157',
		'cate_childcount' => '0',
		'cate_postcount' => '74'
	),
	'158' => array(
		'cate_id' => '158',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '黑客',
		'cate_dir' => 'hacker',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '黑客网站,黑客网站大全,黑客网站排名,免费黑客网站',
		'cate_description' => '黑客网站大全为您提供：黑客网站、黑客网站大全、黑客网站排名、免费黑客网站、以及国内国外的黑客网站大全！',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '158',
		'cate_childcount' => '0',
		'cate_postcount' => '48'
	),
	'159' => array(
		'cate_id' => '159',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '分类信息',
		'cate_dir' => 'fenlei',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '分类信息网站,分类信息网站大全',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '159',
		'cate_childcount' => '0',
		'cate_postcount' => '147'
	),
	'161' => array(
		'cate_id' => '161',
		'root_id' => '1',
		'cate_mod' => 'website',
		'cate_name' => 'dj',
		'cate_dir' => 'dj',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => 'dj,dj网站,dj网站大全',
		'cate_description' => 'DJ网站大全提供：潮牌夜店dj,dj舞曲 超劲爆dj网站,提供最新dj下载,歌曲串烧,嗨曲,dj小可电音舞曲,慢摇dj,mc喊麦dj网站,夜店歌曲,最好听的dj嗨嗨网音乐MP3舞曲,车载dj舞曲等等有关DJ的网站让大家选择。',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '161',
		'cate_childcount' => '0',
		'cate_postcount' => '82'
	),
	'162' => array(
		'cate_id' => '162',
		'root_id' => '5',
		'cate_mod' => 'website',
		'cate_name' => '左派',
		'cate_dir' => 'zuopai',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '左派,左派网站,左派网站大全,左派网址大全',
		'cate_description' => '左派网站大全主张追求正义,流芳百世,最受中华爱国人士欢迎！收录各类左派网站,右派网站,社会人生网站,公益网站,工人网站,左派右派名家博客等。',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '162',
		'cate_childcount' => '0',
		'cate_postcount' => '15'
	),
	'163' => array(
		'cate_id' => '163',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '机车',
		'cate_dir' => 'jiche',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '机车,机车网',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '163',
		'cate_childcount' => '0',
		'cate_postcount' => '57'
	),
	'164' => array(
		'cate_id' => '164',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '数据',
		'cate_dir' => 'shuju',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '数据,数据网站,数据网站大全',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '164',
		'cate_childcount' => '0',
		'cate_postcount' => '86'
	),
	'165' => array(
		'cate_id' => '165',
		'root_id' => '5',
		'cate_mod' => 'website',
		'cate_name' => '香港',
		'cate_dir' => 'hk',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '香港网站,香港网站大全',
		'cate_description' => '',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '165',
		'cate_childcount' => '0',
		'cate_postcount' => '104'
	),
	'166' => array(
		'cate_id' => '166',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => 'VR',
		'cate_dir' => 'vr',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => 'vr,虚拟现实,vr网站大全',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '166',
		'cate_childcount' => '0',
		'cate_postcount' => '44'
	),
	'167' => array(
		'cate_id' => '167',
		'root_id' => '2',
		'cate_mod' => 'website',
		'cate_name' => '海淘',
		'cate_dir' => 'haitao',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '海淘网站,海淘网站大全',
		'cate_description' => '',
		'cate_arrparentid' => '0,2',
		'cate_arrchildid' => '167',
		'cate_childcount' => '0',
		'cate_postcount' => '46'
	),
	'168' => array(
		'cate_id' => '168',
		'root_id' => '1',
		'cate_mod' => 'website',
		'cate_name' => '小游戏',
		'cate_dir' => 'xiaoyouxi',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '小游戏网站,小游戏大全,小游戏网站大全',
		'cate_description' => '',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '168',
		'cate_childcount' => '0',
		'cate_postcount' => '76'
	),
	'169' => array(
		'cate_id' => '169',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '创业',
		'cate_dir' => 'chuangye',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '创业网站,创业网站大全',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '169',
		'cate_childcount' => '0',
		'cate_postcount' => '97'
	),
	'170' => array(
		'cate_id' => '170',
		'root_id' => '4',
		'cate_mod' => 'website',
		'cate_name' => '科技',
		'cate_dir' => 'keji',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '科技,科技网站,科技网站大全',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '170',
		'cate_childcount' => '0',
		'cate_postcount' => '75'
	),
	'171' => array(
		'cate_id' => '171',
		'root_id' => '101',
		'cate_mod' => 'website',
		'cate_name' => '装修',
		'cate_dir' => 'zhuangxiu',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '装修,装修网站,装修网站大全',
		'cate_description' => '',
		'cate_arrparentid' => '0,101',
		'cate_arrchildid' => '171',
		'cate_childcount' => '0',
		'cate_postcount' => '112'
	),
	'172' => array(
		'cate_id' => '172',
		'root_id' => '101',
		'cate_mod' => 'website',
		'cate_name' => '商业',
		'cate_dir' => 'shangye',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '商业网站,商业网站大全',
		'cate_description' => '',
		'cate_arrparentid' => '0,101',
		'cate_arrchildid' => '172',
		'cate_childcount' => '0',
		'cate_postcount' => '92'
	),
	'173' => array(
		'cate_id' => '173',
		'root_id' => '101',
		'cate_mod' => 'website',
		'cate_name' => '加盟',
		'cate_dir' => 'jiameng',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '加盟网站,加盟网站大全',
		'cate_description' => '',
		'cate_arrparentid' => '0,101',
		'cate_arrchildid' => '173',
		'cate_childcount' => '0',
		'cate_postcount' => '109'
	),
	'174' => array(
		'cate_id' => '174',
		'root_id' => '1',
		'cate_mod' => 'website',
		'cate_name' => '电影',
		'cate_dir' => '',
		'cate_url' => 'dianying',
		'cate_isbest' => '1',
		'cate_keywords' => '电影,电影网站,电影网站大全',
		'cate_description' => '',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '174',
		'cate_childcount' => '0',
		'cate_postcount' => '226'
	),
	'175' => array(
		'cate_id' => '175',
		'root_id' => '3',
		'cate_mod' => 'website',
		'cate_name' => '交易',
		'cate_dir' => 'jiaoyi',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '175',
		'cate_childcount' => '0',
		'cate_postcount' => '41'
	),
);
?>