<?php /* Smarty version Smarty-3.1.18, created on 2023-01-08 22:13:11
         compiled from "/www/wwwroot/digg58.com/templet/default/top.html" */ ?>
<?php /*%%SmartyHeaderCode:109075228463bacf77f1f982-91353363%%*/if(!defined('SMARTY_DIR')) exit('no direct access allowed');
$_valid = $_smarty_tpl->decodeProperties(array (
  'file_dependency' => 
  array (
    'cb6b2af00e017742c4860defb78adc822ee606ed' => 
    array (
      0 => '/www/wwwroot/digg58.com/templet/default/top.html',
      1 => 1647173775,
      2 => 'file',
    ),
  ),
  'nocache_hash' => '109075228463bacf77f1f982-91353363',
  'function' => 
  array (
  ),
  'variables' => 
  array (
    'page_name' => 0,
    'site_name' => 0,
    'css_path' => 0,
    'site_root' => 0,
    'option' => 0,
    'js_path' => 0,
    'crumb' => 0,
    'rssfeed' => 0,
    'hot' => 0,
    'instat' => 0,
    'outstat' => 0,
  ),
  'has_nocache_code' => false,
  'version' => 'Smarty-3.1.18',
  'unifunc' => 'content_63bacf781e57f7_64466723',
),false); /*/%%SmartyHeaderCode%%*/?>
<?php if ($_valid && !is_callable('content_63bacf781e57f7_64466723')) {function content_63bacf781e57f7_64466723($_smarty_tpl) {?><!DOCTYPE HTML>

<html>

<head>

<title><?php echo $_smarty_tpl->tpl_vars['page_name']->value;?>
 - <?php echo $_smarty_tpl->tpl_vars['site_name']->value;?>
</title>

<meta charset="utf-8">

<meta name="Keywords" content="网站热榜，网站TOP排行榜，热门网站排行，网站风云榜" />

<meta name="Description" content="提供最新热门网站排行数据，让您及时了解那些信息最受关注。" />

<meta name="Copyright" content="Powered By caoguzi.com" />

<link href="<?php echo $_smarty_tpl->tpl_vars['css_path']->value;?>
default/style.css" rel="stylesheet" type="text/css" />

<script type="text/javascript">var $root = '<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
'; var $linktype = '<?php echo $_smarty_tpl->tpl_vars['option']->value['link_struct'];?>
';</script>

<script type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['js_path']->value;?>
jquery.min.js"></script>

<script type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['js_path']->value;?>
common.js"></script>
<?php echo $_smarty_tpl->getSubTemplate ("header-public.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>

</head>



<body>

<?php echo $_smarty_tpl->getSubTemplate ("header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>


<div class="wrapper">

	<div class="crumb"><?php echo $_smarty_tpl->tpl_vars['crumb']->value;?>
&nbsp;<?php echo $_smarty_tpl->tpl_vars['rssfeed']->value;?>
</div>

    <div class="mainbox">
    	
    		<div class="topsite">

            	<div class="topsite-title">小说网站排行榜 [热]</div>

                <ul class="topsite-list">

                   	<?php  $_smarty_tpl->tpl_vars['hot'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['hot']->_loop = false;
 $_from = get_websites(10,10,false,false,'views'); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
 $_smarty_tpl->tpl_vars['smarty']->value['foreach']['hot_website']['iteration']=0;
foreach ($_from as $_smarty_tpl->tpl_vars['hot']->key => $_smarty_tpl->tpl_vars['hot']->value) {
$_smarty_tpl->tpl_vars['hot']->_loop = true;
 $_smarty_tpl->tpl_vars['smarty']->value['foreach']['hot_website']['iteration']++;
?>

                   	<li><span><?php echo $_smarty_tpl->getVariable('smarty')->value['foreach']['hot_website']['iteration'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
</a><em><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_views'];?>
</em></li>

                   	<?php } ?>

               	</ul>

            </div>
            <div class="topsite">

            	<div class="topsite-title">直播网站排行榜 [热]</div>

                <ul class="topsite-list">

                   	<?php  $_smarty_tpl->tpl_vars['hot'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['hot']->_loop = false;
 $_from = get_websites(152,10,false,false,'views'); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
 $_smarty_tpl->tpl_vars['smarty']->value['foreach']['hot_website']['iteration']=0;
foreach ($_from as $_smarty_tpl->tpl_vars['hot']->key => $_smarty_tpl->tpl_vars['hot']->value) {
$_smarty_tpl->tpl_vars['hot']->_loop = true;
 $_smarty_tpl->tpl_vars['smarty']->value['foreach']['hot_website']['iteration']++;
?>

                   	<li><span><?php echo $_smarty_tpl->getVariable('smarty')->value['foreach']['hot_website']['iteration'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
</a><em><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_views'];?>
</em></li>

                   	<?php } ?>

               	</ul>

            </div>
            <div class="topsite">

            	<div class="topsite-title">网购网站排行榜 [热]</div>

                <ul class="topsite-list">

                   	<?php  $_smarty_tpl->tpl_vars['hot'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['hot']->_loop = false;
 $_from = get_websites(145,10,false,false,'views'); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
 $_smarty_tpl->tpl_vars['smarty']->value['foreach']['hot_website']['iteration']=0;
foreach ($_from as $_smarty_tpl->tpl_vars['hot']->key => $_smarty_tpl->tpl_vars['hot']->value) {
$_smarty_tpl->tpl_vars['hot']->_loop = true;
 $_smarty_tpl->tpl_vars['smarty']->value['foreach']['hot_website']['iteration']++;
?>

                   	<li><span><?php echo $_smarty_tpl->getVariable('smarty')->value['foreach']['hot_website']['iteration'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
</a><em><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_views'];?>
</em></li>

                   	<?php } ?>

               	</ul>

            </div>
            <div class="topsite">

            	<div class="topsite-title">游戏网站排行榜 [热]</div>

                <ul class="topsite-list">

                   	<?php  $_smarty_tpl->tpl_vars['hot'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['hot']->_loop = false;
 $_from = get_websites(8,10,false,false,'views'); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
 $_smarty_tpl->tpl_vars['smarty']->value['foreach']['hot_website']['iteration']=0;
foreach ($_from as $_smarty_tpl->tpl_vars['hot']->key => $_smarty_tpl->tpl_vars['hot']->value) {
$_smarty_tpl->tpl_vars['hot']->_loop = true;
 $_smarty_tpl->tpl_vars['smarty']->value['foreach']['hot_website']['iteration']++;
?>

                   	<li><span><?php echo $_smarty_tpl->getVariable('smarty')->value['foreach']['hot_website']['iteration'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
</a><em><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_views'];?>
</em></li>

                   	<?php } ?>

               	</ul>

            </div>
			<div class="blank10"></div>
        	<div class="topsite">

            	<div class="topsite-title">娱乐休闲网站排行榜</div>

                <ul class="topsite-list">

                   	<?php  $_smarty_tpl->tpl_vars['hot'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['hot']->_loop = false;
 $_from = get_websites(1,10,false,false,'views'); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
 $_smarty_tpl->tpl_vars['smarty']->value['foreach']['hot_website']['iteration']=0;
foreach ($_from as $_smarty_tpl->tpl_vars['hot']->key => $_smarty_tpl->tpl_vars['hot']->value) {
$_smarty_tpl->tpl_vars['hot']->_loop = true;
 $_smarty_tpl->tpl_vars['smarty']->value['foreach']['hot_website']['iteration']++;
?>

                   	<li><span><?php echo $_smarty_tpl->getVariable('smarty')->value['foreach']['hot_website']['iteration'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
</a><em><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_views'];?>
</em></li>

                   	<?php } ?>

               	</ul>

            </div>
            <div class="topsite">

            	<div class="topsite-title">生活资讯网站排行榜</div>

                <ul class="topsite-list">

                   	<?php  $_smarty_tpl->tpl_vars['hot'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['hot']->_loop = false;
 $_from = get_websites(2,10,false,false,'views'); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
 $_smarty_tpl->tpl_vars['smarty']->value['foreach']['hot_website']['iteration']=0;
foreach ($_from as $_smarty_tpl->tpl_vars['hot']->key => $_smarty_tpl->tpl_vars['hot']->value) {
$_smarty_tpl->tpl_vars['hot']->_loop = true;
 $_smarty_tpl->tpl_vars['smarty']->value['foreach']['hot_website']['iteration']++;
?>

                   	<li><span><?php echo $_smarty_tpl->getVariable('smarty')->value['foreach']['hot_website']['iteration'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
</a><em><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_views'];?>
</em></li>

                   	<?php } ?>

               	</ul>

            </div>
            <div class="topsite">

            	<div class="topsite-title">电脑网络网站排行榜</div>

                <ul class="topsite-list">

                   	<?php  $_smarty_tpl->tpl_vars['hot'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['hot']->_loop = false;
 $_from = get_websites(3,10,false,false,'views'); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
 $_smarty_tpl->tpl_vars['smarty']->value['foreach']['hot_website']['iteration']=0;
foreach ($_from as $_smarty_tpl->tpl_vars['hot']->key => $_smarty_tpl->tpl_vars['hot']->value) {
$_smarty_tpl->tpl_vars['hot']->_loop = true;
 $_smarty_tpl->tpl_vars['smarty']->value['foreach']['hot_website']['iteration']++;
?>

                   	<li><span><?php echo $_smarty_tpl->getVariable('smarty')->value['foreach']['hot_website']['iteration'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
</a><em><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_views'];?>
</em></li>

                   	<?php } ?>

               	</ul>

            </div>
            <div class="topsite">

            	<div class="topsite-title">教育学习网站排行榜</div>

                <ul class="topsite-list">

                   	<?php  $_smarty_tpl->tpl_vars['hot'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['hot']->_loop = false;
 $_from = get_websites(4,10,false,false,'views'); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
 $_smarty_tpl->tpl_vars['smarty']->value['foreach']['hot_website']['iteration']=0;
foreach ($_from as $_smarty_tpl->tpl_vars['hot']->key => $_smarty_tpl->tpl_vars['hot']->value) {
$_smarty_tpl->tpl_vars['hot']->_loop = true;
 $_smarty_tpl->tpl_vars['smarty']->value['foreach']['hot_website']['iteration']++;
?>

                   	<li><span><?php echo $_smarty_tpl->getVariable('smarty')->value['foreach']['hot_website']['iteration'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
</a><em><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_views'];?>
</em></li>

                   	<?php } ?>

               	</ul>

            </div>
			<div class="blank10"></div>
			<div class="topsite">

            	<div class="topsite-title">综合其他网站排行榜</div>

                <ul class="topsite-list">

                   	<?php  $_smarty_tpl->tpl_vars['hot'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['hot']->_loop = false;
 $_from = get_websites(5,10,false,false,'views'); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
 $_smarty_tpl->tpl_vars['smarty']->value['foreach']['hot_website']['iteration']=0;
foreach ($_from as $_smarty_tpl->tpl_vars['hot']->key => $_smarty_tpl->tpl_vars['hot']->value) {
$_smarty_tpl->tpl_vars['hot']->_loop = true;
 $_smarty_tpl->tpl_vars['smarty']->value['foreach']['hot_website']['iteration']++;
?>

                   	<li><span><?php echo $_smarty_tpl->getVariable('smarty')->value['foreach']['hot_website']['iteration'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
</a><em><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_views'];?>
</em></li>

                   	<?php } ?>

               	</ul>

            </div>
            <div class="topsite">

            	<div class="topsite-title">公司企业网站排行榜</div>

                <ul class="topsite-list">

                   	<?php  $_smarty_tpl->tpl_vars['hot'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['hot']->_loop = false;
 $_from = get_websites(101,10,false,false,'views'); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
 $_smarty_tpl->tpl_vars['smarty']->value['foreach']['hot_website']['iteration']=0;
foreach ($_from as $_smarty_tpl->tpl_vars['hot']->key => $_smarty_tpl->tpl_vars['hot']->value) {
$_smarty_tpl->tpl_vars['hot']->_loop = true;
 $_smarty_tpl->tpl_vars['smarty']->value['foreach']['hot_website']['iteration']++;
?>

                   	<li><span><?php echo $_smarty_tpl->getVariable('smarty')->value['foreach']['hot_website']['iteration'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
</a><em><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_views'];?>
</em></li>

                   	<?php } ?>

               	</ul>

            </div>
            <div class="topsite">

            	<div class="topsite-title">点入网站排行榜</div>

                <ul class="topsite-list">

                   	<?php  $_smarty_tpl->tpl_vars['instat'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['instat']->_loop = false;
 $_from = get_websites(0,10,false,false,'instat'); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
 $_smarty_tpl->tpl_vars['smarty']->value['foreach']['instat_website']['iteration']=0;
foreach ($_from as $_smarty_tpl->tpl_vars['instat']->key => $_smarty_tpl->tpl_vars['instat']->value) {
$_smarty_tpl->tpl_vars['instat']->_loop = true;
 $_smarty_tpl->tpl_vars['smarty']->value['foreach']['instat_website']['iteration']++;
?>

                   	<li><span><?php echo $_smarty_tpl->getVariable('smarty')->value['foreach']['instat_website']['iteration'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['instat']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['instat']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['instat']->value['web_name'];?>
</a><em><?php echo $_smarty_tpl->tpl_vars['instat']->value['web_instat'];?>
</em></li>

                   	<?php } ?>

               	</ul>

            </div>

        	<div class="topsite">

            	<div class="topsite-title">点出网站排行榜</div>

                <ul class="topsite-list">

                   	<?php  $_smarty_tpl->tpl_vars['outstat'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['outstat']->_loop = false;
 $_from = get_websites(0,10,false,false,'outstat'); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
 $_smarty_tpl->tpl_vars['smarty']->value['foreach']['outstat_website']['iteration']=0;
foreach ($_from as $_smarty_tpl->tpl_vars['outstat']->key => $_smarty_tpl->tpl_vars['outstat']->value) {
$_smarty_tpl->tpl_vars['outstat']->_loop = true;
 $_smarty_tpl->tpl_vars['smarty']->value['foreach']['outstat_website']['iteration']++;
?>

                   	<li><span><?php echo $_smarty_tpl->getVariable('smarty')->value['foreach']['outstat_website']['iteration'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['outstat']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['outstat']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['outstat']->value['web_name'];?>
</a><em><?php echo $_smarty_tpl->tpl_vars['outstat']->value['web_outstat'];?>
</em></li>

                   	<?php } ?>

               	</ul>

            </div>
    </div>

</div>

<?php echo $_smarty_tpl->getSubTemplate ("footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>


</body>

</html><?php }} ?>
