<?php
if (!defined('IN_MEMBER')) exit('Access Denied');

$page_name = '网站认领';
$page_url = '?mod=claim';
$tplfile = 'claim.html';
$table = $DB->table('websites');

$action = isset($_GET['act']) ? $_GET['act'] : 'one';
$smarty->assign('action', $action); 

if (!$smarty->isCached($tplfile)) {
	$smarty->assign('page_name', $page_name);
	$smarty->assign('crumb', get_crumb().' &rsaquo; '.$page_name);
	
	if ($_POST['do'] == 'next') {
		$domain = strtolower(trim($_POST['domain']));
		
		if (empty($domain)) {
			alert('请输入要认领的域名！');
		} else {
			if (!is_valid_domain($domain)) {
				alert('请输入正确的网站域名！');
			}
		}
		
    	$query = $DB->query("SELECT web_id FROM $table WHERE web_url='$domain'");
    	if (!$DB->num_rows($query)) {
        	alert('该网站还未被提交！', '?mod=website&act=add');
    	}
		
		$smarty->assign('action', 'two'); 
		$smarty->assign('domain', $domain);
		$smarty->assign('siteurl', format_url($domain));
		$smarty->assign('token', random(32));
		
	} elseif ($_POST['do'] == 'verify') {
		$vtype = trim($_POST['vtype']);
		$domain = strtolower(trim($_POST['domain']));
		$token = trim($_POST['token']);
		
		if (empty($vtype)) {
			alert('请选择验证类型！');
		}
		
		if (empty($domain)) {
			alert('请输入要认领的域名！');
		} else {
			if (!is_valid_domain($domain)) {
				alert('请输入正确的网站域名！');
			}
		}
		
    	$query = $DB->query("SELECT web_id FROM $table WHERE web_url='$domain'");
    	if (!$DB->num_rows($query)) {
        	alert('该网站还未被提交！');
    	}
		
		$siteurl = format_url($domain);
		if ($vtype == 'file') {
			$content = get_url_content($siteurl.'iwebdir-site-verification.html');
			if ($content == $token) {
				$DB->update($table, array('user_id' => $myself['user_id']), array('web_url' => $domain));
				alert('网站认领成功！', '?mod=website');
			} else {
				alert('网站验证失败！');
			}
		}
		
		if ($vtype == 'meta') {
			$content = get_url_content($siteurl);
			if (preg_match('/<meta\s+name=\"iwebdir-site-verification\"\s+content=\"(.*?)\"/si', $content, $matches)) {
				if ($matches[1] == $token) {
					$DB->update($table, array('user_id' => $myself['user_id']), array('web_url' => $domain));
					alert('网站认领成功！', '?mod=website');					
				} else {
					alert('网站验证失败！');
				}
			} else {
				alert('您还未向首页添加元标记！');
			}
		}
	}
}

smarty_output($tplfile);
?>