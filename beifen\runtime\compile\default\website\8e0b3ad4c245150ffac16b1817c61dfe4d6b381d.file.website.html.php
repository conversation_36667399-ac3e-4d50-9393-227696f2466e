<?php /* Smarty version Smarty-3.1.18, created on 2023-11-07 23:46:59
         compiled from "/www/wwwroot/www.8t.lv/templet/default/website.html" */ ?>
<?php /*%%SmartyHeaderCode:2337953266548e934f13413-78982700%%*/if(!defined('SMARTY_DIR')) exit('no direct access allowed');
$_valid = $_smarty_tpl->decodeProperties(array (
  'file_dependency' => 
  array (
    '8e0b3ad4c245150ffac16b1817c61dfe4d6b381d' => 
    array (
      0 => '/www/wwwroot/www.8t.lv/templet/default/website.html',
      1 => 1699370045,
      2 => 'file',
    ),
  ),
  'nocache_hash' => '2337953266548e934f13413-78982700',
  'function' => 
  array (
  ),
  'version' => 'Smarty-3.1.18',
  'unifunc' => 'content_6548e9350a2768_87227794',
  'variables' => 
  array (
    'cate_id' => 0,
    'cate_name' => 0,
    'curpage' => 0,
    'site_name' => 0,
    'css_path' => 0,
    'site_root' => 0,
    'option' => 0,
    'js_path' => 0,
    'crumb' => 0,
    'rssfeed' => 0,
    'categories' => 0,
    'sub' => 0,
    'websites' => 0,
    'w' => 0,
    'showpage' => 0,
    'instat' => 0,
    'outstat' => 0,
    'new' => 0,
    'quick' => 0,
    'rel' => 0,
  ),
  'has_nocache_code' => false,
),false); /*/%%SmartyHeaderCode%%*/?>
<?php if ($_valid && !is_callable('content_6548e9350a2768_87227794')) {function content_6548e9350a2768_87227794($_smarty_tpl) {?><!DOCTYPE HTML>

<html>

<head>

<title><?php if ($_smarty_tpl->tpl_vars['cate_id']->value>0) {?><?php echo $_smarty_tpl->tpl_vars['cate_name']->value;?>
<?php }?>网站大全<?php if ($_smarty_tpl->tpl_vars['curpage']->value>1) {?>_第<?php echo $_smarty_tpl->tpl_vars['curpage']->value;?>
页<?php }?> - <?php echo $_smarty_tpl->tpl_vars['site_name']->value;?>
</title>

<meta charset="utf-8">

<meta name="Keywords" content="<?php echo $_smarty_tpl->tpl_vars['cate_name']->value;?>
" />

<meta name="Description" content="<?php echo $_smarty_tpl->tpl_vars['cate_name']->value;?>
网站网址大全,收集正规的中文官方网站,用户自主提交网站,8T网站分类目录努力打造互动新颖的网站分类目录导航收录平台" />
<?php echo $_smarty_tpl->getSubTemplate ("header-public.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>

<link href="<?php echo $_smarty_tpl->tpl_vars['css_path']->value;?>
default/style.css" rel="stylesheet" type="text/css" />

<script type="text/javascript">var $root = '<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
'; var $linktype = '<?php echo $_smarty_tpl->tpl_vars['option']->value['link_struct'];?>
';</script>

<script type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['js_path']->value;?>
jquery.min.js"></script>

<script type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['js_path']->value;?>
common.js"></script>

</head>



<body>

<?php echo $_smarty_tpl->getSubTemplate ("header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>


<div class="wrapper">

    <div class="crumb"><?php echo $_smarty_tpl->tpl_vars['crumb']->value;?>
&nbsp;<?php echo $_smarty_tpl->tpl_vars['rssfeed']->value;?>
</div>

    <div class="mainbox">

        <div class="mainbox-left">

            <div class="clearfix indexcatebox allcate">

                <h3 class="scatbox-title"><?php if ($_smarty_tpl->tpl_vars['cate_id']->value>0) {?><?php echo $_smarty_tpl->tpl_vars['cate_name']->value;?>
<?php }?>相关分类</h3>

                <ul class="clearfix scatbox-list">

                    <?php  $_smarty_tpl->tpl_vars['sub'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['sub']->_loop = false;
 $_from = $_smarty_tpl->tpl_vars['categories']->value; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['sub']->key => $_smarty_tpl->tpl_vars['sub']->value) {
$_smarty_tpl->tpl_vars['sub']->_loop = true;
?>

                    <?php if ($_smarty_tpl->tpl_vars['sub']->value['cate_mod']!='article') {?>

                    <li><a title="<?php echo $_smarty_tpl->tpl_vars['sub']->value['cate_name'];?>
网站大全" href="<?php echo $_smarty_tpl->tpl_vars['sub']->value['cate_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['sub']->value['cate_name'];?>
</a><em>(<?php echo $_smarty_tpl->tpl_vars['sub']->value['cate_postcount'];?>
)</em></li>

                    <?php }?>

                    <?php } ?>

                </ul>

            </div>

            <div class="blank10"></div>

            <div class="clearfix listbox allcate">

                <h1 class="listbox-title"><?php if ($_smarty_tpl->tpl_vars['cate_id']->value>0) {?><?php echo $_smarty_tpl->tpl_vars['cate_name']->value;?>
<?php }?>网站大全</h1>

                <ul class="sitelist">

                    <?php  $_smarty_tpl->tpl_vars['w'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['w']->_loop = false;
 $_from = $_smarty_tpl->tpl_vars['websites']->value; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['w']->key => $_smarty_tpl->tpl_vars['w']->value) {
$_smarty_tpl->tpl_vars['w']->_loop = true;
?>

                    <li><a class="sitelistCAO" href="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_link'];?>
" target="_blank" onclick="clickout(<?php echo $_smarty_tpl->tpl_vars['w']->value['web_id'];?>
)"><img src="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_pic'];?>
" width="120" height="95" alt="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_name'];?>
" class="thumb" onerror="javascript:this.src='/images/imglogo.jpg'"/></a><div class="info"><h3><a href="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_name'];?>
" onclick="clickout(<?php echo $_smarty_tpl->tpl_vars['w']->value['web_id'];?>
)" target="_blank"><?php echo $_smarty_tpl->tpl_vars['w']->value['web_name'];?>
</a></h3><p><?php echo $_smarty_tpl->tpl_vars['w']->value['web_description'];?>
</p><cite><?php echo $_smarty_tpl->tpl_vars['w']->value['web_utime'];?>
</cite></div></li>

                    <?php }
if (!$_smarty_tpl->tpl_vars['w']->_loop) {
?>

                    <li>该目录下无任何内容！</li>

                    <?php } ?>

                </ul>

                <div class="clearfix showpage"><?php echo $_smarty_tpl->tpl_vars['showpage']->value;?>
</div>

            </div>

        </div>

        <div class="mainbox-right">
            <div class="newbox">

                <div class="newbox-title">最新点入</div>

                <ul class="newbox-list">

                    <?php  $_smarty_tpl->tpl_vars['instat'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['instat']->_loop = false;
 $_from = get_websites($_smarty_tpl->tpl_vars['cate_id']->value,12,false,false,'instat'); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['instat']->key => $_smarty_tpl->tpl_vars['instat']->value) {
$_smarty_tpl->tpl_vars['instat']->_loop = true;
?>

                    <li><span><?php echo $_smarty_tpl->tpl_vars['instat']->value['web_ctime'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['instat']->value['web_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['instat']->value['web_name'];?>
</a></li>

                    <?php } ?>

                </ul>

            </div>
            
            <div class="blank10"></div>

            <div class="newbox">

                <div class="newbox-title">最新点出</div>

                <ul class="newbox-list">

                    <?php  $_smarty_tpl->tpl_vars['outstat'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['outstat']->_loop = false;
 $_from = get_websites($_smarty_tpl->tpl_vars['cate_id']->value,12,false,false,'outstat'); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['outstat']->key => $_smarty_tpl->tpl_vars['outstat']->value) {
$_smarty_tpl->tpl_vars['outstat']->_loop = true;
?>

                    <li><span><?php echo $_smarty_tpl->tpl_vars['outstat']->value['web_ctime'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['outstat']->value['web_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['outstat']->value['web_name'];?>
</a></li>

                    <?php } ?>

                </ul>

            </div>
            
            <div class="blank10"></div>

            <div class="newbox">

                <div class="newbox-title">最新收录</div>

                <ul class="newbox-list">

                    <?php  $_smarty_tpl->tpl_vars['new'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['new']->_loop = false;
 $_from = get_websites($_smarty_tpl->tpl_vars['cate_id']->value,12,false,false,'ctime'); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['new']->key => $_smarty_tpl->tpl_vars['new']->value) {
$_smarty_tpl->tpl_vars['new']->_loop = true;
?>

                    <li><span><?php echo $_smarty_tpl->tpl_vars['new']->value['web_ctime'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['new']->value['web_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['new']->value['web_name'];?>
</a></li>

                    <?php } ?>

                </ul>

            </div>
            
            <div class="blank10"></div>
            <div class="newbox">

                <div class="newbox-title">快审收录</div>

                <ul class="newbox-list">

                    <?php  $_smarty_tpl->tpl_vars['quick'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['quick']->_loop = false;
 $_from = get_websites(0,12,true); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['quick']->key => $_smarty_tpl->tpl_vars['quick']->value) {
$_smarty_tpl->tpl_vars['quick']->_loop = true;
?>

                    <li><span><?php echo $_smarty_tpl->tpl_vars['quick']->value['web_ctime'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['quick']->value['web_name'];?>
</a></li>

                    <?php } ?>

                </ul>

            </div>
            
            <div class="blank10"></div>
            <div class="newbox">

                <div class="newbox-title">随机网站</div>

                <ul class="newbox-list">

                    <?php  $_smarty_tpl->tpl_vars['rel'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['rel']->_loop = false;
 $_from = get_websites_lunbo($_smarty_tpl->tpl_vars['cate_id']->value,12); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['rel']->key => $_smarty_tpl->tpl_vars['rel']->value) {
$_smarty_tpl->tpl_vars['rel']->_loop = true;
?>
                        <li><span><?php echo $_smarty_tpl->tpl_vars['rel']->value['web_ctime'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['rel']->value['web_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['rel']->value['web_name'];?>
</a></li>
                    <?php } ?>
                </ul>

            </div>

        </div>  
    </div>

</div>

<?php echo $_smarty_tpl->getSubTemplate ("footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>

</body>

</html><?php }} ?>
