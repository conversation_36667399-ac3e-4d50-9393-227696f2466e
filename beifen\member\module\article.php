<?php
if (!defined('IN_MEMBER')) exit('Access Denied');

require(CORE_PATH.'module/arccate.php');
require(CORE_PATH.'module/article.php');

$page_url = '?mod=article';
$tplfile = 'article.html';
$table = $DB->table('articles');

$action = isset($_GET['act']) ? $_GET['act'] : 'list';
$smarty->assign('action', $action); 

if (!$smarty->isCached($tplfile)) {
	/** list */
	if ($action == 'list') {
		$page_name = '文章管理';
		$smarty->assign('crumb', get_crumb().' &rsaquo; '.$page_name);
		
		$pagesize = 10;
		$curpage = intval($_GET['page']);
		if ($curpage > 1) {
			$start = ($curpage - 1) * $pagesize;
		} else {
			$start = 0;
			$curpage = 1;
		}
		
		$where = "a.user_id='".$myself['user_id']."'";
	
		$articles = get_article_list($where, 'ctime', 'DESC', $start, $pagesize);
		$total = $DB->get_count($table.' a', $where);
		$showpage = showpage($page_url, $total, $curpage, $pagesize);
		
		$smarty->assign('page_name', $page_name);
		$smarty->assign('articles', $articles);
		$smarty->assign('total', $total);
		$smarty->assign('showpage', $showpage);
	}
	
	/** add */
	if ($action == 'add') {
		$page_name = '发布文章';
		
		$smarty->assign('page_name', $page_name);
		$smarty->assign('crumb', get_crumb().' &rsaquo; '.$page_name);	
		$smarty->assign('do', 'saveadd');
	}
	
	/** edit */
	if ($action == 'edit') {
		$page_name = '编辑文章';
		
		$art_id = intval($_GET['aid']);
		$where = "a.user_id='$myself[user_id]' AND a.art_id='$art_id'";
		$row = get_one_article($where);
		if (!$row) {
			alert('您要修改的内容不存在或无权限！');
		}
		
		$cate_pids = array();
		$crow = get_one_arccate($row['cate_id']);
		if ($crow) {
			$parent_cids = $crow['cate_arrparentid'].','.$row['cate_id']; #分类父ID
			if (strpos($parent_cids, ',') !== false) {
				$cate_pids = explode(',', $parent_cids);
				array_shift($cate_pids);
			} else {
				$cate_pids = (array) $parent_cids;
			}
		}
		$row['art_content'] = str_replace('[upload_dir]', $options['site_root'].$options['upload_dir'].'/article/', $row['art_content']);
		
		$smarty->assign('page_name', $page_name);
		$smarty->assign('crumb', get_crumb().' &rsaquo; '.$page_name);	
		$smarty->assign('row', $row);
		$smarty->assign('cate_pids', $cate_pids);
		$smarty->assign('do', 'saveedit');
	}
	
	/** save */
	if (in_array($_POST['do'], array('saveadd', 'saveedit'))) {
		$cate_id = intval($_POST['cate_id']);
		$art_title = trim($_POST['art_title']);
		$art_tags = addslashes(trim($_POST['art_tags']));
		$copy_from = trim($_POST['copy_from']);
		$copy_url = trim($_POST['copy_url']);
		$art_intro = strip_tags(trim($_POST['art_intro']));
		$art_content = $_POST['art_content'];
		$art_time = time();
		
		if ($cate_id <= 0) {
			alert('请选择文章所属分类！');
		} else {
			$cate = get_one_arccate($cate_id);
			if ($cate['cate_childcount'] > 0) {
				alert('指定的分类下有子分类，请选择子分类进行操作！');
			}
		}
		
		if (empty($art_title)) {
			alert('请输入文章标题！');
		} else {
			if (!censor_words($options['filter_words'], $art_title)) {
				alert('文章标题中含有非法关键词！');	
			}
		}
		
		if (empty($art_tags)) {
			alert('请输入TAG标签！');
		} else {
			if (!censor_words($options['filter_words'], $art_tags)) {
				alert('TAG标签中含有非法关键词！');
			}
			
			$art_tags = str_replace('，', ',', $art_tags);
			$art_tags = str_replace(',,', ',', $art_tags);
			if (substr($art_tags, -1) == ',') {
				$art_tags = substr($art_tags, 0, strlen($art_tags) - 1);
			}
		}
		
		if (empty($copy_from)) $copy_from = '本站原创';
		if (empty($copy_url)) $copy_url = $options['site_url'];
		
		if (empty($art_intro)) {
			alert('请输入内容摘要！');
		} else {
			if (!censor_words($options['filter_words'], $art_intro)) {
				alert('内容摘要中含有非法关键词！');	
			}
		}
		
		if (empty($art_content)) {
			alert('请输入文章内容！');
		} else {
			if (!censor_words($options['filter_words'], $art_content)) {
				alert('文章内容中含有非法关键词！');	
			}
		}
		
		if ($options['post_article_score'] > 0 && $myself['user_score'] < $options['post_article_score']) {
			alert('积分不足，请充值后再提交！', '?mod=consume&act=pay');
		}
		
		$art_content = str_replace($options['site_root'].$options['upload_dir'].'/article/', '[upload_dir]', $art_content);
		$art_ispay = $options['post_article_score'] > 0 ? 1 : 0;
		$art_status = $options['post_article_score'] > 0 ? 3 : 2;
		$art_data = array(
			'user_id' => $myself['user_id'],
			'cate_id' => $cate_id,
			'art_title' => $art_title,
			'art_tags' => $art_tags,
			'copy_from' => $copy_from,
			'copy_url' => $copy_url,
			'art_intro' => $art_intro,
			'art_content' => $art_content,
			'art_ispay' => $art_ispay,
			'art_status' => $art_status,
			'art_ctime' => $art_time,
		);
		
		if ($_POST['do'] == 'saveadd') {
    		$query = $DB->query("SELECT art_id FROM $table WHERE art_title='$art_title'");
    		if ($DB->num_rows($query)) {
        		alert('您所发布的文章已存在！');
    		}
			$DB->insert($table, $art_data);
			$insert_id = $DB->insert_id();
			
			#消费记录
			if ($options['post_article_score'] > 0) {
				$order_id = strtoupper(uniqid());
				$cons_score = $options['post_article_score'];
				$cons_data = array('user_id' => $myself['user_id'], 'order_id' => $order_id, 'cons_name' => $myself['user_email'].'：'.$constypes[3], 'cons_type' => 3, 'cons_score' => $cons_score, 'cons_status' => 4, 'cons_time' => $art_time);
				$DB->insert($DB->table('consumes'), $cons_data);
				$DB->query("UPDATE ".$DB->table('users')." SET user_score=user_score - ".$cons_score." WHERE user_id='".$myself['user_id']."' LIMIT 1");
			}
		
			alert('文章发布成功！', $page_url);	
		} elseif ($_POST['do'] == 'saveedit') {
			$art_id = intval($_POST['art_id']);
			$where = array('art_id' => $art_id);
			$DB->update($table, $art_data, $where);
			
			alert('文章编辑成功！', $page_url);
		}
	}
}

smarty_output($tplfile);
?>