<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$page_name = '最新收录';
$page_url = '?mod=update';
$tplfile = 'update.html';
$tpldir = 'update';
$table = $DB->table('websites');

/** 缓存设置 */
$smarty->compile_dir .= $tpldir;
$smarty->cache_dir .= $tpldir;
$smarty->cache_lifetime = $options['cache_time_list'] * 3600;

$pagesize = 15;
$curpage = intval($_GET['page']);
if ($curpage > 1) {
	$start = ($curpage - 1) * $pagesize;
} else {
	$start = 0;
	$curpage = 1;
}
$pagestr = ($curpage > 0) ? ' - 第'.$curpage.'页': '';
		
$setdays = intval($_GET['days']);
$cache_id = $setdays.'-'.$curpage;

if (!$smarty->isCached($tplfile, $cache_id)) {	
	$newarr = array();
	$i = 0;
	foreach ($timescope as $key => $val) {
		$newarr[$i]['time_id'] = $key;
		$newarr[$i]['time_text'] = $val;
		$newarr[$i]['time_link'] = $page_url.'&days='.$key;
		$i++;
	}
	
	$where = "w.web_status=3";
	if ($setdays > 0) {
		$strurl .= '&days='.$setdays;
		$now = time();
		switch ($setdays) {
			case 1 :
				$where .= " AND FROM_UNIXTIME(w.web_ctime, '%Y-%m-%d') = CURDATE()";
				break;
			case 3 :
				$where .= " AND DATEDIFF(CURDATE(), FROM_UNIXTIME(w.web_ctime, '%Y-%m-%d')) <= 3";
				break;
			case 7 :
				$where .= " AND DATEDIFF(CURDATE(), FROM_UNIXTIME(w.web_ctime, '%Y-%m-%d')) <= 7";
				break;
			case 30 :
				$where .= " AND FROM_UNIXTIME(w.web_ctime, '%Y%m') = DATE_FORMAT(CURDATE(), '%Y%m')";
				break;
			case 365 :
				$where .= " AND FROM_UNIXTIME(w.web_ctime, '%Y') = DATE_FORMAT(CURDATE(), '%Y')";
				break;
		}
		$crumb = get_crumb('website').' &rsaquo; <a href="'.$page_url.'">'.$page_name.'</a> &rsaquo; '.$timescope[$setdays];
	} else {
		$crumb = get_crumb('website').' &rsaquo; '.$page_name;
	}
			
	$websites = get_website_list($where, 'web_ctime', 'DESC', $start, $pagesize);
	$total = $DB->get_count($table.' w', $where);
	$showpage = showpage($page_url.$strurl, $total, $curpage, $pagesize);
			
	$smarty->assign('page_name', $page_name);
	$smarty->assign('pagestr', $pagestr);
	$smarty->assign('crumb', $crumb);
	$smarty->assign('rssfeed', get_rssfeed('website'));
	$smarty->assign('timescope', $newarr);
	$smarty->assign('timestr', $timescope[$setdays]);
	$smarty->assign('days', $setdays);
	$smarty->assign('total', $total);
	$smarty->assign('websites', $websites);
	$smarty->assign('showpage', $showpage);
}
	
smarty_output($tplfile, $cache_id);
?>