{"name": "directory-frontend", "version": "2.0.0", "description": "Modern Directory Website Frontend", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "axios": "^1.5.0", "element-plus": "^2.3.9", "@element-plus/icons-vue": "^2.1.0", "vue-i18n": "^9.4.1", "nprogress": "^0.2.0", "js-cookie": "^3.0.5", "dayjs": "^1.11.9", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "sortablejs": "^1.15.0", "vue-draggable-plus": "^0.2.5"}, "devDependencies": {"@types/js-cookie": "^3.0.3", "@types/lodash-es": "^4.17.8", "@types/node": "^20.5.1", "@types/nprogress": "^0.2.0", "@types/sortablejs": "^1.15.2", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0", "@vitejs/plugin-vue": "^4.3.4", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^11.0.3", "@vue/tsconfig": "^0.4.0", "autoprefixer": "^10.4.15", "eslint": "^8.47.0", "eslint-plugin-vue": "^9.17.0", "postcss": "^8.4.28", "prettier": "^3.0.2", "sass": "^1.66.1", "tailwindcss": "^3.3.3", "typescript": "~5.1.6", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.2", "vite": "^4.4.9", "vite-plugin-pwa": "^0.16.4", "vue-tsc": "^1.8.8"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}