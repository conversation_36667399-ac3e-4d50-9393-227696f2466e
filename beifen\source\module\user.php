<?php
/** check login */
function check_user_login($data) {
	global $DB, $usertypes;
	
	list($user_id, $user_pass, $login_count) = !empty($data) ? explode("\t", authcode($data, 'DECODE', AUTH_KEY)) : array('', '');
	$user_id = intval($user_id);
	$user_pass = addslashes($user_pass);
	$login_count = intval($login_count);
	
	$userinfo = array();
	if ($user_id && $user_pass && $login_count) {
		$row = $DB->fetch_one("SELECT user_id, user_type, user_email, user_pass, nick_name, user_qq, user_score, user_status, join_time, login_time, login_ip, login_count FROM ".$DB->table('users')." WHERE user_id='$user_id'");
		if ($row['user_pass'] == $user_pass && $row['login_count'] == $login_count) {
			$userinfo = array(
				'user_id' => $row['user_id'],
				'user_type' => $usertypes[$row['user_type']],
				'user_email' => $row['user_email'],
				'nick_name' => $row['nick_name'],
				'user_qq' => $row['user_qq'],
				'user_score' => $row['user_score'],
				'user_status' => $row['user_status'],
				'join_time' => date('Y-m-d H:i:s', $row['join_time']),
				'login_time' => date('Y-m-d H:i:s', $row['login_time']),
				'login_ip' => long2ip($row['login_ip']),
				'login_count' => $row['login_count'],
			);
		}
	}
	
	return $userinfo;
}

/** user list */
function get_user_list($where = 1, $field = 'join_time', $order = 'DESC', $start = 0, $pagesize = 0) {
	global $DB;
	
	$sql = "SELECT user_id, user_type, user_email, nick_name, user_qq, user_score, join_time, user_status FROM ".$DB->table('users')." WHERE $where ORDER BY $field $order LIMIT $start, $pagesize";
	$query = $DB->query($sql);
	$data = array();
	while ($row = $DB->fetch_array($query)) {
		$data[] = $row;
	}
	$DB->free_result($query);
		
	return $data;
}
	
/** one user */
function get_one_user($user_id = 0) {
	global $DB;
	
	$row = $DB->fetch_one("SELECT user_id, user_type, user_email, nick_name, user_qq, user_score, join_time, user_status FROM ".$DB->table('users')." WHERE user_id='$user_id' LIMIT 1");
	
	return $row;
}

/** user count */
function get_user_count($user_id = 0) {
	global $DB;
	
	$count = $DB->get_count($DB->table('users'), array('root_id' => $user_id));
	
	return $count;
}

/** user option */
function get_usertype_option($type = 'normal') {
	global $usertypes;
	
	foreach ($usertypes as $key => $val) {
		$optstr .= '<option value="'.$key.'"';
		if ($type == $key) $optstr .= ' selected';
		$optstr .= '>'.$val.'</option>';
	}
	
	return $optstr;
}
?>