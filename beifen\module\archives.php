<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$page_name = '数据归档';
$page_url = '?mod=archives';
$tplfile = 'archives.html';
$tpldir = 'archives';
$table = $DB->table('websites');

/** 缓存设置 */
$smarty->compile_dir .= $tpldir;
$smarty->cache_dir .= $tpldir;
$smarty->cache_lifetime = $options['cache_time_list'] * 3600;

$pagesize = 10;
$curpage = intval($_GET['page']);
if ($curpage > 1) {
	$start = ($curpage - 1) * $pagesize;
} else {
	$start = 0;
	$curpage = 1;
}
$pagestr = ($curpage > 0) ? ' - 第'.$curpage.'页': '';
		
$setdate = intval($_GET['date']);
$cache_id = ($setdate && strlen($setdate) == 6 ? $setdate.'-' : '').$curpage;

if (!$smarty->isCached($tplfile, $cache_id)) {	
	$where = "w.web_status=3";
	if ($setdate && strlen($setdate) == 6) {
		$page_url .= '&date='.$setdate;
		$year = substr($setdate, 0, 4);
		if ($year >= 2038 || $year <= 1970) {
			$year = gmdate('Y');
			$month = gmdate('m');
		} else {
			$month = substr($setdate, -2);
			$start_timestamp = strtotime($year.'-'.$month.'-1');
			if ($month == 12) {
				$end_year = $year + 1;
				$end_month = 1;
			} else {
				$end_year  = $year;
				$end_month = $month + 1;
			}
			$end_timestamp = strtotime($end_year.'-'.$end_month.'-1');
		}
		$datestr = $year.'年'.$month.'月';
		$where .= " AND w.web_ctime>='".$start_timestamp."' AND w.web_ctime<'".$end_timestamp."'";
		$crumb = get_crumb('website').' &rsaquo; <a href="'.$page_url.'">数据归档</a> &rsaquo; '.$datestr;
	} else {
		$crumb = get_crumb('website').' &rsaquo; '.$page_name;
	}
	
	$websites = get_website_list($where, 'web_ctime', 'DESC', $start, $pagesize);
	$total = $DB->get_count($table.' w', $where);
	$showpage = showpage($page_url, $total, $curpage, $pagesize);
			
	$smarty->assign('page_name', $page_name);
	$smarty->assign('pagestr', $pagestr);
	$smarty->assign('crumb', $crumb);
	$smarty->assign('rssfeed', get_rssfeed('website'));
	$smarty->assign('date', $setdate);
	$smarty->assign('datestr', $datestr);
	$smarty->assign('archives', get_archives());
	$smarty->assign('total', $total);
	$smarty->assign('websites', $websites);
	$smarty->assign('showpage', $showpage);
}
	
smarty_output($tplfile, $cache_id);
?>