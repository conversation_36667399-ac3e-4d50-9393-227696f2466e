<?php

require('load.php');

require(CORE_PATH.'include/upload.php');



/** check login  */

$module = $_GET['mod'] ? $_GET['mod'] : 'website';

$token = $_GET['token'];

$token = str_replace(array('@', '$'), array('+', '/'), $token);

$myself = check_admin_login($token);

if (empty($myself)) {

	echo json_encode(array('error' => 1, 'message' => '您还未登录或无权限！'));

	exit;

}



if ($module == 'website') {

	$files = $_FILES['Filedata'];

} else {

	$files = $_FILES['imgFile'];

}



$savepath = '../'.$options['upload_dir'].'/'.$module.'/'.date('Ym').'/';

$upload = new upload_file();

$upload->make_dir($savepath);

$upload->init($files, $savepath);

if ($upload->error_code == 0) {

	$filepath = $upload->attach['path'];

	if ($module == 'website') {

		$filepath = str_replace('../'.$options['upload_dir'].'/'.$module.'/', '', $upload->attach['path']);

	}

	echo json_encode(array('error' => 0, 'url' => $filepath));

	exit;

} else {

	echo json_encode(array('error' => 1, 'message' => $upload->error()));

	exit;

}

?>