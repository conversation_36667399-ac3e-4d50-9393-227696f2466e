<?php
function get_stats() {
	global $DB;
	
	if (load_cache('stats')) {
		$data = load_cache('stats');
	} else {
		$data = array();
		$data['category'] = $DB->get_count($DB->table('categories'));
		$data['website'] = $DB->get_count($DB->table('websites'));
		$data['article'] = $DB->get_count($DB->table('articles'));
		$data['apply'] = $DB->get_count($DB->table('websites'), array('web_status' => 2));
		$data['user'] = $DB->get_count($DB->table('users'), "user_type != 'admin'");
		$data['adver'] = $DB->get_count($DB->table('advers'));
		$data['link'] = $DB->get_count($DB->table('links'));
		$data['feedback'] = $DB->get_count($DB->table('feedbacks'));
		$data['label'] = $DB->get_count($DB->table('labels'));
		$data['page'] = $DB->get_count($DB->table('pages'));
	}
	
	return $data;
}
?>