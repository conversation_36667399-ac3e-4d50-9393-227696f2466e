<?php /* Smarty version Smarty-3.1.18, created on 2023-01-08 19:21:21
         compiled from "/www/wwwroot/digg58.com/templet/default/update.html" */ ?>
<?php /*%%SmartyHeaderCode:148602047263baa73140aeb1-71532350%%*/if(!defined('SMARTY_DIR')) exit('no direct access allowed');
$_valid = $_smarty_tpl->decodeProperties(array (
  'file_dependency' => 
  array (
    '01100bf5572b2b806bd4b57b882446763ea79f32' => 
    array (
      0 => '/www/wwwroot/digg58.com/templet/default/update.html',
      1 => 1647173772,
      2 => 'file',
    ),
  ),
  'nocache_hash' => '148602047263baa73140aeb1-71532350',
  'function' => 
  array (
  ),
  'variables' => 
  array (
    'days' => 0,
    'timestr' => 0,
    'page_name' => 0,
    'site_name' => 0,
    'total' => 0,
    'css_path' => 0,
    'site_root' => 0,
    'option' => 0,
    'js_path' => 0,
    'crumb' => 0,
    'rssfeed' => 0,
    'timescope' => 0,
    'item' => 0,
    'websites' => 0,
    'w' => 0,
    'img_path' => 0,
    'showpage' => 0,
    'cate_id' => 0,
    'instat' => 0,
    'outstat' => 0,
    'new' => 0,
    'quick' => 0,
    'cate' => 0,
    'rel' => 0,
  ),
  'has_nocache_code' => false,
  'version' => 'Smarty-3.1.18',
  'unifunc' => 'content_63baa731561400_45728855',
),false); /*/%%SmartyHeaderCode%%*/?>
<?php if ($_valid && !is_callable('content_63baa731561400_45728855')) {function content_63baa731561400_45728855($_smarty_tpl) {?><!DOCTYPE HTML>

<html>

<head>

<title><?php if ($_smarty_tpl->tpl_vars['days']->value>0) {?><?php echo $_smarty_tpl->tpl_vars['timestr']->value;?>
网站收录详情<?php } else { ?><?php echo $_smarty_tpl->tpl_vars['page_name']->value;?>
<?php }?> - <?php echo $_smarty_tpl->tpl_vars['site_name']->value;?>
</title>

<meta charset="utf-8">

<meta name="Keywords" content="网站免费收录，网站免费提交/登录" />

<meta name="Description" content="<?php if ($_smarty_tpl->tpl_vars['days']->value>0) {?><?php echo $_smarty_tpl->tpl_vars['site_name']->value;?>
<?php echo $_smarty_tpl->tpl_vars['timestr']->value;?>
共收录网站<?php echo $_smarty_tpl->tpl_vars['total']->value;?>
个。<?php } else { ?>让你及时了解最新网站收录详情，可按时间段（最近24小时、三天内、一星期、一个月、一年、所有时间）查询，让你及时了解网站在某一时间段内的收录情况。<?php }?>" />

<link href="<?php echo $_smarty_tpl->tpl_vars['css_path']->value;?>
default/style.css" rel="stylesheet" type="text/css" />

<script type="text/javascript">var $root = '<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
'; var $linktype = '<?php echo $_smarty_tpl->tpl_vars['option']->value['link_struct'];?>
';</script>

<script type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['js_path']->value;?>
jquery.min.js"></script>

<script type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['js_path']->value;?>
common.js"></script>
<?php echo $_smarty_tpl->getSubTemplate ("header-public.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>

</head>



<body>

<?php echo $_smarty_tpl->getSubTemplate ("header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>


<div class="wrapper">

	<div class="crumb"><?php echo $_smarty_tpl->tpl_vars['crumb']->value;?>
&nbsp;<?php echo $_smarty_tpl->tpl_vars['rssfeed']->value;?>
</div>

	<div class="mainbox">

		<div class="mainbox-left">

            <div class="clearfix listbox allcate">

            	<h3 class="listbox-title"><span class="timelink"><?php  $_smarty_tpl->tpl_vars['item'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['item']->_loop = false;
 $_from = $_smarty_tpl->tpl_vars['timescope']->value; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['item']->key => $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->_loop = true;
?><a href="<?php echo $_smarty_tpl->tpl_vars['item']->value['time_link'];?>
"<?php if ($_smarty_tpl->tpl_vars['item']->value['time_id']==$_smarty_tpl->tpl_vars['days']->value) {?> class="timelink_bg"<?php }?>><?php echo $_smarty_tpl->tpl_vars['item']->value['time_text'];?>
</a><?php } ?></span><?php if ($_smarty_tpl->tpl_vars['days']->value>0) {?><?php echo $_smarty_tpl->tpl_vars['timestr']->value;?>
网站收录详情<?php } else { ?><?php echo $_smarty_tpl->tpl_vars['page_name']->value;?>
<?php }?></h3>

            	<ul class="sitelist">

					<?php  $_smarty_tpl->tpl_vars['w'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['w']->_loop = false;
 $_from = $_smarty_tpl->tpl_vars['websites']->value; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['w']->key => $_smarty_tpl->tpl_vars['w']->value) {
$_smarty_tpl->tpl_vars['w']->_loop = true;
?>

                	<li><a href="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_link'];?>
" target="_blank"><img src="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_pic'];?>
" width="110" height="95" alt="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_name'];?>
" class="thumb" /></a><div class="info"><h3><a href="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_name'];?>
" target="_blank"><?php echo $_smarty_tpl->tpl_vars['w']->value['web_name'];?>
</a> <?php if ($_smarty_tpl->tpl_vars['w']->value['web_ispay']==1) {?><img src="<?php echo $_smarty_tpl->tpl_vars['img_path']->value;?>
attr/audit.gif" border="0"><?php }?> <?php if ($_smarty_tpl->tpl_vars['w']->value['web_istop']==1) {?><img src="<?php echo $_smarty_tpl->tpl_vars['img_path']->value;?>
attr/top.gif" border="0"><?php }?> <?php if ($_smarty_tpl->tpl_vars['w']->value['web_isbest']==1) {?><img src="<?php echo $_smarty_tpl->tpl_vars['img_path']->value;?>
attr/best.gif" border="0"><?php }?></h3><p><?php echo $_smarty_tpl->tpl_vars['w']->value['web_intro'];?>
</p><cite><?php echo $_smarty_tpl->tpl_vars['w']->value['web_ctime'];?>
</cite></div></li>

                	<?php }
if (!$_smarty_tpl->tpl_vars['w']->_loop) {
?>

                	<li>该目录下无任何内容！</li>

                	<?php } ?>

				</ul>

            	<div class="clearfix showpage"><?php echo $_smarty_tpl->tpl_vars['showpage']->value;?>
</div>

            </div>

		</div>

		<div class="mainbox-right">
		<div class="newbox">

            	<div class="newbox-title">最新点入</div>

                <ul class="newbox-list">

                	<?php  $_smarty_tpl->tpl_vars['instat'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['instat']->_loop = false;
 $_from = get_websites($_smarty_tpl->tpl_vars['cate_id']->value,11,false,false,'instat'); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['instat']->key => $_smarty_tpl->tpl_vars['instat']->value) {
$_smarty_tpl->tpl_vars['instat']->_loop = true;
?>

					<li><span><?php echo $_smarty_tpl->tpl_vars['instat']->value['web_ctime'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['instat']->value['web_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['instat']->value['web_name'];?>
</a></li>

                   	<?php } ?>

                </ul>

            </div>
			
			<div class="blank10"></div>

			<div class="newbox">

            	<div class="newbox-title">最新点出</div>

                <ul class="newbox-list">

                	<?php  $_smarty_tpl->tpl_vars['outstat'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['outstat']->_loop = false;
 $_from = get_websites($_smarty_tpl->tpl_vars['cate_id']->value,11,false,false,'outstat'); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['outstat']->key => $_smarty_tpl->tpl_vars['outstat']->value) {
$_smarty_tpl->tpl_vars['outstat']->_loop = true;
?>

					<li><span><?php echo $_smarty_tpl->tpl_vars['outstat']->value['web_ctime'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['outstat']->value['web_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['outstat']->value['web_name'];?>
</a></li>

                   	<?php } ?>

                </ul>

            </div>
			<div class="blank10"></div>
			<div class="newbox">

            	<div class="newbox-title">最新收录</div>

                <ul class="newbox-list">

                	<?php  $_smarty_tpl->tpl_vars['new'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['new']->_loop = false;
 $_from = get_websites(0,11,false,false,'ctime'); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['new']->key => $_smarty_tpl->tpl_vars['new']->value) {
$_smarty_tpl->tpl_vars['new']->_loop = true;
?>

					<li><span><?php echo $_smarty_tpl->tpl_vars['new']->value['web_ctime'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['new']->value['web_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['new']->value['web_name'];?>
</a></li>

                   	<?php } ?>

                </ul>

            </div>
			
			<div class="blank10"></div>
			<div class="newbox">

            	<div class="newbox-title">快审收录</div>

                <ul class="newbox-list">

					<?php  $_smarty_tpl->tpl_vars['quick'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['quick']->_loop = false;
 $_from = get_websites(0,11,true); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['quick']->key => $_smarty_tpl->tpl_vars['quick']->value) {
$_smarty_tpl->tpl_vars['quick']->_loop = true;
?>

					<li><span><?php echo $_smarty_tpl->tpl_vars['quick']->value['web_ctime'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['quick']->value['web_name'];?>
</a></li>

                   	<?php } ?>

                </ul>

            </div>
			
			<div class="blank10"></div>
			<div class="newbox">

            	<div class="newbox-title">随机站点</div>

                <ul class="newbox-list">

                	<?php  $_smarty_tpl->tpl_vars['rel'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['rel']->_loop = false;
 $_from = get_websites_lunbo($_smarty_tpl->tpl_vars['cate']->value['cate_id'],11); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['rel']->key => $_smarty_tpl->tpl_vars['rel']->value) {
$_smarty_tpl->tpl_vars['rel']->_loop = true;
?>
						<li><span><?php echo $_smarty_tpl->tpl_vars['rel']->value['web_ctime'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['rel']->value['web_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['rel']->value['web_name'];?>
</a></li>
					<?php } ?>

                </ul>

            </div>
		</div>

	</div>

</div>

<?php echo $_smarty_tpl->getSubTemplate ("footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>


</body>

</html><?php }} ?>
