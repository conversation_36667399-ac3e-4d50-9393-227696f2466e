<?php /* Smarty version Smarty-3.1.18, created on 2023-01-30 11:38:39
         compiled from "/www/wwwroot/digg58.com/templet/member/consume.html" */ ?>
<?php /*%%SmartyHeaderCode:205113667163d73bbf89cb27-12509805%%*/if(!defined('SMARTY_DIR')) exit('no direct access allowed');
$_valid = $_smarty_tpl->decodeProperties(array (
  'file_dependency' => 
  array (
    '8f0384573d72568cf21c30a4971849ff2d8ef2f4' => 
    array (
      0 => '/www/wwwroot/digg58.com/templet/member/consume.html',
      1 => 1418038320,
      2 => 'file',
    ),
  ),
  'nocache_hash' => '205113667163d73bbf89cb27-12509805',
  'function' => 
  array (
  ),
  'variables' => 
  array (
    'page_name' => 0,
    'action' => 0,
    'consumes' => 0,
    'row' => 0,
    'site_root' => 0,
    'showpage' => 0,
    'order_id' => 0,
    'myself' => 0,
  ),
  'has_nocache_code' => false,
  'version' => 'Smarty-3.1.18',
  'unifunc' => 'content_63d73bbf922687_10215966',
),false); /*/%%SmartyHeaderCode%%*/?>
<?php if ($_valid && !is_callable('content_63d73bbf922687_10215966')) {function content_63d73bbf922687_10215966($_smarty_tpl) {?><?php echo $_smarty_tpl->getSubTemplate ("header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>




<div class="title"><?php echo $_smarty_tpl->tpl_vars['page_name']->value;?>
<span style="color: #f00; font: normal 12px normal; padding-left: 20px;">说明：由于支付宝不提供确认收货接口，因此确认收货需要登录到支付宝官网进行操作！</span></div>

<div class="content">

	<?php if ($_smarty_tpl->tpl_vars['action']->value=='list') {?>

	<div class="listbox">

		<table width="100%" border="0" cellspacing="1" cellpadding="0">

			<tr>

				<th>ID</th>

				<th>订单编号</th>

				<th>消费类型</th>

				<th>金 额</th>

				<th>积 分</th>

				<th>日期时间</th>

				<th>操作状态</th>

			</tr>

			<?php  $_smarty_tpl->tpl_vars['row'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['row']->_loop = false;
 $_from = $_smarty_tpl->tpl_vars['consumes']->value; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['row']->key => $_smarty_tpl->tpl_vars['row']->value) {
$_smarty_tpl->tpl_vars['row']->_loop = true;
?>

			<tr>

				<td><?php echo $_smarty_tpl->tpl_vars['row']->value['cons_id'];?>
</td>

				<td><?php echo $_smarty_tpl->tpl_vars['row']->value['order_id'];?>
</td>

				<td><?php echo $_smarty_tpl->tpl_vars['row']->value['cons_type'];?>
</td>

				<td>￥<?php echo $_smarty_tpl->tpl_vars['row']->value['cons_money'];?>
</td>

				<td><?php echo $_smarty_tpl->tpl_vars['row']->value['cons_score'];?>
</td>

				<td><?php echo $_smarty_tpl->tpl_vars['row']->value['cons_time'];?>
</td>

				<td><?php echo $_smarty_tpl->tpl_vars['row']->value['cons_attr'];?>
</td>

			</tr>

			<?php }
if (!$_smarty_tpl->tpl_vars['row']->_loop) {
?>

			<tr><td colspan="6">还没有任何消费记录！<a href="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
member/?mod=consume&act=pay">我要充值>></a></td></tr>

			<?php } ?>

		</table>

	</div>

	<div class="showpage"><?php echo $_smarty_tpl->tpl_vars['showpage']->value;?>
</div>

	<?php }?>

	

	<?php if ($_smarty_tpl->tpl_vars['action']->value=='pay') {?>

	<div class="formbox">

		<form name="myfrom" id="myfrom" method="post" target="_blank" action="?mod=payment" onSubmit="return checkform();">

		<ul>

			<li style="line-height: 30px;"><strong>支付方式：</strong><img src="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
public/images/alipay.png" /></li>

			<li><strong>充值金额：</strong><input type="text" name="price" id="price" class="ipt" size="10" maxlength="10" value="10" /> 元 <span id="error" style="color: #999;">充值金额不能低于10元</span></li>

			<li><strong>&nbsp;</strong><input type="hidden" name="orderid" value="<?php echo $_smarty_tpl->tpl_vars['order_id']->value;?>
"><input type="hidden" name="subject" value="<?php echo $_smarty_tpl->tpl_vars['myself']->value['user_email'];?>
：积分充值"><input type="submit" class="btn" value="立即充值"></li>

		</ul>

		</form>

	</div>

	<script type="text/javascript">

	function checkform() {

		var $price = parseInt($('#price').val());

		var $rep = /^[1-9]+[0-9]*]*$/;

		if (!$rep.test($price)) {

			$('#error').html('<font color="#ff0000">请输入整数如10元，不能输入0.01元！</font>');

			return false;

		} else {

			if ($price < 10) {

				$('#error').html('<font color="#ff0000">充值金额不能低于10元！</font>');

				return false;

			}

		}

		return true;

	}

	</script>

	<?php }?>

</div>

            

<?php echo $_smarty_tpl->getSubTemplate ("footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>
<?php }} ?>
