<?php
//File name: category_58.php
//Creation time: 2023-11-07 01:09:37

if (!defined('IN_IWEBDIR')) exit('Access Denied');

$static_data = array(
	'cate_id' => '58',
	'root_id' => '3',
	'cate_mod' => 'website',
	'cate_name' => '数码',
	'cate_dir' => 'shuma',
	'cate_url' => '',
	'cate_isbest' => '0',
	'cate_keywords' => '数码，数码网址大全',
	'cate_description' => '数码网址大全是为您精心挑选出国内外最优秀的数码网站，并保持定期更新和检查，确保您以最安全、最方便的方式，找到您的需要；数码网址大全下设分类：数码综合，数码论坛，数码影像，数码相关。',
	'cate_arrparentid' => '0,3',
	'cate_arrchildid' => '58',
	'cate_childcount' => '0',
	'cate_postcount' => '96',
);
?>