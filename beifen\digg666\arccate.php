<?php

require('common.php');

require(CORE_PATH.'module/arccate.php');



$fileurl = 'arccate.php';

$tplfile = 'arccate.html';

$table = $DB->table('arccate');



$root_id = intval($_GET['root_id']);

$smarty->assign('root_id', $root_id);

if (!isset($action)) $action = 'list';



/** list */

if ($action == 'list') {

	$page_name = '分类列表';

	

	$sql = "SELECT cate_id, cate_name, cate_dir, cate_url, cate_isbest, cate_sort, cate_childcount, cate_postcount FROM $table WHERE root_id='$root_id' ORDER BY cate_sort ASC";

	$query = $DB->query($sql);

	$categories = array();

	while ($row = $DB->fetch_array($query)) {

		$cate_attr = empty($row['cate_url']) ? '<span class="gre">内部</span>' : '<span class="red">外部</span>';

		$cate_attr .= $row['cate_isbest'] != 0 ? ' - <span class="gre">推荐</span>' : '';

		$row['cate_attr'] = $cate_attr;

		$row['cate_oper'] = '<a href="'.$fileurl.'?root_id='.$row['cate_id'].'">进入子类</a>&nbsp;|&nbsp;<a href="'.$fileurl.'?act=add&root_id='.$row['cate_id'].'">添加子类</a>&nbsp;|&nbsp;<a href="'.$fileurl.'?act=edit&cate_id='.$row['cate_id'].'">编辑</a>&nbsp;|&nbsp;<a href="'.$fileurl.'?act=clear&cate_id='.$row['cate_id'].'" onClick="return confirm(\'注：该操作将清空此分类及其子分类下的内容！\n\n确定清空吗？\');">清空</a>&nbsp;|&nbsp;<a href="'.$fileurl.'?act=del&cate_id='.$row['cate_id'].'" onClick="return confirm(\'注：该操作将同时删除此分类下的子分类及相关内容！\n\n确定删除吗？\');">删除</a>';

		$categories[] = $row;

	}

	$DB->free_result($query);

	

	$smarty->assign('root_id', $root_id);

	$smarty->assign('categories', $categories);

}



/** add */

if ($action == 'add') {

	$page_name = '添加分类';

	$category_option = get_arccate_option(0, $root_id, 0);

	

	$smarty->assign('category_option', $category_option);

	$smarty->assign('h_action', 'saveadd');

}



/** edit */

if ($action == 'edit') {

	$page_name = '编辑分类';

	

	$cate_id = intval($_GET['cate_id']);

	$row = get_one_arccate($cate_id);;

	if (!$row) {

		alert('指定的内容不存在！');

	}

	$category_option = get_arccate_option(0, $row['root_id'], 0);

	

	$smarty->assign('category_option', $category_option);

	$smarty->assign('row', $row);

	$smarty->assign('h_action', 'saveedit');

}



/** reset */

if ($action == 'reset') {

	$page_name = '复位分类';

			

	$smarty->assign('h_action', 'savereset');

}



/** merge */

if ($action == 'merge') {

	$page_name = '合并分类';

	$category_option = get_arccate_option(0, 0, 0);

	

	$smarty->assign('category_option', $category_option);

	$smarty->assign('h_action', 'saveunite');

}



/** save data */

if (in_array($action, array('saveadd', 'saveedit'))) {

	$root_id = intval(trim($_POST['root_id']));

	$cate_name = trim($_POST['cate_name']);

	$cate_dir = trim($_POST['cate_dir']);

	$cate_url = trim($_POST['cate_url']);

	$cate_isbest = intval($_POST['cate_isbest']);

	$cate_sort = intval($_POST['cate_sort']);

	$cate_keywords = trim($_POST['cate_keywords']);

	$cate_description = trim($_POST['cate_description']);

	

	if (empty($cate_name)) {

		alert('请输入分类名称！');

	}

	

	if (!empty($cate_dir)) {

		if (!is_valid_dir($cate_dir)) {

			alert('目录名称只能是由英文字母开头，数字，中划线及下划线组成！');

		}

	}

	

	$data = array(

		'root_id' => $root_id,

		'cate_name' => $cate_name,

		'cate_dir' => $cate_dir,

		'cate_url' => $cate_url,

		'cate_isbest' => $cate_isbest,

		'cate_sort' => $cate_sort,

		'cate_keywords' => $cate_keywords,

		'cate_description' => $cate_description,

	);

	

	if ($action == 'saveadd') {

    	$query = $DB->query("SELECT cate_id FROM $table WHERE root_id='$root_id' AND cate_name='$cate_name'");

    	if ($DB->num_rows($query)) {

        	alert('您所添加的分类已存在！');

    	}

		$DB->insert($table, $data);

		update_categories();

		update_cache('arccate');

		

		$fileurl = empty($root_id) ? $fileurl .= '?act=add' : $fileurl .= '?act=add&root_id='.$root_id;

		redirect($fileurl);

	} elseif ($action == 'saveedit') {

		$cate_id = intval($_POST['cate_id']);

		$where = array('cate_id' => $cate_id);

		

		$DB->update($table, $data, $where);

		update_categories();

		update_cache('arccate');

		

		$fileurl = $root_id > 0 ? $fileurl .= '?root_id='.$root_id : $fileurl;

		redirect($fileurl);

	}

}



/** del */

if ($action == 'del') {

	$cate_id = intval($_GET['cate_id']);

	

	$sql = "SELECT cate_arrchildid FROM $table WHERE cate_id='$cate_id'";

	$row = $DB->fetch_one($sql);

	if (!$row) {

		alert('指定的分类不存在！');

	}

	

	$DB->delete($table, 'cate_id IN ('.$row['cate_arrchildid'].')');

	$DB->delete($DB->table('websites'), 'cate_id IN ('.$row['cate_arrchildid'].')');

	update_categories();

	update_cache('arccate');

	

	redirect($fileurl);

}



/** clear */

if ($action == 'clear') {

	$cate_id = intval($_GET['cate_id']);

	

	$sql = "SELECT cate_arrchildid FROM $table WHERE cate_id='$cate_id'";

	$row = $DB->fetch_one($sql);

	if (!$row) {

		alert('指定的分类不存在！');

	}

	

	$DB->delete($DB->table('websites'), 'cate_id IN ('.$row['cate_arrchildid'].')');

	update_categories();

	

	alert('指定分类下的内容已清空！', $fileurl);

}



/** reset */

if ($action == 'savereset') {

	$DB->update($table, array('root_id' => 0));

	update_categories();

	update_cache('arccate');

	

	alert('分类复位成功，请重新对分类进行归属设置！', $fileurl);

}



/** unite */

if ($action == 'saveunite') {

	$current_cate_id = (int) $_POST['current_cate_id'];

	$target_cate_id = (int) $_POST['target_cate_id'];

	

	if (empty($current_cate_id)) {

		alert('请选择要合并的分类！');

	}

	

	if (empty($target_cate_id)) {

		alert('请选择目标分类！');

	}

	

	if ($current_cate_id == $target_cate_id) {

		alert('请不要在相同的分类内操作！');

	}

	

	$sql = "SELECT cate_childcount FROM $table WHERE cate_id='$target_cate_id'";

	$row = $DB->fetch_one($sql);

	if (!$row) {

		alert('指定的目标分类不存在！');

	}

	

	if ($row['cate_childcount'] > 0) {

		alert('目标分类中含有子分类，不能进行操作！');

	}

	

	$DB->delete($table, array('cate_id' => $current_cate_id));

	$DB->update($DB->table('websites'), array('cate_id' => $target_cate_id), array('cate_id' => $current_cate_id));

	update_categories();

	update_cache('arccate');

	

	alert('分类合并成功，且内容已转移到目标分类中！', $fileurl);

}



function update_categories() {

	global $DB, $table, $category;

	

	$sql = "SELECT cate_id FROM $table ORDER BY cate_id ASC";

	$cate_ids = $DB->fetch_all($sql);

	

	

	

	foreach ($cate_ids as $id) {

		$parent_id = get_arccate_parent_ids($id['cate_id']);

		$child_id = $id['cate_id'].get_arccate_child_ids($id['cate_id']);

		$child_count = get_arccate_count($id['cate_id']);

		$post_count = $DB->get_count($DB->table('articles'), 'cate_id IN ('.$child_id.')');

		

		$data = array(

			'cate_arrparentid' => $parent_id,

			'cate_arrchildid' => $child_id,

			'cate_childcount' => $child_count,

			'cate_postcount' => $post_count,

		);

		$where = array('cate_id' => $id['cate_id']);

		

		$DB->update($table, $data, $where);

	}

}



smarty_output($tplfile);

?>