<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$page_name = '网站大全';
$page_url = '?mod=website';
$tplfile = 'website.html';
$tpldir = 'website';
$cate_keywords = "网站大全,网址大全";
$cate_description = "8T网站大全 - 汇集各行业优秀的网站网址，覆盖娱乐休闲、生活资讯、电脑网络、教育学习、企业黄页等等，提供高质量网站提交免费收录服务！";
$table = $DB->table('websites');

/** 缓存设置 */
$smarty->compile_dir .= $tpldir;
$smarty->cache_dir .= $tpldir;
$smarty->cache_lifetime = $options['cache_time_list'] * 3600;

$pagesize = 15;
$curpage = intval($_GET['page']);
if ($curpage > 1) {
	$start = ($curpage - 1) * $pagesize;
} else {
	$start = 0;
	$curpage = 1;
}
$pagestr = ($curpage > 0) ? ' - 第'.$curpage.'页': '';
		
$cate_id = intval($_GET['cid']);
$cache_id = $cate_id.'-'.$curpage;

if (!$smarty->isCached($tplfile, $cache_id)) {
	$page_url .= '&cid='.$cate_id;
	$where = "w.web_status=3";
	if ($cate_id > 0) {
		$crow = get_one_category($cate_id);	
		$cate_id = $crow['cate_id'];
		$cate_name = $crow['cate_name'];
		$cate_keywords = $crow['cate_keywords'];
		$cate_description = $crow['cate_description'];		

		if ($crow['cate_childcount'] > 0) {
			$where .= " AND w.cate_id IN (".$crow['cate_arrchildid'].")";
			$categories = get_categories($crow['cate_id']);
		} else {
			$where .= " AND w.cate_id='".$crow['cate_id']."'";
			$categories = get_categories($crow['root_id']);
		}
		
		$crumb = get_crumb($crow['cate_mod'], $crow['cate_id'].','.$crow['cate_arrparentid']);
		$rssfeed = get_rssfeed($crow['cate_mod'], $crow['cate_id']);
	} else {
		$cate_id = 0;
		$cate_name = '';
		$categories = get_categories();
		$crumb = get_crumb('website');
		$rssfeed = get_rssfeed('website');	
	}
	
	$websites = get_website_list($where, utime, 'DESC', $start, $pagesize);
	$total = $DB->get_count($table.' w', $where);
	$showpage = showpage($page_url, $total, $curpage, $pagesize);
	
	$smarty->assign('page_name', $page_name);
	$smarty->assign('pagestr', $pagestr);
	$smarty->assign('crumb', $crumb);
	$smarty->assign('rssfeed', $rssfeed);
	$smarty->assign('cate_id', $cate_id);
	$smarty->assign('cate_name', $cate_name);
	$smarty->assign('categories', $categories);
	$smarty->assign('total', $total);
	$smarty->assign('websites', $websites);
	$smarty->assign('showpage', $showpage);
	$smarty->assign('cate_keywords', $cate_keywords);
	$smarty->assign('cate_description', $cate_description);
	$smarty->assign('curpage', $curpage);

	
}

smarty_output($tplfile, $cache_id);
?>