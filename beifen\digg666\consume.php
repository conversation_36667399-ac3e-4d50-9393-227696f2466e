<?php

require('common.php');

require(CORE_PATH.'module/consume.php');



$fileurl = 'consume.php';

$tplfile = 'consume.html';

$table = $DB->table('consumes');



if (!isset($action)) $action = 'list';



/** list */

if ($action == 'list') {

	$page_name = '消费记录';

	

	$status = intval($_GET['status']);

	$user_id = intval($_GET['user_id']);

	$type_id = intval($_GET['type_id']);

	$sort = intval($_GET['sort']);

	$order = strtoupper(trim($_GET['order']));

	$keywords = addslashes(trim($_POST['keywords'] ? $_POST['keywords'] : $_GET['keywords']));

	if (empty($order)) $order = 'DESC';

	

	$page_url = $fileurl.'?status='.$status.'&user_id='.$user_id.'&type_id='.$type_id.'&sort='.$sort.'&order='.$order;

	$constype_option = get_constype_option($type_id);

	

	$smarty->assign('status', $status);

	$smarty->assign('user_id', $user_id);

	$smarty->assign('type_id', $type_id);

	$smarty->assign('sort', $sort);

	$smarty->assign('order', $order);

	$smarty->assign('constype_option', $constype_option);

	

	$where = '';

	$sql = "SELECT c.cons_id, c.user_id, c.order_id, c.trade_no, c.trade_status, c.cons_name, c.cons_type, c.cons_money, c.cons_score, c.cons_status, c.cons_time, u.nick_name FROM $table c LEFT JOIN ".$DB->table('users')." u ON c.user_id=u.user_id WHERE";

	

	if ($status > 0) {

		$where .= " c.cons_status='$status'";

	} else {

		$where .= " c.cons_status>-1";

	}

	

	if ($user_id > 0) {

		$where .= " AND c.user_id='$user_id'";

	}

	

	if ($type_id > 0) {

		$where .= " AND c.cons_type='$type_id'";

	}

	

	switch ($sort) {

		case 1 :

			$field = "c.cons_ctime";

			break;

		default :

			$field = "c.cons_id";

			break;

	}

	

	$sql .= $where." ORDER BY $field $order LIMIT $start, $pagesize";

	$query = $DB->query($sql);

	

	$consumes = array();

	while ($cons = $DB->fetch_array($query)) {

		switch ($cons['cons_status']) {

			case 1 :

				$cons_status = '等待付款';

				break;

			case 2 :

				$cons_status = '等待发货';

				break;

			case 3 :

				$cons_status = '确认收货';

				break;

			case 4 :

				$cons_status = '交易完成';

				break;

			default :

				$cons_status = '等待付款';

				break;

		}

		$cons['cons_attr'] = $cons_status;

		$cons['nick_name'] = '<a href="'.$fileurl.'?user_id='.$cons['user_id'].'">'.$cons['nick_name'].'</a>';

		$cons['cons_time'] = date('Y-m-d H:i:s', $cons['cons_time']);

		$cons['cons_opera'] = '<a href="'.$fileurl.'?act=edit&cons_id='.$cons['cons_id'].'">编辑</a>&nbsp;|&nbsp;<a href="'.$fileurl.'?act=del&cons_id='.$web['_id'].'" onClick="return confirm(\'确认删除此内容吗？\');">删除</a>';

		$consumes[] = $cons;

	}

	$DB->free_result($query);

	

	$total = $DB->get_count($table.' c', $where);

	$showpage = showpage($page_url, $total, $curpage, $pagesize);

	

	$smarty->assign('consumes', $consumes);

	$smarty->assign('showpage', $showpage);

}



/** del */

if ($action == 'del') {

	$cons_ids = (array) ($_POST['cons_id'] ? $_POST['cons_id'] : $_GET['cons_id']);

	

	$DB->delete($table, 'cons_id IN ('.dimplode($cons_ids).')');

	

	alert('消费记录删除成功！', $fileurl);

}



smarty_output($tplfile);

?>