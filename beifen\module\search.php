<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$page_name = '搜索结果';
$page_url = '?mod=search';
$tplfile = 'search.html';
$table = $DB->table('websites');

//搜索页不缓存
$smarty->caching = false;

$pagesize = 10;
$curpage = intval($_GET['page']);
if ($curpage > 1) {
	$start = ($curpage - 1) * $pagesize;
} else {
	$start = 0;
	$curpage = 1;
}
$pagestr = ($curpage > 0) ? ' - 第'.$curpage.'页': '';
		
$strtype = strtolower(trim($_GET['type']));
$keyword = addslashes(trim($_GET['query']));

if (empty($keyword)) {
	alert('请输入搜索关键字！');	
}

if (!$smarty->isCached($tplfile)) {
	$where = "w.web_status>=2";
	if (!empty($keyword)) {
		$page_url .= '&type='.$strtype.'&query='.urlencode($keyword);		
		switch ($strtype) {
			case 'name' :
				$where .= " AND w.web_name like '%$keyword%'";
				break;
			case 'url' :
				$where .= " AND w.web_url like '%$keyword%'";
				break;
			case 'tags' :
				$where .= " AND w.web_tags like '%$keyword%'";
				break;
			case 'intro' :
				$where .= " AND w.web_intro like '%$keyword%'";
			default :
				$where .= " AND w.web_name like '%$keyword%'";
				break;
		}
	}
			
	$websites = get_website_list($where, 'web_ctime', 'DESC', $start, $pagesize);
	$total = $DB->get_count($table.' w', $where);
	$showpage = showpage($page_url, $total, $curpage, $pagesize);
		
	$smarty->assign('page_name', $page_name);
	$smarty->assign('pagestr', $pagestr);
	$smarty->assign('crumb', get_crumb('website').' &rsaquo; '.$page_name.' &rsaquo; <font color="#ff0000">'.$keyword.'</font>');
	$smarty->assign('rssfeed', get_rssfeed('website'));
	$smarty->assign('keyword', $keyword);
	$smarty->assign('categories', get_categories());
	$smarty->assign('archives', get_archives());
	$smarty->assign('total', $total);
	$smarty->assign('websites', $websites);
	$smarty->assign('showpage', $showpage);
}
	
smarty_output($tplfile, $cache_id);
?>