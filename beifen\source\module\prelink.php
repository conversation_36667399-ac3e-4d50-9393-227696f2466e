<?php
/** module */
function get_module_url($module = 'index') {
	global $options;
	
	if ($module != 'index') {
		if ($options['link_struct'] == 1) {
			$strurl = $module.'.html';
		} elseif ($options['link_struct'] == 2) {
			$strurl = $module.'/';
		} elseif ($options['link_struct'] == 3) {
			$strurl = $module;
		} else {
			$strurl = '?mod='.$module;
		}
	}
	
	return $options['site_url'].$strurl;
}

/** vip */
function get_vip_url($page = 1) {
	global $options;
	
	
	$page = isset($page) && $page > 0 ? $page : 1;
	
	if ($options['link_struct'] == 1) {
		$strurl = 'vip/'.$page.'.html';
	} elseif ($options['link_struct'] == 2) {
		$strurl = 'vip/'.$page.'.html';
	} elseif ($options['link_struct'] == 3) {
		$strurl = 'vip/'.$page;
	} else {
		$strurl = '?mod=vip';
	}
	
	return $options['site_url'].$strurl;
}

/** wait */
function get_wait_url($page = 1) {
	global $options;
	
	
	$page = isset($page) && $page > 0 ? $page : 1;
	
	if ($options['link_struct'] == 1) {
		$strurl = 'wait/'.$page.'.html';
	} elseif ($options['link_struct'] == 2) {
		$strurl = 'wait/'.$page.'.html';
	} elseif ($options['link_struct'] == 3) {
		$strurl = 'wait/'.$page;
	} else {
		$strurl = '?mod=wait';
	}
	
	return $options['site_url'].$strurl;
}

/** domain */
function get_domain_url($page = 1) {
	global $options;
	
	
	$page = isset($page) && $page > 0 ? $page : 1;
	
	if ($options['link_struct'] == 1) {
		$strurl = 'domain/'.$page.'.html';
	} elseif ($options['link_struct'] == 2) {
		$strurl = 'domain/'.$page.'.html';
	} elseif ($options['link_struct'] == 3) {
		$strurl = 'domain/'.$page;
	} else {
		$strurl = '?mod=domain';
	}
	
	return $options['site_url'].$strurl;
}


/** category */
function get_category_url($cate_mod = 'webdir', $cate_id = 0, $page = 1) {
	global $options;
	
	$cate = get_one_category($cate_id);
	$cate_dir = !empty($cate['cate_dir']) ? $cate['cate_dir'] : 'category';
	$page = isset($page) && $page > 0 ? $page : 1;
	
	if ($options['link_struct'] == 1) {
		$strurl = $cate_mod.'-'.$cate_dir.'-'.$cate_id.'-'.$page.'.html';
	} elseif ($options['link_struct'] == 2) {
		$strurl = $cate_mod.'/'.$cate_dir.'-'.$cate_id.'-'.$page.'.html';
	} elseif ($options['link_struct'] == 3) {
		$strurl = $cate_mod.'/'.$cate_dir.'-'.$cate_id.'-'.$page;
	} else {
		$strurl = '?mod='.$cate_mod.'&cid='.$cate_id;
	}
	
	return $options['site_url'].$strurl;
}

/** arccate */
function get_arccate_url($cate_id = 0, $page = 1) {
	global $options;
	
	$row = get_one_arccate($cate_id);
	if ($row) {
		$cate_id = $row['cate_id'];
		$cate_dir = !empty($row['cate_dir']) ? $row['cate_dir'] : 'arccate';
	} else {
		$cate_id = 0;
		$cate_dir = 'arccate';
	}
	
	$page = isset($page) && $page > 0 ? $page : 1;
	
	if ($options['link_struct'] == 1) {
		$strurl = 'arccate-'.$cate_dir.'-'.$cate_id.'-'.$page.'.html';
	} elseif ($options['link_struct'] == 2) {
		//$strurl = 'arccate/'.$cate_dir.'-'.$cate_id.'-'.$page.'/';
		$strurl = 'arccate/'.$cate_dir.'-'.$cate_id.'-'.$page.'.html';
	} elseif ($options['link_struct'] == 3) {
		$strurl = 'arccate/'.$cate_dir.'-'.$cate_id.'-'.$page.'.html';
	} else {
		$strurl = '?mod=arccate&cid='.$cate_id;
	}
	
	return $options['site_url'].$strurl;
}

/** update */
function get_update_url($days, $page = 1) {
	global $options;
	
	$days = isset($days) && $days > 0 ? $days : 0;
	$page = isset($page) && $page > 0 ? $page : 1;
	
	if ($options['link_struct'] == 1) {
		$strurl = 'update-'.$days.'-'.$page.'.html';
	} elseif ($options['link_struct'] == 2) {
		$strurl = 'update/'.$days.'-'.$page.'.html';
	} elseif ($options['link_struct'] == 3) {
		$strurl = 'update/'.$days.'-'.$page;
	} else {
		$strurl = '?mod=update&days='.$days;
	}
	
	return $options['site_url'].$strurl;
}

/** archives */
function get_archives_url($date, $page = 1) {
	global $options;
	
	$date = isset($date) && strlen($date) == 6 ? $date : 0;
	$page = isset($page) && $page > 0 ? $page : 1;
	
	if ($options['link_struct'] == 1) {
		$strurl = 'archives-'.$date.'-'.$page.'.html';
	} elseif ($options['link_struct'] == 2) {
		$strurl = 'archives/'.$date.'-'.$page.'.html';
	} elseif ($options['link_struct'] == 3) {
		$strurl = 'archives/'.$date.'-'.$page;
	} else {
		$strurl = '?mod=archives&date='.$date;
	}
	
	return $options['site_url'].$strurl;
}

/** search */
function get_search_url($type = 'name', $query, $page = 1) {
	global $options;

	$query = isset($query) && !empty($query) ? urlencode($query) : '';
	$page = isset($page) && $page > 0 ? $page : 1;
	
	if ($options['link_struct'] == 1) {
		$strurl = 'search-'.$type.'-'.$query.'-'.$page.'.html';
	} elseif ($options['link_struct'] == 2) {
		$strurl = 'search/'.$type.'-'.$query.'-'.$page.'.html';
	} elseif ($options['link_struct'] == 3) {
		$strurl = 'search/'.$type.'-'.$query.'-'.$page;
	} else {
		$strurl = '?mod=search&type='.$type.'&query='.$query;
	}
	
	return $options['site_url'].$strurl;
}

/** website */
function get_website_url($web_id, $abs_path = false) {
	global $options;
	
	if ($options['link_struct'] == 1) {
		$strurl = 'website-'.$web_id.'.html';
	} elseif ($options['link_struct'] == 2) {
		$strurl = 'website/'.$web_id.'.html';
	} elseif ($options['link_struct'] == 3) {
		$strurl = 'website/'.$web_id;
	} else {
		$strurl = '?mod=siteinfo&wid='.$web_id;
	}
	
	return $options['site_url'].$strurl;
}

/** article */
function get_article_url($art_id) {
	global $options;
	
	if ($options['link_struct'] == 1) {
		$strurl = 'article-'.$art_id.'.html';
	} elseif ($options['link_struct'] == 2) {
		$strurl = 'article/info-'.$art_id.'.html';
	} elseif ($options['link_struct'] == 3) {
		$strurl = 'article/'.$art_id;
	} else {
		$strurl = '?mod=artinfo&aid='.$art_id;
	}
	
	return $options['site_url'].$strurl;
}

/** weblink */
function get_weblink_url($page = 1) {
	global $options;
	
	$page = isset($page) && $page > 0 ? $page : 1;
	if ($options['link_struct'] == 1) {
		$strurl = 'weblink-'.$page.'.html';
	} elseif ($options['link_struct'] == 2) {
		$strurl = 'weblink/'.$page.'.html';
	} elseif ($options['link_struct'] == 3) {
		$strurl = 'weblink/'.$page;
	} else {
		$strurl = '?mod=weblink&page='.$page;
	}
	
	return $options['site_url'].$strurl;
}

/** linkinfo */
function get_linkinfo_url($link_id, $abs_path = false) {
	global $options;
	
	if ($abs_path) {
		$url_prefix = $options['site_url'];
	} else {
		$url_prefix = $options['site_root'];
	}
	
	if ($options['link_struct'] == 1) {
		$strurl = 'linkinfo-'.$link_id.'.html';
	} elseif ($options['link_struct'] == 2) {
		$strurl = 'linkinfo/'.$link_id.'.html';
	} elseif ($options['link_struct'] == 3) {
		$strurl = 'linkinfo/'.$link_id;
	} else {
		$strurl = '?mod=linkinfo&lid='.$link_id;
	}
	
	return $options['site_url'].$strurl;
}

/** ipinfo */
function get_ipinfo_url($ipaddr = '127.0.0.1') {
	global $options;
	
	if ($options['link_struct'] == 1) {
		$strurl = 'ipinfo-'.$ipaddr.'.html';
	} elseif ($options['link_struct'] == 2) {
		$strurl = 'ipinfo/'.$ipaddr.'.html';
	} elseif ($options['link_struct'] == 3) {
		$strurl = 'ipinfo/'.$ipaddr;
	} else {
		$strurl = '?mod=ipinfo&ip='.$ipaddr;
	}
	
	return $options['site_url'].$strurl;
}


/** diypage diypage*/
function get_diypage_url($page_id) {
	global $options;
	
	if ($options['link_struct'] == 1) {
		$strurl = $options['site_root'].'diypage-'.$page_id.'.html';
	} elseif ($options['link_struct'] == 2) {
		$strurl = $options['site_root'].'diypage/'.$page_id.'.html';
	} elseif ($options['link_struct'] == 3) {
		$strurl = $options['site_root'].'diypage/'.$page_id;
	} else {
		$strurl = '?mod=diypage&pid='.$page_id;
	}
	
	return $strurl;
}




/** diypage
function get_diypage_url($page_id) {
	global $options;
	
	if ($options['link_struct'] == 1) {
		$strurl = 'diypage-'.$page_id.'.html';
	} elseif ($options['link_struct'] == 2) {
		$strurl = 'diypage/'.$page_id.'.html';
	} elseif ($options['link_struct'] == 3) {
		$strurl = 'diypage/'.$page_id;
	} else {
		$strurl = '?mod=diypage&pid='.$page_id;
	}
	
	return $options['site_url'].$strurl;
} */

/** rssfeed */
function get_rssfeed_url($module, $cate_id) {
	global $options;
	
	if ($cate_id > 0) {
		if ($options['link_struct'] == 1) {
			$strurl = 'rssfeed-'.$module.'-'.$cate_id.'.html';
		} elseif ($options['link_struct'] == 2) {
			$strurl = 'rssfeed/'.$module.'-'.$cate_id.'.html';
		} elseif ($options['link_struct'] == 3) {
			$strurl = 'rssfeed/'.$module.'-'.$cate_id;
		} else {
			$strurl = '?mod=rssfeed&type='.$module.'&cid='.$cate_id;
		}
	} else {
		if ($options['link_struct'] == 1) {
			$strurl = 'rssfeed-'.$module.'.html';
		} elseif ($options['link_struct'] == 2) {
			$strurl = 'rssfeed/'.$module.'.html';
		} elseif ($options['link_struct'] == 3) {
			$strurl = 'rssfeed/'.$module;
		} else {
			$strurl = '?mod=rssfeed&type='.$module;
		}
	}
	
	return $options['site_url'].$strurl;
}

/** sitemap */
function get_sitemap_url($module, $cate_id) {
	global $options;
	
	if ($cate_id > 0) {
		if ($options['link_struct'] == 1) {
			$strurl = 'sitemap-'.$module.'-'.$cate_id.'.html';
		} elseif ($options['link_struct'] == 2) {
			$strurl = 'sitemap/'.$module.'-'.$cate_id.'.html';
		} elseif ($options['link_struct'] == 3) {
			$strurl = 'sitemap/'.$module.'-'.$cate_id;
		} else {
			$strurl = '?mod=sitemap&type='.$module.'&cid='.$cate_id;
		}
	} else {
		if ($options['link_struct'] == 1) {
			$strurl = 'sitemap-'.$module.'.html';
		} elseif ($options['link_struct'] == 2) {
			$strurl = 'sitemap/'.$module.'.html';
		} elseif ($options['link_struct'] == 3) {
			$strurl = 'sitemap/'.$module;
		} else {
			$strurl = '?mod=sitemap&type='.$module;
		}
	}
	
	return $options['site_url'].$strurl;
}

/** thumbs */
function get_webthumb($web_url, $web_pic) {
	global $options;
	
	if (!empty($web_pic)) {
		//$strurl = (!empty($options['upload_url']) ? $options['upload_url'] : '').$options['site_root'].$options['upload_dir'].'/website/'.$web_pic;
      //$strurl = 'https://blinky.nemui.org/shot?http://'.$web_url;
       $strurl = 'https://s0.wordpress.com/mshots/v1/http://'.$web_url.'/?w=600&h=450';
    //$strurl = '/images/slt.jpg';
	} else {
		//$strurl = $options['site_root'].'Images/defaultpic.gif';
		//$strurl = 'https://blinky.nemui.org/shot?http://'.$web_url;
       $strurl = 'https://s0.wordpress.com/mshots/v1/http://'.$web_url.'/?w=600&h=450';
		//$strurl = 'http://api.webthumbnail.org/?width=480&height=330&screen=1280&url='.$web_url;
		//$strurl = '/images/slt.jpg';
	}
	
	return $strurl;
}
?>