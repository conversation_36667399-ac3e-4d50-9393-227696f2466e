<?php

require('common.php');



$fileurl = 'option.php';

$tplfile = 'option.html';

$table = $DB->table('options');



$option = $_GET['opt'] ? $_GET['opt'] : $_POST['opt'];

if (!isset($option)) $option = 'basic';

$fileurl .= '?opt='.$option;



if (in_array($option, array('basic', 'misc', 'user', 'payment', 'score', 'cache', 'link', 'mail'))) {

	switch ($option) {

		case 'basic' :

			$page_name = '站点信息';

			break;

		case 'misc' :

			$page_name = '选项设置';

			break;

		case 'user' :

			$page_name = '会员设置';

			break;

		case 'payment' :

			$page_name = '支付设置';

			break;

		case 'score' :

			$page_name = '积分设置';

			break;

		case 'cache' :

			$page_name = '缓存设置';

			break;

		case 'link' :

			$page_name = '链接设置';

			break;

		case 'mail' :

			$page_name = '邮件设置';

			break;

		default :

			$page_name = '站点信息';

			break;

	}

	

	$configs = $options;

	$configs['site_root'] = str_replace('\\', '/', dirname($site_root));

	

	$smarty->assign('page_name', $page_name);

	$smarty->assign('option', $option);

	$smarty->assign('cfg', $configs);

	

	if ($action == 'update') {

		foreach ($_POST['cfg'] as $cname => $cval) {

			if (!empty($cval) && ($cname == 'site_url' || $cname == 'upload_url')) $cval .= (substr($cval, -1) != '/') ? '/' : '';

			if ($cname == 'data_update_cycle' && $cval <= 0) $cval = 3;

			if ($cname == 'filter_words') {

				$cval = str_replace('，', ',', $cval);

				$cval = str_replace(',,', ',', $cval);

				if (substr($cval, -1) == ',') {

					$cval = substr($cval, 0, strlen($cval) - 1);

				}

			}

			

			$udata = array('option_value' => $cval);

			$uwhere = array('option_name' => $cname);

			$idata = array('option_name' => $cname, 'option_value' => $cval);

			

			$DB->fetch_one("SELECT option_name FROM $table WHERE option_name = '$cname'") ? $DB->update($table, $udata, $uwhere) : $DB->insert($table, $idata);

		}

		update_cache('options');

		

		alert('更新系统配置成功！', $fileurl);

	}

}



smarty_output($tplfile);

?>