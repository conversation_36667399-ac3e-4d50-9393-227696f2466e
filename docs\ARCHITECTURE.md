# 现代化分类目录系统架构设计

## 系统概述

本项目是对传统分类目录网站的全面重构，采用现代化技术栈和架构设计，提供更好的用户体验、更强的功能和更高的性能。

## 技术栈选择

### 前端技术栈
- **框架**: Vue.js 3 + TypeScript
- **UI组件库**: Element Plus / Ant Design Vue
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **构建工具**: Vite
- **CSS预处理器**: SCSS
- **移动端**: PWA + 响应式设计

### 后端技术栈
- **语言**: PHP 8.1+ / Node.js (可选)
- **框架**: Laravel 10 / Symfony 6
- **API**: RESTful API + GraphQL (可选)
- **认证**: JWT + OAuth 2.0
- **文档**: OpenAPI/Swagger

### 数据库技术栈
- **主数据库**: MySQL 8.0
- **缓存**: Redis 7.0
- **搜索引擎**: Elasticsearch 8.0
- **文件存储**: 阿里云OSS / AWS S3

### 基础设施
- **容器化**: Docker + Docker Compose
- **Web服务器**: Nginx
- **CI/CD**: GitHub Actions / GitLab CI
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack

## 系统架构设计

### 1. 微服务架构
采用微服务架构，将系统拆分为多个独立的服务：

#### 用户服务 (User Service)
- 用户注册、登录、认证
- 用户信息管理
- 权限控制
- 社交登录集成

#### 网站管理服务 (Website Service)
- 网站提交和审核
- 网站信息管理
- 网站状态监控
- 批量操作

#### 分类服务 (Category Service)
- 分类层级管理
- 分类信息维护
- 分类统计
- 分类推荐

#### 搜索服务 (Search Service)
- 全文搜索
- 高级筛选
- 搜索建议
- 搜索统计

#### 推荐服务 (Recommendation Service)
- 智能推荐算法
- 个性化推荐
- 热门推荐
- 相关推荐

#### 统计服务 (Analytics Service)
- 访问统计
- 用户行为分析
- 数据报表
- 实时监控

### 2. 数据库设计优化

#### 主要表结构优化
```sql
-- 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    avatar_url VARCHAR(255),
    role ENUM('admin', 'moderator', 'user') DEFAULT 'user',
    status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
    email_verified_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_status (status)
);

-- 分类表
CREATE TABLE categories (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    parent_id BIGINT NULL,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    icon VARCHAR(255),
    sort_order INT DEFAULT 0,
    is_featured BOOLEAN DEFAULT FALSE,
    status ENUM('active', 'inactive') DEFAULT 'active',
    seo_title VARCHAR(255),
    seo_description TEXT,
    seo_keywords VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL,
    INDEX idx_parent_id (parent_id),
    INDEX idx_slug (slug),
    INDEX idx_status (status),
    INDEX idx_featured (is_featured)
);

-- 网站表
CREATE TABLE websites (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    category_id BIGINT NOT NULL,
    title VARCHAR(200) NOT NULL,
    url VARCHAR(500) NOT NULL,
    description TEXT,
    logo_url VARCHAR(255),
    screenshot_url VARCHAR(255),
    tags JSON,
    status ENUM('pending', 'approved', 'rejected', 'suspended') DEFAULT 'pending',
    is_featured BOOLEAN DEFAULT FALSE,
    is_premium BOOLEAN DEFAULT FALSE,
    view_count BIGINT DEFAULT 0,
    click_count BIGINT DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.00,
    rating_count INT DEFAULT 0,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    approved_at TIMESTAMP NULL,
    last_checked_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT,
    INDEX idx_user_id (user_id),
    INDEX idx_category_id (category_id),
    INDEX idx_status (status),
    INDEX idx_featured (is_featured),
    INDEX idx_url (url(100)),
    FULLTEXT idx_search (title, description)
);
```

### 3. API设计规范

#### RESTful API 设计
```
GET    /api/v1/websites              # 获取网站列表
POST   /api/v1/websites              # 提交新网站
GET    /api/v1/websites/{id}         # 获取网站详情
PUT    /api/v1/websites/{id}         # 更新网站信息
DELETE /api/v1/websites/{id}         # 删除网站

GET    /api/v1/categories            # 获取分类列表
POST   /api/v1/categories            # 创建分类
GET    /api/v1/categories/{id}       # 获取分类详情
PUT    /api/v1/categories/{id}       # 更新分类
DELETE /api/v1/categories/{id}       # 删除分类

GET    /api/v1/search               # 搜索网站
GET    /api/v1/recommendations      # 获取推荐
GET    /api/v1/analytics            # 获取统计数据
```

### 4. 缓存策略

#### Redis缓存设计
- **热门网站缓存**: `hot_websites:{category_id}` (TTL: 1小时)
- **分类缓存**: `categories:tree` (TTL: 24小时)
- **用户会话**: `session:{user_id}` (TTL: 30天)
- **搜索结果缓存**: `search:{query_hash}` (TTL: 30分钟)
- **统计数据缓存**: `stats:{type}:{date}` (TTL: 1天)

### 5. 搜索引擎集成

#### Elasticsearch索引设计
```json
{
  "mappings": {
    "properties": {
      "title": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart"
      },
      "description": {
        "type": "text",
        "analyzer": "ik_max_word"
      },
      "url": {
        "type": "keyword"
      },
      "category": {
        "type": "nested",
        "properties": {
          "id": {"type": "long"},
          "name": {"type": "keyword"}
        }
      },
      "tags": {
        "type": "keyword"
      },
      "status": {
        "type": "keyword"
      },
      "created_at": {
        "type": "date"
      }
    }
  }
}
```

## 安全设计

### 1. 认证与授权
- JWT Token认证
- OAuth 2.0社交登录
- 基于角色的权限控制(RBAC)
- API限流和防护

### 2. 数据安全
- 参数化查询防止SQL注入
- XSS防护
- CSRF防护
- 数据加密存储

### 3. 系统安全
- HTTPS强制加密
- 安全头设置
- 输入验证和过滤
- 日志审计

## 性能优化

### 1. 前端优化
- 代码分割和懒加载
- 图片压缩和CDN
- 浏览器缓存策略
- PWA离线支持

### 2. 后端优化
- 数据库查询优化
- Redis缓存策略
- 异步任务处理
- 连接池管理

### 3. 基础设施优化
- 负载均衡
- CDN加速
- 数据库读写分离
- 容器化部署

## 部署架构

### 1. 开发环境
- Docker Compose本地开发
- 热重载和调试支持
- 模拟数据和测试环境

### 2. 生产环境
- Kubernetes集群部署
- 自动扩缩容
- 蓝绿部署
- 监控和告警

## 监控和运维

### 1. 应用监控
- 性能指标监控
- 错误日志收集
- 用户行为分析
- 业务指标统计

### 2. 基础设施监控
- 服务器资源监控
- 数据库性能监控
- 网络状态监控
- 安全事件监控

## 扩展性设计

### 1. 水平扩展
- 微服务架构支持
- 数据库分片
- 缓存集群
- 负载均衡

### 2. 功能扩展
- 插件系统
- API开放平台
- 第三方集成
- 多语言支持

这个架构设计为现代化分类目录系统提供了完整的技术方案，确保系统的可扩展性、高性能和良好的用户体验。
