<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$page_name = '链接详细';
$page_url = '?mod=linkinfo';
$tplfile = 'linkinfo.html';
$tpldir = 'linkinfo';
$table = $DB->table('weblinks');

/** 缓存设置 */
$smarty->compile_dir .= $tpldir;
$smarty->cache_dir .= $tpldir;
$smarty->cache_lifetime = $options['cache_time_info'] * 3600;

$link_id = intval($_GET['lid']);
$cache_id = $link_id;
		
if (!$smarty->isCached($tplfile, $cache_id)) {
	$where = "w.web_status=3 AND l.link_id='$link_id'";
	$link = get_one_weblink($where);
	if (!$link) {
		_404();
	}
	
	$DB->query("UPDATE $table SET link_views=link_views+1 WHERE link_id='".$link['link_id']."' LIMIT 1");
	
	$cate = get_one_category($link['cate_id']);

	$link['web_furl'] = format_url($link['web_url']);
	$link['web_pic'] = get_webthumb($link['web_url'], $link['web_pic']);
	$link['deal_type'] = $dealtypes[$link['deal_type']];
	$link['link_type'] = $linktypes[$link['link_type']];
	$link['link_pos'] = $linkpos[$link['link_pos']];
	$link['link_price'] = $link['link_price'] > 0 ? $link['link_price'].'元 / 月' : '商谈';
	$link['link_time'] = date('Y-m-d', $link['link_time']);
	
	$smarty->assign('page_name', $page_name);
	$smarty->assign('crumb', get_crumb('weblink').' &rsaquo; '.$cate['cate_name'].' &rsaquo; '.$page_name);
	$smarty->assign('rssfeed', get_rssfeed('webdir', $link['cate_id']));
    $smarty->assign('link', $link);
}
		
smarty_output($tplfile, $cache_id);
?>