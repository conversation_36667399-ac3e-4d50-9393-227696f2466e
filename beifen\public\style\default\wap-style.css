@media screen and (max-width: 1200px) {
    body{
        zoom:0;
    }
    
    .grid-container {
    width: 100%;
    max-width: 1200px;
}
.row:before, .row:after {
    content: "";
    display: block;
    height: 0;
    width: 0;
    visibility: hidden;
    clear: both;
}
[class*='col_'] {
    float: left;
    min-height: 1px;
    width: 8.33%;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -o-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -webkit-box-sizing: border-box;
}
.col_1 { width: 8.33%; }
.col_2 { width: 16.66%; }
.col_3 { width: 25%; }
.col_4 { width: 33.33%; }
.col_5 { width: 41.66%; }
.col_6 { width: 50%; }
.col_7 { width: 58.33%; }
.col_8 { width: 66.66%; }
.col_9 { width: 75%; }
.col_10 { width: 83.33%; }
.col_11 { width: 91.66%; }
.col_12,.wrapper,.add-left, .mainbox-left,.topsite,div.addurl-right,.add-left,.add-right {width: 100%!important;}
.wapno,.mainbox-right{/* display:none; */}
a.logo:before,a.logo:after,.yq,.mainbox-right{
    display: none!important;
}
.ad img {
    width: 100%;
    display: none;
}
.logo {
    background: url(digg.png) no-repeat center!important;
    width: 100%;
    background-size: 44%!important;
    margin: 0 auto;
    /* background-position-x: 50%; */
}
.navbox {
    overflow: hidden;
    background: white!important;
    width: 100%;
}

.navbar li {
    width: 25%!important;
    width: 93px!important;
}
.navbar li a{
    padding: 0px;
}
a.ad span,.sitelist li .info cite {
    display: none;
}

.bw-bestbox-list li i.icon {
    display: none;
}
.indexcatebox ul li {width: 20%!important;margin-right: 0px;}
.listbox-title {
    height: auto;
}

.sitelist li .info p {
    width: 100%!important;
    overflow: hidden!important;
    /* text-overflow:ellipsis; */
    /* white-space: nowrap; */
    /* height: auto; */
    position: relative!important;
    top: 0px!important;
    line-height: 21px!important;
    margin-top: 32px;
}

.sitelist li .info {
    width: 60%!important;
    position: relative!important;
    margin-left: 0px!important;
    float: left;
    padding-left: 3%;
}

.sitelist li {
}

html .clearfix {
    zoom: 0;
}

.sitelist li .info h3 {
    position: relative;
    line-height: 32px;
    height: auto;
    font-size: 18px;
}

.sitelist li .thumb {
    position: relative;
    width: 34%;
}

.mainbox .hotdir ul li {
    width: 10%!important;
}
.timelink {
    display: block;
    width: 100%;
    display: none;
}
.topsite-list li a {
    width: auto;
}

.mainbox {
    width: 100%;
}

.topsite,.add-left{
    box-sizing: border-box;
}
.wthumb {
    width: 100%;
    height: auto;
}

ul.wdata {
    height: auto;
    overflow: hidden;
}
.relsite-list li img {
    width: 90%!important;
    height: auto!important;
}

.relsite-list li p {
    width: 100%!important;
}

.relsite-list li {
    width: 19%;
    box-sizing: border-box;
}

.navbar .navline {
    display: none!important;
}
.wdata li {
    display: none;
}

.wdata li:nth-child(1),.wdata li:nth-child(2),.wdata li:nth-child(3) {
    display: block;
}

.crumb {
    display: none;
}

.siteitem li {
    width: 100%;
}
.arcbox-list li a {
    padding: 0 10px 0 0;
}
.sofrm {
    width: 80%!important;
    text-align: center;
    margin: 0 auto;
    margin-bottom: 10px;
    padding-top: 0px!important;
}

.sipt {
    width: 89%!important;
    box-sizing: border-box;
    height: auto!important;
    padding: 5px 6px 5px 90px!important;
    border-radius: 50px;
}

.sbtn {
    width: 30%;
    border-radius: 50px;
    margin-left: -73px;
}

.search {
    text-align: center;
    width: 100%;
}

#cursel {text-align: left;}

.adcode1 {
    display: none;
}

.wapno {
    /* font-size: 6px; */
    box-sizing: border-box;
    width: 100%;
}

#selopt {
    top: 5px!important;
}

.footer {
    background: #181818;
    padding-right: 10px;
}
.bw-bestbox-list li {
    width: 25%!important;
    text-align: center;
}
div.addurl-left {
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 10px;
}

div.addurl-left>ul {
    box-sizing: border-box;
}
#myfrom input[type=text], #myfrom textarea {
    width: 70%;
}

.formbox li strong {
    width: 23%;
}

.add-left {
    margin-bottom: 10px;
}

span.hqyzm {
    width: 70%;
    float: right;
    margin-left: 0px;
}

li.hqyzm1 img {
    width: 30%;
    margin-left: 0px;
}

li.hqyzm1 {
    box-sizing: border-box;
}

.m-no{
    display: none;
}
.siteHonour {
    top: 0px;
}
div#pagebox-left {
    display: none;
}

div#pagebox-right {
    width: 100%;
    float: inherit;
}
.allcate {
    box-sizing: border-box;
}



}