<?php /* Smarty version Smarty-3.1.18, created on 2023-07-07 14:39:05
         compiled from "/www/wwwroot/digg58.com/templet/system/website.html" */ ?>
<?php /*%%SmartyHeaderCode:141210509663ca45902b2c30-27701382%%*/if(!defined('SMARTY_DIR')) exit('no direct access allowed');
$_valid = $_smarty_tpl->decodeProperties(array (
  'file_dependency' => 
  array (
    '18c271b278cb3a7ad09c5b890cad15f7ec55a704' => 
    array (
      0 => '/www/wwwroot/digg58.com/templet/system/website.html',
      1 => 1688711941,
      2 => 'file',
    ),
  ),
  'nocache_hash' => '141210509663ca45902b2c30-27701382',
  'function' => 
  array (
  ),
  'version' => 'Smarty-3.1.18',
  'unifunc' => 'content_63ca459050c2e3_12025541',
  'variables' => 
  array (
    'action' => 0,
    'page_name' => 0,
    'fileurl' => 0,
    'keywords' => 0,
    'user_id' => 0,
    'cate_id' => 0,
    'sort' => 0,
    'order' => 0,
    'key_url' => 0,
    'status' => 0,
    'category_option' => 0,
    'websites' => 0,
    'item' => 0,
    'showpage' => 0,
    'cate_pids' => 0,
    'row' => 0,
    'token' => 0,
    'ispay' => 0,
    'istop' => 0,
    'isbest' => 0,
    'h_action' => 0,
  ),
  'has_nocache_code' => false,
),false); /*/%%SmartyHeaderCode%%*/?>
<?php if ($_valid && !is_callable('content_63ca459050c2e3_12025541')) {function content_63ca459050c2e3_12025541($_smarty_tpl) {?><?php echo $_smarty_tpl->getSubTemplate ("header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>




	<?php if ($_smarty_tpl->tpl_vars['action']->value=='list') {?>

    <h3 class="title"><em><?php echo $_smarty_tpl->tpl_vars['page_name']->value;?>
</em><span><a href="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
?act=add">+添加新站点</a></span></h3>

	<div class="listbox">

		<form name="mform" method="post" action="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">

		<div class="search">

			<input name="keywords" type="text" id="keywords" class="ipt" size="30" value="<?php echo $_smarty_tpl->tpl_vars['keywords']->value;?>
" />

			<input type="submit" class="btn" value="搜索" />

        </div>

        </form>

                    

		<form name="mform" method="post" action="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">

		<div class="toolbar">

			<select name="act" id="act" class="sel">

			<option value="del" style="color: #f00;">删除选定</option>

            <option value="move" style="color: #06c;">移动内容</option>

            <option value="attr" style="color: #f60;">属性设置</option>

			</select>

			<input type="submit" class="btn" value="应用" onClick="if(IsCheck('web_id[]')==false){alert('请指定您要操作的站点ID！');return false;}else{return confirm('确认执行此操作吗？');}">

			<select class="sel" onChange="if(this.options[this.selectedIndex].value!=''){location='<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
?status='+this.options[this.selectedIndex].value+'&user_id=<?php echo $_smarty_tpl->tpl_vars['user_id']->value;?>
&cate_id=<?php echo $_smarty_tpl->tpl_vars['cate_id']->value;?>
&sort=<?php echo $_smarty_tpl->tpl_vars['sort']->value;?>
&order=<?php echo $_smarty_tpl->tpl_vars['order']->value;?>
<?php echo $_smarty_tpl->tpl_vars['key_url']->value;?>
';}">

			<option value="0">所有状态</option>

			<option value="1" style="color: #333;"<?php echo opt_selected($_smarty_tpl->tpl_vars['status']->value,1);?>
>拉黑</option>

			<option value="2" style="color: #f30;"<?php echo opt_selected($_smarty_tpl->tpl_vars['status']->value,2);?>
>待审核</option>

			<option value="3" style="color: #080;"<?php echo opt_selected($_smarty_tpl->tpl_vars['status']->value,3);?>
>已审核</option>

			</select>

			<select class="sel" onChange="if(this.options[this.selectedIndex].value!=''){location='<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
?status=<?php echo $_smarty_tpl->tpl_vars['status']->value;?>
&user_id=<?php echo $_smarty_tpl->tpl_vars['user_id']->value;?>
&cate_id='+this.options[this.selectedIndex].value+'&sort=<?php echo $_smarty_tpl->tpl_vars['sort']->value;?>
&order=<?php echo $_smarty_tpl->tpl_vars['order']->value;?>
<?php echo $_smarty_tpl->tpl_vars['key_url']->value;?>
';}">

			<option value="0" selected>所有分类</option>

			<?php echo $_smarty_tpl->tpl_vars['category_option']->value;?>


			</select>

			<select class="sel" onChange="if(this.options[this.selectedIndex].value!=''){location='<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
?status=<?php echo $_smarty_tpl->tpl_vars['status']->value;?>
&user_id=<?php echo $_smarty_tpl->tpl_vars['user_id']->value;?>
&cate_id=<?php echo $_smarty_tpl->tpl_vars['cate_id']->value;?>
&sort='+this.options[this.selectedIndex].value+'<?php echo $_smarty_tpl->tpl_vars['key_url']->value;?>
';}">

			<option value="1"<?php echo opt_selected($_smarty_tpl->tpl_vars['sort']->value,1);?>
>按提交时间排列</option>
			<option value="10"<?php echo opt_selected($_smarty_tpl->tpl_vars['sort']->value,10);?>
>按更新时间排列</option>
			<option value="2"<?php echo opt_selected($_smarty_tpl->tpl_vars['sort']->value,2);?>
>按谷歌PR排列</option>

            <option value="3"<?php echo opt_selected($_smarty_tpl->tpl_vars['sort']->value,3);?>
>按百度PR排列</option>

            <option value="4"<?php echo opt_selected($_smarty_tpl->tpl_vars['sort']->value,4);?>
>按搜狗PR排列</option>

			<option value="5"<?php echo opt_selected($_smarty_tpl->tpl_vars['sort']->value,5);?>
>按Alexa排列</option>

			<option value="6"<?php echo opt_selected($_smarty_tpl->tpl_vars['sort']->value,6);?>
>按入站排列</option>

			<option value="7"<?php echo opt_selected($_smarty_tpl->tpl_vars['sort']->value,7);?>
>按出站排列</option>

			<option value="8"<?php echo opt_selected($_smarty_tpl->tpl_vars['sort']->value,8);?>
>按浏览排列</option>

			<option value="9"<?php echo opt_selected($_smarty_tpl->tpl_vars['sort']->value,9);?>
>按错误排列</option>


			</select>

			<select class="sel" onChange="if(this.options[this.selectedIndex].value!=''){location='<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
?status=<?php echo $_smarty_tpl->tpl_vars['status']->value;?>
&user_id=<?php echo $_smarty_tpl->tpl_vars['user_id']->value;?>
&cate_id=<?php echo $_smarty_tpl->tpl_vars['cate_id']->value;?>
&sort=<?php echo $_smarty_tpl->tpl_vars['sort']->value;?>
&order='+this.options[this.selectedIndex].value+'<?php echo $_smarty_tpl->tpl_vars['key_url']->value;?>
';}">

			<option value="DESC"<?php echo opt_selected($_smarty_tpl->tpl_vars['order']->value,'DESC');?>
>降序</option>

			<option value="ASC"<?php echo opt_selected($_smarty_tpl->tpl_vars['order']->value,'ASC');?>
>升序</option>

			</select>

		</div>

		

    	<table class="table table-hover" width="100%" border="0" cellspacing="1" cellpadding="0">
			<thead>
			<tr>

				<th><input type="checkbox" id="ChkAll" onClick="CheckAll(this.form)"></th>

				<th>ID</th>

				<th>所属分类</th>

				<th>网站名称</th>

				<th>谷歌PR</th>

                <th>百度PR</th>

                <th>搜狗PR</th>

				<th>Alexa</th>

				<th>入站次数</th>

				<th>出站次数</th>

				<th>浏览次数</th>

				<th>属性状态</th>

				<th>所 有 者</th>

				<th>收录时间</th>

				<th>操作选项</th>

			</tr>
			</thead>
			<?php  $_smarty_tpl->tpl_vars['item'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['item']->_loop = false;
 $_from = $_smarty_tpl->tpl_vars['websites']->value; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['item']->key => $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->_loop = true;
?>

			<tr>

				<td><input name="web_id[]" type="checkbox" value="<?php echo $_smarty_tpl->tpl_vars['item']->value['web_id'];?>
"></td>

				<td><?php echo $_smarty_tpl->tpl_vars['item']->value['web_id'];?>
</td>

				<td><?php echo $_smarty_tpl->tpl_vars['item']->value['web_cate'];?>
</td>

				<td class="ltext"><?php echo $_smarty_tpl->tpl_vars['item']->value['web_name'];?>
</td>

				<td class="data"><?php echo $_smarty_tpl->tpl_vars['item']->value['web_grank'];?>
</td>

                <td class="data"><?php echo $_smarty_tpl->tpl_vars['item']->value['web_brank'];?>
</td>

                <td class="data"><?php echo $_smarty_tpl->tpl_vars['item']->value['web_srank'];?>
</td>

				<td class="data"><?php echo $_smarty_tpl->tpl_vars['item']->value['web_arank'];?>
</td>

				<td class="data"><?php echo $_smarty_tpl->tpl_vars['item']->value['web_instat'];?>
</td>

				<td class="data"><?php echo $_smarty_tpl->tpl_vars['item']->value['web_outstat'];?>
</td>

				<td class="data"><?php echo $_smarty_tpl->tpl_vars['item']->value['web_views'];?>
</td>

				<td><?php echo $_smarty_tpl->tpl_vars['item']->value['web_attr'];?>
</td>

				<td><?php echo $_smarty_tpl->tpl_vars['item']->value['nick_name'];?>
</td>

				<td><?php echo $_smarty_tpl->tpl_vars['item']->value['web_ctime'];?>
</td>

				<td><?php echo $_smarty_tpl->tpl_vars['item']->value['web_opera'];?>
</td>

			</tr>

			<?php }
if (!$_smarty_tpl->tpl_vars['item']->_loop) {
?>

			<tr><td colspan="15">无任何网站！</td></tr>

			<?php } ?>

		</table>

        </form>

        <div class="pagebox"><?php echo $_smarty_tpl->tpl_vars['showpage']->value;?>
</div>

    </div>

    <?php }?>



	<?php if ($_smarty_tpl->tpl_vars['action']->value=='add'||$_smarty_tpl->tpl_vars['action']->value=='edit') {?>

    <h3 class="title"><em><?php echo $_smarty_tpl->tpl_vars['page_name']->value;?>
</em><span><a href="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">返回列表&rsaquo;</a></span></h3>

	<div class="formbox" style="max-width: 500px;margin: 0 auto;">

    	<form name="mform" method="post" action="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">

		<div class="form-group">

				<label for="exampleInputEmail1" class="col-md-3 text-right">所属分类：</label>				<div class="col-md-9">

					<?php  $_smarty_tpl->tpl_vars['item'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['item']->_loop = false;
 $_from = $_smarty_tpl->tpl_vars['cate_pids']->value; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['item']->key => $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->_loop = true;
?><select name="level_<?php echo $_smarty_tpl->tpl_vars['item']->value;?>
" id="level_<?php echo $_smarty_tpl->tpl_vars['item']->value;?>
" class="sel form-control"></select><?php }
if (!$_smarty_tpl->tpl_vars['item']->_loop) {
?><select name="level_1" id="level_1" class="sel form-control"></select><?php } ?><input type="hidden" name="cate_id" id="cate_id" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['cate_id'];?>
">
				</div>
		</div>
		<div class="form-group">
				<label for="exampleInputEmail1" class="col-md-3 text-right">网站名称：</label>				<div class="col-md-9">
				

					<input name="web_name" type="text" class="form-control" id="web_name" size="50" maxlength="50" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['web_name'];?>
" /></td>
				</div>

			</div>

		<div class="form-group">

				<label for="exampleInputEmail1" class="col-md-3 text-right">网站域名：</label>				<div class="col-md-9">

				<input name="web_url" type="text" class="form-control" id="web_url" size="50" maxlength="50" onKeyPress="Strip_Http()" onChange="Strip_Http()" onKeyUp="Strip_Http()" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['web_url'];?>
" /><input type="button" class="btn btn-success" id="meta_btn" value="抓取Meta" onclick="GetMeta()"><span class="tips">例: www.digg58.com</span></td>
				</div>
			</div>

		<div class="form-group">

				<label for="exampleInputEmail1" class="col-md-3 text-right">TAG标签：</label>				<div class="col-md-9">

				<input name="web_tags" type="text" class="form-control" id="web_tags" size="50" maxlength="255" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['web_tags'];?>
" onBlur="javascript:this.value=this.value.replace(/，/ig,',');" /><span class="tips">多个标签用英文的“,”逗号隔开</span></td>
</div>
			</div>

		<div class="form-group">

				<label for="exampleInputEmail1" class="col-md-3 text-right">网站截图：</label>				<div class="col-md-9">

				<input name="web_pic" type="text" class="form-control" id="web_pic" size="50" maxlength="100" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['web_pic'];?>
" style="float: left;" /><input type="file" name="file_upload" id="file_upload"></td>
</div>
			</div>

		<div class="form-group">

				<label for="exampleInputEmail1" class="col-md-3 text-right">网站简介：</label>				<div class="col-md-9">
          
          
          
          
          

				
          
          
          
          
          <script type="text/javascript">

					var editor;

					KindEditor.ready(function(K) {

						editor = K.create('textarea[name="web_intro"]', {

							resizeType : 1,

							allowPreviewEmoticons : false,

							allowImageUpload : true,

							uploadJson : 'upload.php?mod=article&token=<?php echo $_smarty_tpl->tpl_vars['token']->value;?>
',

							items : [

								'source', '|', 'fontname', 'fontsize', '|', 'forecolor', 'hilitecolor', 'bold', 'italic', 'underline',

								'removeformat', '|', 'justifyleft', 'justifycenter', 'justifyright', 'insertorderedlist',

								'insertunorderedlist', '|', 'emoticons', 'image', 'link', '|', 'fullscreen']

						});

					});

                    </script>

                    <textarea name="web_intro" id="web_intro" cols="50" rows="6" class="form-control" style="width: 100%; height: 300px; visibility: hidden;"><?php echo $_smarty_tpl->tpl_vars['row']->value['web_intro'];?>
</textarea>
          

          
          
          
          </td>
</div>
			</div>

        <div class="form-group">

				<label for="exampleInputEmail1" class="col-md-3 text-right">服务器IP：</label>				<div class="col-md-9">

				<td><input name="web_ip" type="text" class="form-control" id="web_ip" size="30" maxlength="30" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['web_ip'];?>
" /><input type="button" class="btn btn-success" id="data_btn" value="获取数据" onclick="GetData()"><span class="tips">例: 127.0.0.1</span></td>
</div>
			</div>
			<div class="form-group">
				<label for="exampleInputEmail1" class="col-md-3 text-right">QQ：</label>				<div class="col-md-9">
				

					<input name="user_qq" type="text" class="form-control" id="user_qq" size="50" maxlength="50" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['user_qq'];?>
" /></td>
				</div>

			</div>

 		<div class="form-group">

				<label for="exampleInputEmail1" class="col-md-3 text-right">PageRank/https：</label>				<div class="col-md-9">

				<td><input name="web_grank" type="text" class="form-control" id="web_grank" size="10" maxlength="1" value="<?php echo !$_smarty_tpl->tpl_vars['row']->value['web_grank'] ? '0' : $_smarty_tpl->tpl_vars['row']->value['web_grank'];?>
"/><span class="tips">填写“1”为开启https</span></td>
</div>
			</div>

 		<div class="form-group">

				<label for="exampleInputEmail1" class="col-md-3 text-right">BaiduRank：</label>				<div class="col-md-9">

				<input name="web_brank" type="text" class="form-control" id="web_brank" size="10" maxlength="1" value="<?php echo !$_smarty_tpl->tpl_vars['row']->value['web_brank'] ? '0' : $_smarty_tpl->tpl_vars['row']->value['web_brank'];?>
" />
</div>
			</div>

 		<div class="form-group">

				<label for="exampleInputEmail1" class="col-md-3 text-right">SogouRank：</label>				<div class="col-md-9">

				<input name="web_srank" type="text" class="form-control" id="web_srank" size="10" maxlength="1" value="<?php echo !$_smarty_tpl->tpl_vars['row']->value['web_srank'] ? '0' : $_smarty_tpl->tpl_vars['row']->value['web_srank'];?>
" />
</div>
			</div>

 		<div class="form-group">

				<label for="exampleInputEmail1" class="col-md-3 text-right">AlexaRank：</label>				<div class="col-md-9">

				<input name="web_arank" type="text" class="form-control" id="web_arank" size="10" maxlength="10" value="<?php echo !$_smarty_tpl->tpl_vars['row']->value['web_arank'] ? '0' : $_smarty_tpl->tpl_vars['row']->value['web_arank'];?>
" />
</div>
			</div>

 		<div class="form-group">

				<label for="exampleInputEmail1" class="col-md-3 text-right">点入次数：</label>				<div class="col-md-9">

				<input name="web_instat" type="text" class="form-control" id="web_instat" size="10" maxlength="10" value="<?php echo !$_smarty_tpl->tpl_vars['row']->value['web_instat'] ? '0' : $_smarty_tpl->tpl_vars['row']->value['web_instat'];?>
" /> 

			</div>
</div>
 		<div class="form-group">

				<label for="exampleInputEmail1" class="col-md-3 text-right">点出次数：</label>				<div class="col-md-9">

				<td><input name="web_outstat" type="text" class="form-control" id="web_outstat" size="10" maxlength="10" value="<?php echo !$_smarty_tpl->tpl_vars['row']->value['web_outstat'] ? '0' : $_smarty_tpl->tpl_vars['row']->value['web_outstat'];?>
" /> 
</div>
			</div>

 		<div class="form-group">

				<label for="exampleInputEmail1" class="col-md-3 text-right">浏览次数：</label>				<div class="col-md-9">

				<input name="web_views" type="text" class="form-control" id="web_views" size="10" maxlength="10" value="<?php echo !$_smarty_tpl->tpl_vars['row']->value['web_views'] ? '0' : $_smarty_tpl->tpl_vars['row']->value['web_views'];?>
" /> 
</div>
			</div>

		<div class="form-group">

				<label for="exampleInputEmail1" class="col-md-3 text-right">错误次数：</label>				<div class="col-md-9">

				<input name="web_errors" type="text" class="form-control" id="web_errors" size="10" maxlength="10" value="<?php echo !$_smarty_tpl->tpl_vars['row']->value['web_errors'] ? '0' : $_smarty_tpl->tpl_vars['row']->value['web_errors'];?>
" /> 
</div>
			</div>

		<div class="form-group">

				<label for="exampleInputEmail1" class="col-md-3 text-right">属性设置：</label>				
					<div class="col-md-9">

					<input name="web_ispay" type="checkbox" id="web_ispay" value="1"<?php echo opt_checked($_smarty_tpl->tpl_vars['ispay']->value,1);?>
 /><label for="web_ispay">付费</label>　<input name="web_istop" type="checkbox" id="web_istop" value="1"<?php echo opt_checked($_smarty_tpl->tpl_vars['istop']->value,1);?>
 /><label for="web_istop">置顶</label>	　<input name="web_isbest" type="checkbox" id="web_isbest" value="1"<?php echo opt_checked($_smarty_tpl->tpl_vars['isbest']->value,1);?>
 /><label for="web_isbest">推荐</label>		
				</div>
		</div>

		<div class="form-group">

				<label for="exampleInputEmail1" class="col-md-3 text-right">审核状态：</label>				
				<div class="col-md-9">

					<select name="web_status" id="web_status" class="sel"><option value="1" style="color: #333;"<?php echo opt_selected($_smarty_tpl->tpl_vars['status']->value,1);?>
>黑名单</option><option value="2" style="color: #f30;"<?php echo opt_selected($_smarty_tpl->tpl_vars['status']->value,2);?>
>待审核</option><option value="3" style="color: #080;"<?php echo opt_selected($_smarty_tpl->tpl_vars['status']->value,3);?>
>已审核</option></select></td>
				</div>
		</div>

			<tr class="btnbox">

            	<th>&nbsp;</th>

				<td>

					<input name="act" type="hidden" id="act" value="<?php echo $_smarty_tpl->tpl_vars['h_action']->value;?>
">

					<?php if ($_smarty_tpl->tpl_vars['action']->value=='edit'&&$_smarty_tpl->tpl_vars['row']->value['web_id']) {?>

					<input name="web_id" type="hidden" id="web_id" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['web_id'];?>
">

					<?php }?>

					<input type="submit" class="btn btn-success" value="保 存">&nbsp;

					<input type="reset" class="btn btn-warning" value="取 消" onClick="window.location.href='<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
';">

				</td>

			</tr>

		</table>

        </form>

	</div>

	<script src="../public/scripts/linkage.select.js" type="text/javascript"></script>

	<script src="../public/scripts/jquery.uploadify.js" type="text/javascript"></script>

	<script type="text/javascript">

	var options = {

		ajax : 'ajaxget.php?mod=website',

		auto : true,

		field_name : '[name=cate_id]'

	}

	

	var sel = new LinkageSelect(options);

	<?php if ($_smarty_tpl->tpl_vars['action']->value=='add') {?>

	sel.bind('#level_1');

	<?php } else { ?>

	<?php  $_smarty_tpl->tpl_vars['item'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['item']->_loop = false;
 $_smarty_tpl->tpl_vars['key'] = new Smarty_Variable;
 $_from = $_smarty_tpl->tpl_vars['cate_pids']->value; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['item']->key => $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->_loop = true;
 $_smarty_tpl->tpl_vars['key']->value = $_smarty_tpl->tpl_vars['item']->key;
?>

	sel.bind('#level_<?php echo $_smarty_tpl->tpl_vars['item']->value;?>
', <?php echo $_smarty_tpl->tpl_vars['item']->value;?>
);

	<?php } ?>

	<?php }?>

		

	$('#file_upload').uploadify({

		'width' : 60,

		'height' : 25,

		'buttonText' : '上传截图',

		'buttonClass' : 'btn',

		'fileTypeExts' : '*.gif; *.jpg; *.png',

		'fileSizeLimit' : '10MB',

		'multi' : false,

		'swf' : '../public/scripts/uploadify.swf',

		'uploader' : 'upload.php?mod=website&token=<?php echo $_smarty_tpl->tpl_vars['token']->value;?>
',

		'onUploadSuccess' : function($file, $data, $response) {

			$json = $.parseJSON($data);

			$('#web_pic').val($json.url);

		},

		'onUploadError' : function() {

			alert('上传失败！');

		}

	});

	</script>

	<?php }?>

	

	<?php if ($_smarty_tpl->tpl_vars['action']->value=='move') {?>

    <h3 class="title"><em><?php echo $_smarty_tpl->tpl_vars['page_name']->value;?>
</em><span><a href="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">返回列表&rsaquo;</a></span></h3>

	<div class="formbox">

		<form name="mform" method="post" action="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">

		<table width="100%" border="0" cellspacing="1" cellpadding="0">

			<tr>

				<th valign="top">已选定的内容：</label>				<div class="col-md-9">

				<td><?php  $_smarty_tpl->tpl_vars['item'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['item']->_loop = false;
 $_from = $_smarty_tpl->tpl_vars['websites']->value; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['item']->key => $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->_loop = true;
?><a href="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
&act=edit&web_id=<?php echo $_smarty_tpl->tpl_vars['item']->value['web_id'];?>
"><?php echo $_smarty_tpl->tpl_vars['item']->value['web_name'];?>
</a><input name="web_id[]" type="hidden" value="<?php echo $_smarty_tpl->tpl_vars['item']->value['web_id'];?>
"><br /><?php } ?></td>

			</tr>

			<tr>

				<th>将以上内容移动至：</label>				<div class="col-md-9">

				<td><select name="cate_id" id="cate_id" class="sel"><?php echo $_smarty_tpl->tpl_vars['category_option']->value;?>
</select></td>

			</tr>

			<tr class="btnbox">

            	<th>&nbsp;</th>

				<td>

					<input name="act" type="hidden" id="act" value="<?php echo $_smarty_tpl->tpl_vars['h_action']->value;?>
">

					<input type="submit" class="btn" value="保 存">&nbsp;

					<input type="reset" class="btn" value="取 消" onClick="window.location.href='<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
';">

				</td>

			</tr>

		</table>

		</form>

	</div>

	<?php }?>

    

	<?php if ($_smarty_tpl->tpl_vars['action']->value=='attr') {?>

    <h3 class="title"><em><?php echo $_smarty_tpl->tpl_vars['page_name']->value;?>
</em><span><a href="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">返回列表&rsaquo;</a></span></h3>

	<div class="formbox">

		<form name="mform" method="post" action="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">

		<table class="table table-hover" width="100%" border="0" cellspacing="1" cellpadding="0">

			<tr>

				<th valign="top">已选定的内容：</label>				<div class="col-md-9">

				<td><?php  $_smarty_tpl->tpl_vars['item'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['item']->_loop = false;
 $_from = $_smarty_tpl->tpl_vars['websites']->value; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['item']->key => $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->_loop = true;
?><?php echo $_smarty_tpl->tpl_vars['item']->value['web_id'];?>
 - <a href="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
&act=edit&web_id=<?php echo $_smarty_tpl->tpl_vars['item']->value['web_id'];?>
"><?php echo $_smarty_tpl->tpl_vars['item']->value['web_name'];?>
</a><input name="web_id[]" type="hidden" value="<?php echo $_smarty_tpl->tpl_vars['item']->value['web_id'];?>
"><br /><?php } ?></td>

			</tr>

			<tr>

				<th>属性设置：</label>				<div class="col-md-9">

				<td><input name="web_ispay" type="checkbox" id="web_ispay" value="1" /><label for="web_ispay">付费</label>				<div class="col-md-9"> <input name="web_istop" type="checkbox" id="web_istop" value="1" /><label for="web_istop">置顶</label>				<div class="col-md-9">　<input name="web_isbest" type="checkbox" id="web_isbest" value="1" /><label for="web_isbest">推荐</label>				<div class="col-md-9"></td>

			</tr>

			<tr>

				<th>审核状态：</label>				<div class="col-md-9">

				<td><select name="web_status" id="web_status" class="sel"><option value="1" style="color: #333;">黑名单</option><option value="2" style="color: #f30;">待审核</option><option value="3" selected="selected" style="color: #080;">已审核</option></select></td>

			</tr>

			<tr class="btnbox">

            	<th>&nbsp;</th>

				<td colspan="2">

				<input name="act" type="hidden" id="act" value="<?php echo $_smarty_tpl->tpl_vars['h_action']->value;?>
">

				<input type="submit" class="btn" value="保 存">&nbsp;

				<input type="reset" class="btn" value="取 消" onClick="window.location.href='<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
';">

				</td>

			</tr>

		</table>

		</form>

	</div>

	<?php }?>

    

    <?php if ($_smarty_tpl->tpl_vars['action']->value=='down') {?>

    <h3 class="title"><em><?php echo $_smarty_tpl->tpl_vars['page_name']->value;?>
</em><span><a href="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">返回列表&rsaquo;</a></span></h3>

	<div class="formbox">

		<form name="mform" method="post" action="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">

		<table width="100%" border="0" cellspacing="1" cellpadding="0">

			<tr>

				<th>注意事项：</label>				<div class="col-md-9">

				<td>下载远程图片，将占用一定的服务器资源，请避免在白天流量高峰期时段使用</td>

			<tr class="btnbox">

            	<th>&nbsp;</th>

				<td colspan="2">

				<input type="button" class="btn" value="下载所有站点图片" onClick="window.location.href='webpic.php?act=down&type=all';">&nbsp;

				<input type="button" class="btn" value="下载失败图片" onClick="window.location.href='webpic.php?act=down&type=part';">&nbsp;

                <input type="button" class="btn" value="失效图片检测" onClick="window.location.href='webpic.php?act=check';">

				</td>

			</tr>

		</table>

		</form>

	</div>

    <?php }?>
<style>
	.btn{
		padding: 2px 5px!important;
	}
	.pagebox a {
    padding: 5px 10px;
    margin-right: 10px;
    background: white;
}
.pagebox a:hover {
    background: #337ab7;
    color: #fff;
}
span.current {
    padding: 5px 10px;
    margin-right: 10px;
    background: #000000;
    color: #fff;
}
span.total_page {
    padding: 5px 10px;
    margin-right: 10px;
    background: #ffffff;
}
select.form-control {
    width: 50%!important;
    float: left;
}
div#file_upload {
    float: right;
}
</style>



<?php echo $_smarty_tpl->getSubTemplate ("footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>
<?php }} ?>
