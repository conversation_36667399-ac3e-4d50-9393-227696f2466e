<?php
if (!defined('IN_MEMBER')) exit('Access Denied');

$page_url = '?mod=invite';
$tplfile = 'invite.html';
$table = $DB->table('users');

$action = isset($_GET['act']) ? $_GET['act'] : 'list';
$smarty->assign('action', $action); 

if (!$smarty->isCached($tplfile)) {
	/** list */
	if ($action == 'list') {
		$page_name = '我邀请的好友';
		$smarty->assign('crumb', get_crumb().' &rsaquo; '.$page_name);
		
		$pagesize = 10;
		$curpage = intval($_GET['page']);
		if ($curpage > 1) {
			$start = ($curpage - 1) * $pagesize;
		} else {
			$start = 0;
			$curpage = 1;
		}
		
		$where = "root_id='".$myself['user_id']."'";
		$users = get_user_list($where, 'join_time', 'DESC', $start, $pagesize);
		$total = $DB->get_count($table, $where);
		$showpage = showpage($page_url, $total, $curpage, $pagesize);
		
		$smarty->assign('page_name', $page_name);
		$smarty->assign('users', $users);
		$smarty->assign('total', $total);
		$smarty->assign('showpage', $showpage);
	}
}

smarty_output($tplfile);
?>