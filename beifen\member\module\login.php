<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$page_name = '登录账户';
$page_url = '?mod=login';
$tplfile = 'login.html';
$table = $DB->table('users');

if (!$smarty->isCached($tplfile)) {
	$smarty->assign('page_name', $page_name);
	$smarty->assign('crumb', get_crumb().' &rsaquo; '.$page_name);
    
    if ($_POST['action'] == 'login') {
		$user_email = trim($_POST['email']);
		$user_pass = trim($_POST['pass']);
		$open_id = trim($_POST['open_id']);
		
		if (empty($user_email) || !is_valid_email($user_email)) {
			alert('请输入有效的电子邮箱！');
		}
        
		if (empty($user_pass)) {
			alert('请输入登陆密码！');
		}
		
		$newpass = md5($user_pass);
		$row = $DB->fetch_one("SELECT user_id, user_pass, login_time, login_count FROM $table WHERE user_email='$user_email'");
		if ($row) {
            if ($newpass == $row['user_pass']) {
				$ip_address = sprintf("%u", ip2long(get_client_ip()));
            	$login_count = $row['login_count'] + 1;
				
				$data = array();
				if (!empty($open_id)) {
					$data = array('open_id' => $open_id, 'login_time' => time(), 'login_ip' => $ip_address, 'login_count' => $login_count);
				} else {
					$data = array('login_time' => time(), 'login_ip' => $ip_address, 'login_count' => $login_count);
				}
				$where = array('user_id' => $row['user_id']);
				$DB->update($table, $data, $where);
				
				$authcode = authcode("$row[user_id]\t$row[user_pass]\t$login_count", "ENCODE", AUTH_KEY);
				$expire = time() + 3600 * 24;
				setcookie('authcode', $authcode, $expire, $options['site_root']);
			
				redirect('?mod=home');
            } else {
				alert('用户名或密码错误，请重试！');
			}			
		} else {
			alert('用户名或密码错误，请重试！');
		}
	}
}

smarty_output($tplfile);
?>