<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$page_name = '域名库';
$page_url = '?mod=domain';
$tplfile = 'domain.html';
$tpldir = 'domain';
$table = $DB->table('websites');

/** 缓存设置 */
$smarty->compile_dir .= $tpldir;
$smarty->cache_dir .= $tpldir;
$smarty->cache_lifetime = $options['cache_time_list'] * 3600;

$pagesize = 100;
$curpage = intval($_GET['page']);
if ($curpage > 1) {
	$start = ($curpage - 1) * $pagesize;
} else {
	$start = 0;
	$curpage = 1;
}
$pagestr = ($curpage > 0) ? ' - 第'.$curpage.'页': '';
		
//$setdays = intval($_GET['days']);
//$cache_id = $setdays.'-'.$curpage;

if (!$smarty->isCached($tplfile, $cache_id)) {	
	$newarr = array();
	$i = 0;
	foreach ($timescope as $key => $val) {
		$newarr[$i]['time_id'] = $key;
		$newarr[$i]['time_text'] = $val;
		$newarr[$i]['time_link'] = $page_url.'&days='.$key;
		$i++;
	}
	
	$where = "w.web_status=3";

	$crumb = get_crumb('website').' &rsaquo; '.$page_name;

			
	$websites = get_website_list($where, 'web_ctime', 'DESC', $start, $pagesize);
	$total = $DB->get_count($table.' w', $where);
	$showpage = showpage($page_url.$strurl, $total, $curpage, $pagesize);
			
	$smarty->assign('page_name', $page_name);
	$smarty->assign('pagestr', $pagestr);
	$smarty->assign('crumb', $crumb);
	$smarty->assign('rssfeed', get_rssfeed('website'));
	$smarty->assign('timescope', $newarr);
	//$smarty->assign('timestr', $timescope[$setdays]);
	//$smarty->assign('days', $setdays);
	$smarty->assign('total', $total);
	$smarty->assign('websites', $websites);
	$smarty->assign('showpage', $showpage);
	$smarty->assign('curpage', $curpage);
}
	
smarty_output($tplfile, $cache_id);
?>