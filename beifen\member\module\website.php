<?php
if (!defined('IN_MEMBER')) exit('Access Denied');

require(CORE_PATH.'module/category.php');
require(CORE_PATH.'module/website.php');

$page_url = '?mod=website';
$tplfile = 'website.html';
$table = $DB->table('websites');

$action = isset($_GET['act']) ? $_GET['act'] : 'list';
$smarty->assign('action', $action); 

if (!$smarty->isCached($tplfile)) {
	/** list */
	if ($action == 'list') {
		$page_name = '网站管理';
		$smarty->assign('page_name', $page_name);
		$smarty->assign('crumb', get_crumb().' &rsaquo; '.$page_name);
		
		$pagesize = 20;
		$curpage = intval($_GET['page']);
		if ($curpage > 1) {
			$start = ($curpage - 1) * $pagesize;
		} else {
			$start = 0;
			$curpage = 1;
		}
		
		$where = "w.user_id='".$myself['user_id']."'";
	
		$websites = get_website_list($where, 'ctime', 'DESC', $start, $pagesize);
		$total = $DB->get_count($table.' w', $where);
		$showpage = showpage($page_url, $total, $curpage, $pagesize);
		
		$smarty->assign('page_name', $page_name);
		$smarty->assign('websites', $websites);
		$smarty->assign('total', $total);
		$smarty->assign('showpage', $showpage);
	}
	
	/** add */
	if ($action == 'add') {
		$page_name = '网站提交';
		
		#统计当日可提交的站点数量
		if ($options['submit_limit'] > 0) {
			$today_count = $DB->get_count($DB->table('websites'), "FROM_UNIXTIME(web_ctime, '%Y-%m-%d') = CURDATE()");
			$submit_limit = $options['submit_limit'] - $today_count;
			$smarty->assign('submit_limit', $submit_limit);
		}
		
		$smarty->assign('page_name', $page_name);
		$smarty->assign('crumb', get_crumb().' &rsaquo; '.$page_name);
		$smarty->assign('do', 'saveadd');
	}
	
	/** edit */
	if ($action == 'edit') {
		$page_name = '网站编辑';
		
		$web_id = intval($_GET['wid']);
		$where = "w.user_id='$myself[user_id]' AND w.web_id='$web_id'";
		$row = get_one_website($where);
		if (!$row) {
			alert('您要修改的内容不存在或无权限！');
		}
		
		$cate_pids = array();
		$crow = get_one_category($row['cate_id']);
		if ($crow) {
			$parent_cids = $crow['cate_arrparentid'].','.$row['cate_id']; #分类父ID
			if (strpos($parent_cids, ',') !== false) {
				$cate_pids = explode(',', $parent_cids);
				array_shift($cate_pids);
			} else {
				$cate_pids = (array) $parent_cids;
			}		
		}
		$row['web_ip'] = long2ip($row['web_ip']);
		
		$smarty->assign('page_name', $page_name);
		$smarty->assign('crumb', get_crumb().' &rsaquo; '.$page_name);	
		$smarty->assign('cate_pids', $cate_pids);
		$smarty->assign('row', $row);	
		$smarty->assign('do', 'saveedit');
	}
	
	/** save */
	if (in_array($_POST['do'], array('saveadd', 'saveedit'))) {
		$cate_id = intval($_POST['cate_id']);
		$web_name = strip_tags($_POST['web_name']);
		$web_url = trim($_POST['web_url']);
		$web_tags = strip_tags($_POST['web_tags']);
		$web_intro = strip_tags($_POST['web_intro']);
		$web_ip = trim($_POST['web_ip']);
		$web_grank = intval($_POST['web_grank']);
		$web_brank = intval($_POST['web_brank']);
		$web_srank = intval($_POST['web_srank']);
		$web_arank = intval($_POST['web_arank']);
		$web_time = time();
		
		if ($cate_id <= 0) {
			alert('请选择网站所属分类！');
		} else {
			$cate = get_one_category($cate_id);
			if ($cate['cate_childcount'] > 0) {
				alert('指定的分类下有子分类，请选择子分类进行操作！');
			}
		}
	
		if (empty($web_name)) {
			alert('请输入网站名称！');
		} else {
			if (!censor_words($options['filter_words'], $web_name)) {
				alert('网站名称中含有非法关键词！');	
			}
		}
		
		if (empty($web_url)) {
			alert('请输入网站域名！');
		} else {
			if (!is_valid_domain($web_url)) {
				alert('请输入正确的网站域名！');
			}
		}
		
		if (!empty($web_tags)) {
			if (!censor_words($options['filter_words'], $web_tags)) {
				alert('TAG标签中含有非法关键词！');
			}
			
			$web_tags = str_replace('，', ',', $web_tags);
			$web_tags = str_replace(',,', ',', $web_tags);
			if (substr($web_tags, -1) == ',') {
				$web_tags = substr($web_tags, 0, strlen($web_tags) - 1);
			}
		}
			
		if (empty($web_intro)) {
			alert('请输入网站简介！');
		} else {
			if (!censor_words($options['filter_words'], $web_intro)) {
				alert('网站简介中含有非法关键词！');	
			}
		}
		
		if ($options['post_website_score'] > 0 && $myself['user_score'] < $options['post_website_score']) {
			alert('积分不足，请充值后再提交！', '?mod=consume&act=pay');
		}
		
		$web_ip = sprintf("%u", ip2long($web_ip));
		$web_ispay = $options['post_website_score'] > 0 ? 1 : 0;
		$web_status = $options['post_website_score'] > 0 ? 3 : 2;
		$web_data = array(
			'cate_id' => $cate_id,
			'user_id' => $myself['user_id'],
			'web_name' => $web_name,
			'web_url' => $web_url,
			'web_tags' => $web_tags,
			'web_intro' => $web_intro,
			'web_ispay' => $web_ispay,
			'web_status' => $web_status,
			'web_ctime' => $web_time,
		);
		
		if ($_POST['do'] == 'saveadd') {
    		$query = $DB->query("SELECT web_id FROM $table WHERE web_url='$web_url'");
    		if ($DB->num_rows($query)) {
        		alert('您所提交的网站已存在！');
    		}
			
			$DB->insert($table, $web_data);
			$insert_id = $DB->insert_id();
			
			$stat_data = array(
				'web_id' => $insert_id,
				'web_ip' => $web_ip,
				'web_grank' => $web_grank,
				'web_brank' => $web_brank,
				'web_srank' => $web_srank,
				'web_arank' => $web_arank,
				'web_utime' => $web_time,
			);
			$DB->insert($DB->table('webdata'), $stat_data);
			
			#消费记录
			if ($options['post_website_score'] > 0) {
				$order_id = strtoupper(uniqid());
				$cons_score = $options['post_website_score'];
				$cons_data = array('user_id' => $myself['user_id'], 'order_id' => $order_id, 'cons_name' => $myself['user_email'].'：'.$constypes[2], 'cons_type' => 2, 'cons_score' => $cons_score, 'cons_status' => 4, 'cons_time' => $web_time);
				$DB->insert($DB->table('consumes'), $cons_data);
				$DB->query("UPDATE ".$DB->table('users')." SET user_score=user_score - ".$cons_score." WHERE user_id='".$myself['user_id']."' LIMIT 1");
			}
		
			alert('网站提交成功！', $page_url);
		} elseif ($_POST['do'] == 'saveedit') {
			$web_id = intval($_POST['web_id']);
			$where = array('web_id' => $web_id);
			$DB->update($table, $web_data, $where);
			
			$stat_data = array(
				'web_ip' => $web_ip,
				'web_grank' => $web_grank,
				'web_brank' => $web_brank,
				'web_srank' => $web_srank,
				'web_arank' => $web_arank,
				'web_utime' => $web_time,
			);
			$DB->update($DB->table('webdata'), $stat_data, $where);
			
			alert('网站编辑成功！', $page_url);
		}
	}
}

smarty_output($tplfile);
?>