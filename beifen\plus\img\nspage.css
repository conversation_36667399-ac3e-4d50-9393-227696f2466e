/*---------------SPAGE:评论,投票,友情链接*/
*{
	padding:0px;
	margin:0px;
	font-family:Verdana, Arial, Helvetica, sans-serif;
}
html{
	
}
body{
	background:#F7F7F7;
	font-size:12px;
}
a{
	color:#0033CC;
}
a:hover{
	color:#F00;
	text-decoration:none;
}
img{
	border:none;
}
input{
	vertical-align:middle;
	font-size:12px;
}
select{
	font-size:12px;
}

.mceneter{
	margin:0px auto;
}

.main{
	width:800px;
	border:4px solid #F0F0F0;
	background:#FFF;
	padding:10px 20px 10px 20px;
	margin-top:20px;
}
.main .toplogo{
	clear:both;
	width:800px;
	height:60px;

}
.main .toplogo .logo{
	width:200px;
	float:left;
}
.main .toplogo .title{
	width:510px;
	height:15px;
	float:right;
	padding:5px 10px 0px 0px;
	letter-spacing:3px; 
	font-weight:bold;
	line-height:12px;
	color:#555;
	text-align:right;
	background:#EEE url(nspage_tophr.gif) left center no-repeat;
	margin-top:20px;
}
.main .cmain{
	clear:both;
	overflow:hidden;
}

.main .cmain .ctitle{
	width:800px;
	height:25px;
	margin-top:10px;
}
.main .cmain .ctitle h1{
	width:190px;
	height:25px;
	font-size:12px;
	text-indent:10px;
	letter-spacing:5px;
	line-height:25px;
	color:#777;
	display:block;
	background:url(nspage_mtitle.gif) no-repeat;
	float:left;
}
.main .cmain .ctitle span{
	width:520px;
	height:12px;
	overflow:hidden;
	float:right;
	border-bottom:1px solid #EEE;
}
.main .cbox{
	width:780px;
	padding-bottom:10px;
}
.copyright{
	width:760px;
	text-align:right;
	font-size:11px;
	line-height:25px;
}



.linklist{
	width:700px;
}
.linklist .txtlink{
	width:700px;
	overflow:hidden;
}
.linklist .txtlink a{
	display:block;
	float:left;
	height:15px;
	line-height:15px;
	padding-right:16px;
	padding-left:16px;
	margin:17px 0px 0px -1px;
	white-space:nowrap;
	border-left:1px solid #EEE;
}

.linklist .imglink{
	width:700px;
	overflow:hidden;
	margin-top:10px;
}
.linklist .imglink a{
	width:88px;
	height:31px;
	display:block;
	float:left;
	overflow:hidden;
	border:3px solid #FFF;
	margin:10px 12px 2px 8px;
}
.linklist .imglink a:hover{
	border:3px solid #EEE;
}
.linklist .imglink a img{
	width:88px;
	height:31px;
}

.linkadd {
	margin-top:20px;
}
.linkadd dl{
	width:600px;
	clear:both;
	padding-top:6px;
	overflow:hidden;
}
.linkadd dl dt{
	display:block;
	float:left;
	width:110px;
	height:31px;
	text-align:right;
	line-height:27px;
}
.linkadd dl dd {
	width:480px;
	float:right;
}
.linkadd dl dd input{
	font-size:12px;
	border:1px solid #888;
	padding:4px;
	background:url(login_input_bg.gif) left top no-repeat;
	margin-right:5px;
}
.linkadd dl dd select{
	margin:4px 0px 0px 0px;
}
.linkadd dl dd img{
	vertical-align:middle;
}
.linkadd dl dd textarea{
	font-size:12px;
	border:1px solid #888;
	padding:4px;
	background:url(login_input_bg.gif) left top no-repeat;
}
.linkadd .submit{
	padding:10px 0px 0px 120px;
	float:left;
}
.linkadd .submit input{
	font-size:14px;
	padding:2px 5px 2px 5px;
}



.votelist{
	width:700px;
	margin-top:16px;
}
.votelist .vtitle h2{
	font-size:16px;
	color:#222;
	letter-spacing:2px;
	font-weight:normal;
}
.votelist .vtitle span{
	line-height:35px;
	color:#666666;
	text-indent:10px;
}
.votelist .vbox{
	width:700px;
}
.votelist .vbox dl{
	width:600px;
	height:31px;
	clear:both;
	overflow:hidden;
	margin-top:7px;
}
.votelist .vbox dl dt{
	width:200px;
	float:left;
	text-align:right;
	line-height:15px;
	color:#333;
	font-weight:bold;
}
.votelist .vbox dl dd{
	width:390px;
	float:right;
	background:#EEE;
	height:13px;
	font-size:11px;
	line-height:11px;
	color:#777;
}
.votelist .vbox dl dd strong{
	color:#000;
	margin-right:2px;
}
.votelist .vbox dl dd span{
	height:13px;
	text-indent:3px;
	padding-right:3px;
	display:block;
	background:#690;
	color:#CD9;
	border-right:1px solid #CCC;
	float:left;
}
.votelist .vbox dl dd span strong{
	color:#FFF;
}

.maplist{
	line-height:21px;
}
.maplist * b{
	display:block;
	font-size:14px;
	margin-top:16px;
}

.coolbg {
  border:0px;
  border-right: 1px solid #ACACAC;
  border-bottom: 1px solid #ACACAC;
  background-color: #F1F8B4;
  padding:2px;
  padding-right:5px;
  padding-left:5px;
  background: url(wbg.gif) #EFF7D0;
  cursor:pointer;
}

.listtable {
	width:100%;
	background:#dedede;
	margin-bottom:8px;
}

.listtable td {
	padding-left: 5px;
}

.left {
	float:left;
}

.right {
	float:right;
}
