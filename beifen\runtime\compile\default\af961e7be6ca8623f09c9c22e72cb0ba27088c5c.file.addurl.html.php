<?php /* Smarty version Smarty-3.1.18, created on 2023-11-06 22:01:09
         compiled from "/www/wwwroot/www.8t.lv/templet/default/addurl.html" */ ?>
<?php /*%%SmartyHeaderCode:5043658416548ebd7b2a6e6-18625291%%*/if(!defined('SMARTY_DIR')) exit('no direct access allowed');
$_valid = $_smarty_tpl->decodeProperties(array (
  'file_dependency' => 
  array (
    'af961e7be6ca8623f09c9c22e72cb0ba27088c5c' => 
    array (
      0 => '/www/wwwroot/www.8t.lv/templet/default/addurl.html',
      1 => 1699278599,
      2 => 'file',
    ),
  ),
  'nocache_hash' => '5043658416548ebd7b2a6e6-18625291',
  'function' => 
  array (
  ),
  'version' => 'Smarty-3.1.18',
  'unifunc' => 'content_6548ebd7c09d18_53705130',
  'variables' => 
  array (
    'site_name' => 0,
    'css_path' => 0,
    'site_root' => 0,
    'option' => 0,
    'js_path' => 0,
    'action' => 0,
    'cate_pids' => 0,
    'item' => 0,
    'row' => 0,
    'do' => 0,
    'new' => 0,
  ),
  'has_nocache_code' => false,
),false); /*/%%SmartyHeaderCode%%*/?>
<?php if ($_valid && !is_callable('content_6548ebd7c09d18_53705130')) {function content_6548ebd7c09d18_53705130($_smarty_tpl) {?><!DOCTYPE HTML>

<html>

<head>

<title>网站提交收录入口_网站提交大全  - <?php echo $_smarty_tpl->tpl_vars['site_name']->value;?>
</title>

<meta charset="utf-8">

<meta name="Keywords" content="网站提交,网站提交大全,网站提交收录入口" />

<meta name="Description" content="这里是8T网站目录收录提交通道,将你的网站提交8T目录站,让搜索引擎快速收录你的网站,本站每天大量蜘蛛光顾。建议你收藏8T网站目录免登陆提交入口。" />

<link href="<?php echo $_smarty_tpl->tpl_vars['css_path']->value;?>
default/style.css" rel="stylesheet" type="text/css" />

<script type="text/javascript">var $root = '<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
'; var $linktype = '<?php echo $_smarty_tpl->tpl_vars['option']->value['link_struct'];?>
';</script>

<script type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['js_path']->value;?>
jquery.min.js"></script>

<script type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['js_path']->value;?>
common.js"></script>

<script type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['js_path']->value;?>
clipboard.min.js"></script>
<?php echo $_smarty_tpl->getSubTemplate ("header-public.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>

</head>



<body>
<?php echo $_smarty_tpl->getSubTemplate ("header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>

<div class="wrapper">
	<div class="crumb">当前位置：<a href="http://www.8t.lv/">8T网站目录</a> &rsaquo; 网站提交大全</div>
	

	
	<?php if ($_smarty_tpl->tpl_vars['action']->value!='add') {?>
	<div class="allcate addurl-left">
		<ul>
			<li>有*标志都为必填项，在提交前认真检查您的资料是否正确。</li>
			<li>所提供网站资料要经过本站审核，但本站不保证一定收录,本站优先收录做好本站友情链接的网站。</li>
			<li>不收录有反动、色情、赌博等不良内容或提供不良内容链接的网站，以及网站名称或内容违反国家有关法规的网站， 如您的网站有上述内容或链接，请您不要登录；</li>
			<li>有些提供主页空间的网站含有色情等不良内容的广告 ( 包括弹出窗口 ) 或链接，本站不收录此类主页空间下的网站。如果您的主页使用的恰好是此类主页空间，您可以更换主页空间之后再来登陆；</li>
			<li>不收录无实用内容的网站，如您的网站正在建设中，尚无完整内容，请不必现在登录，欢迎您在网站建设完成后再来登陆；</li>
			<li>不收录含有病毒、木马，弹出插件或恶意更改他人电脑设置的网站及有多个弹出窗口广告的网站；</li>
			<li>对挂靠别人的网站下的网站 ( 即没有自己单独域名的网站 ) ，本站将不予收录；</li>
			<li>不收录在正常情况下无法正常连接或浏览的网站；</li>
			<li>本站保留是否收录您的网站的权力以及在本站网址数据库中相关内容的编辑决定权；</li>
		</ul>
		<div class="blank11"></div>
		<div class="addLinks">
			<p>
				<input type="text" id="siteName" value="8T分类目录" /><button data-clipboard-text="8T分类目录">点击复制</button>
			</p>
			<div class="blank11"></div>
			<p>
				<input type="text" id="siteUrl" value="http://www.8t.lv/" /><button data-clipboard-text="http://www.8t.lv/">点击复制</button>
			</p>
		</div>
		<div class="blank11"></div>
		<div class="daili"><a rel="nofollow" href="javascript:if(confirm('您是代理吗？' + '\n' + '不是选取消，点下面的游客提交入口！'))location='/member/?mod=login'">代理登录提交入口</a></div>
		<div class="blank11"></div>
		<div class="youke"><a rel="nofollow" href="javascript:if(confirm('提交之前请添加友链，否则选择付费快审！QQ：190323122'))location='/addurl/add.html'">游客免注册提交入口</a></div>
	</div>
	<div class="addurl-right">
		<div class="allcate">
			<div class="newbox-title">更多网站提交入口</div>
			<ul class="spider">
				<li><a target="_blank" href="http://www.gvz.cc/2.html">各位站网站提交入口</a></li>
				<li><a target="_blank" href="http://link.chaobie.com/help.html">超别导航提入口</a></li>
				<li><a target="_blank" href="http://dir.chaobie.com/member/?mod=login">超别目录提交入口</a></li>
				<li><a target="_blank" href="http://www.dirv.cn/member/?mod=website&act=add">迪威目录交登录入口</a></li>
				<li><a target="_blank" href="#">更多期待您的加入</a></li>
				<li><a target="_blank" href="#">更多期待您的加入</a></li>
			</ul>
		</div>
		<div class="blank10"></div>
		<div class="allcate">
			<div class="newbox-title">网站分类目录提交入口</div>
			<ul class="dir">
				
			</ul>
		</div>
		<div class="blank10"></div>
		<div class="allcate">
			<div class="newbox-title">高质量导航站提交入口</div>
			<ul class="dir">
				
			</ul>
		</div>
	</div>
	
	<script type="text/javascript">
	(function($) {
		$.fn.typewriter = function() {
			this.each(function() {
				var $ele = $(this), str = $ele.html(), progress = 0;
				$ele.html('');
				var timer = setInterval(function() {
					var current = str.substr(progress, 1);
					if (current == '<') {
						progress = str.indexOf('>', progress) + 1;
					} else {
						progress++;
					}
					$ele.html(str.substring(0, progress) + (progress & 1 ? '_' : ''));
					if (progress >= str.length) {
						clearInterval(timer);
					}
				}, 75);
			});
			return this;
		};
	})(jQuery);
	$(function(){
		$(".addurl-left>ul").typewriter();
	})
	</script>
	<script>
    window.onload = function(){
    	var btns = document.querySelectorAll('button');
	    var clipboard = new Clipboard(btns);

	    clipboard.on('success', function(e) {
	        console.log(e);
			alert('复制成功');
	    });

	    clipboard.on('error', function(e) {
	        console.log(e);
			alert('复制失败');
	    });
    }
	</script>
	<?php }?>

	<?php if ($_smarty_tpl->tpl_vars['action']->value=='add') {?>
		<div class="formbox allcate add-left">
			 <i id="xxxxx">站点数据调整清理中~~<br>禁止游客提交~~<br>付费收录联系QQ：190323122<br></i>
			<style>
				i#xxxxx {
				           position: absolute;
    top: 0px;
    left: 2px;
    z-index: 999;
    width: 443px;
    height: 230px;
    font-size: 29px;
    color: #000;
    padding-top: 388px;
    padding-left: 43px;
    background: #fff;
                  display:none
				}
              li.hqyzm1 img {
    display: inline-block;
    float: left;
    margin-left: 30px;
}

span.hqyzm {
    font-size: 18px;
    margin-left: 20px;
    display: inline-block;
    width: 254px;
    text-align: center;
}

span.hqyzm b {
    font-size: 28px;
    color:#e74c3c;
}

#key {
    width: 130px!important;
}
			</style>
			<div class="hint"><a href="javascript:history.go(-1)">返回上一页</a></div>
			<form name="myfrom" id="myfrom" method="post" action="?mod=addurl">
			<ul>
				
    			<li class="alertsite">网站快审 30 元，QQ：190323122 <a style="color: #fff;" target="_blank" rel="nofollow" href="https://www.8t.lv/diypage/9.html">点击进入高质量外链选购</a><br>
				严禁：seo关键词作为网站名称<br>
				严禁：危险，违规网站<br>
				网站简介字数太少，编辑简单的，一律进黑名单，网站一天审核三次！！
				编辑内容一律不准出现官网 最 等字眼！！
				</li>
				<!--<li><strong>选择分类：</strong><?php  $_smarty_tpl->tpl_vars['item'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['item']->_loop = false;
 $_from = $_smarty_tpl->tpl_vars['cate_pids']->value; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['item']->key => $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->_loop = true;
?><select name="level_<?php echo $_smarty_tpl->tpl_vars['item']->value;?>
" id="level_<?php echo $_smarty_tpl->tpl_vars['item']->value;?>
"></select><?php }
if (!$_smarty_tpl->tpl_vars['item']->_loop) {
?><select name="level_1" id="level_1"></select><?php } ?><input type="hidden" name="cate_id" id="cate_id" value="<?php echo !$_smarty_tpl->tpl_vars['row']->value['cate_id'] ? '0' : $_smarty_tpl->tpl_vars['row']->value['cate_id'];?>
"></li>-->
				<!--<li><strong>网站名称：</strong><input required="required" type="text" name="web_name" id="web_name" class="ipt" size="50" maxlength="8" placeholder="限制最多8个字" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['web_name'];?>
" /></li>-->
				<!--<li><strong>网站域名：</strong><input required="required" type="text" name="web_url" id="web_url" class="ipt" size="50" maxlength="50" placeholder="危险域名禁止" onKeyPress="strip_http()" onChange="strip_http()" onKeyUp="strip_http()" onblur="checkurl(this.value)" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['web_url'];?>
" /><input type="hidden" class="btn" id="meta_btn" value="抓取Meta" onclick="getmeta()"></li>-->
				<!--<li><strong>TAG标签：</strong><input required="required" type="text" name="web_tags" id="web_tags" class="ipt" size="50" placeholder="用逗号隔开关键字" maxlength="50" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['web_tags'];?>
" onBlur="javascript:this.value=this.value.replace(/，/ig,',');" /></li>-->
				<!--<li><strong>网站简介：</strong><textarea required="required" name="web_intro" id="web_intro" cols="50" rows="6" placeholder="网站简介低于55字直接进黑名单，不解释！" class="ipt"><?php echo $_smarty_tpl->tpl_vars['row']->value['web_intro'];?>
</textarea><i id="znum">0</i></li>-->
				<!--li><strong>服务器IP：</strong><input required="required" name="web_ip" type="text" class="ipt" id="web_ip" size="30" maxlength="30" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['web_ip'];?>
" readonly /><input type="button" class="btn" id="data_btn" value="获取数据" onclick="getdata()"></li-->
				<!--<li class="linobox"><strong>PageRank：</strong><input name="web_grank" type="text" class="ipt" id="web_grank" size="10" maxlength="2" value="<?php echo !$_smarty_tpl->tpl_vars['row']->value['web_grank'] ? '0' : $_smarty_tpl->tpl_vars['row']->value['web_grank'];?>
" readonly /></li>-->
				<!--<li class="linobox"><strong>BaiduRank：</strong><input name="web_brank" type="text" class="ipt" id="web_brank" size="10" maxlength="2" value="<?php echo !$_smarty_tpl->tpl_vars['row']->value['web_brank'] ? '0' : $_smarty_tpl->tpl_vars['row']->value['web_brank'];?>
" readonly /></li>-->
				<!--<li class="linobox"><strong>SogouRank：</strong><input name="web_srank" type="text" class="ipt" id="web_srank" size="10" maxlength="2" value="<?php echo !$_smarty_tpl->tpl_vars['row']->value['web_srank'] ? '0' : $_smarty_tpl->tpl_vars['row']->value['web_srank'];?>
" readonly /></li>-->
				<!--<li class="linobox"><strong>AlexaRank：</strong><input name="web_arank" type="text" class="ipt" id="web_arank" size="10" maxlength="10" value="<?php echo !$_smarty_tpl->tpl_vars['row']->value['web_arank'] ? '0' : $_smarty_tpl->tpl_vars['row']->value['web_arank'];?>
" readonly /></li>-->
    <!--          <li class="hqyzm1"><img src="/images/xsgwx.png" width="100px"><span class="hqyzm">微信扫描二维码关注后回复<br>“<b>蟹束阁大闸蟹</b>”<br>获取验证码下方填写</span></li>-->
				<!--<li><strong>验 证 码：</strong><canvas id="canvasCode" width="10" height="25" style="float: left"></canvas>&nbsp; <input id="key" type="text" value="">&nbsp;<a href="javascript:code();">刷新</a></li>-->
				<!--<li><strong>&nbsp;</strong><?php if ($_smarty_tpl->tpl_vars['action']->value=='edit') {?><input type="hidden" name="web_id" id="web_id" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['web_id'];?>
"><?php }?><input type="hidden" name="do" id="do" value="<?php echo $_smarty_tpl->tpl_vars['do']->value;?>
"><input type="submit" class="btn" value="提 交">&nbsp;<input type="reset" class="btn" value="重 填"></li>-->
				
			</ul>
			</form>
		</div>
		<div class="add-right">
			
			<div class="allcate">
				<div class="newbox-title">最新待审核站点</div>

                <ul id='tag'>

                	<?php  $_smarty_tpl->tpl_vars['new'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['new']->_loop = false;
 $_from = get_websites_status(0,50); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['new']->key => $_smarty_tpl->tpl_vars['new']->value) {
$_smarty_tpl->tpl_vars['new']->_loop = true;
?>

					<li><a target="_blank" title="<?php echo $_smarty_tpl->tpl_vars['new']->value['web_url'];?>
" href="<?php echo $_smarty_tpl->tpl_vars['new']->value['web_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['new']->value['web_name'];?>
</a></li>

                   	<?php } ?>

                </ul>
			</div>
           	<div class="blank10"></div>
            <div class="allcate">
                <div class="newbox-title">最新黑名单站点</div>

                <ul id='tag'>

                	<?php  $_smarty_tpl->tpl_vars['new'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['new']->_loop = false;
 $_from = get_websites_black(0,50); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['new']->key => $_smarty_tpl->tpl_vars['new']->value) {
$_smarty_tpl->tpl_vars['new']->_loop = true;
?>

					<li><a target="_blank" title="<?php echo $_smarty_tpl->tpl_vars['new']->value['web_url'];?>
"><?php echo $_smarty_tpl->tpl_vars['new']->value['web_name'];?>
</a></li>

                   	<?php } ?>

                </ul>
            </div>

		</div>
	<script src="/public/scripts/linkage.select.js" type="text/javascript"></script>
	<script type="text/javascript">
	var options = {
		ajax : '/?mod=ajaxgets&type=website',
		auto : true,
		field_name : '[name=cate_id]'
	};
	
	var sel = new LinkageSelect(options);
	sel.bind('#level_1');
	/*<?php if ($_smarty_tpl->tpl_vars['action']->value=='add') {?>
	sel.bind('#level_1');
	<?php } else { ?>
	<?php  $_smarty_tpl->tpl_vars['item'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['item']->_loop = false;
 $_smarty_tpl->tpl_vars['key'] = new Smarty_Variable;
 $_from = $_smarty_tpl->tpl_vars['cate_pids']->value; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['item']->key => $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->_loop = true;
 $_smarty_tpl->tpl_vars['key']->value = $_smarty_tpl->tpl_vars['item']->key;
?>
	sel.bind('#level_<?php echo $_smarty_tpl->tpl_vars['item']->value;?>
', <?php echo $_smarty_tpl->tpl_vars['item']->value;?>
);
	<?php } ?>
	<?php }?>*/

	</script>
	 <script>
 	function code(){	
					/*var num1 = Math.ceil(Math.random()*99);	
					var num2 = Math.ceil(Math.random()*99);	
					var num = num1+num2;
					var dou = Math.ceil(Math.random()*25);
					var dou1 = Math.ceil(Math.random()*25);
					var dou2 = Math.ceil(Math.random()*25);
					var canvas=$('#canvasCode')[0];
					var code=canvas.getContext('2d');
					code.fillStyle='#27ae60';
					code.fillRect(0,0,110,25);
					code.font="17px Arial";
					code.fillStyle='#fff';
					code.fillText(num1,10,20);
					code.fillText(num2,60,20);
					code.font="18px Arial";
					code.fillText("+",40,20);
					code.fillText("=",90,20);
					code.strokeStyle='#fff';
					code.beginPath();
					code.moveTo(dou,dou2);
					code.lineTo(100,dou);
					code.moveTo(dou2,dou1);
					code.lineTo(100,dou2);
					code.moveTo(dou2,dou);
					code.lineTo(100,dou1);
					code.stroke();
					code.closePath();
					$(".btn[type='submit']").attr({"disabled":"disabled"});	
					$('#num1').html(num1);	
					$('#num2').html(num2);	*/
					$('#key').bind('input propertychange', function() { 
						var ab = $('#key').val(); 
						if(ab == '777777'){	
							console.log('成功');	
							$(".btn[type='submit']").removeAttr("disabled");
							$(".btn[type='submit']").addClass('tijiao'); 
						};	
					});	
					$('#web_intro').bind('input propertychange', function() {	
							var web_intro = $('#web_intro').val().length;	
							console.log(web_intro);
							$('#znum').text(web_intro);
							if (web_intro>55) {
								$('#znum').css({'color':'#27ae60'});
							}else{
								$('#znum').css({'color':'#e74c3c'});
							}
					})
				}	
				code(); 
 </script>
 <style>
 i#znum {
    margin-left: 3px;
    font-size: 18px;
    color: #e74c3c;
    position: absolute;
    line-height: 18px;
}
</style>
</div>
          
 <?php }?>

<?php echo $_smarty_tpl->getSubTemplate ("footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>


</body>

</html><?php }} ?>
