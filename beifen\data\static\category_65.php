<?php
//File name: category_65.php
//Creation time: 2023-11-07 01:09:37

if (!defined('IN_IWEBDIR')) exit('Access Denied');

$static_data = array(
	'cate_id' => '65',
	'root_id' => '3',
	'cate_mod' => 'website',
	'cate_name' => '博客',
	'cate_dir' => 'boke',
	'cate_url' => '',
	'cate_isbest' => '0',
	'cate_keywords' => '博客，博客网址大全',
	'cate_description' => '博客网址大全是为您精心挑选出国内外最优秀的博客网站，并保持定期更新和检查，确保您以最安全、最方便的方式，找到您的需要；博客网址大全下设分类：博客，微博，博客周边，网摘/书签。',
	'cate_arrparentid' => '0,3',
	'cate_arrchildid' => '65',
	'cate_childcount' => '0',
	'cate_postcount' => '116',
);
?>