<?php
if (!defined('IN_MEMBER')) exit('Access Denied');

$page_name = '修改密码';
$page_url = '?mod=editpwd';
$tplfile = 'editpwd.html';
$table = $DB->table('users');

if (!$smarty->isCached($tplfile)) {
	$smarty->assign('page_name', $page_name);
	$smarty->assign('crumb', get_crumb().' &rsaquo; '.$page_name);
	
	if ($_POST['do'] == 'save') {
		$old_pass = trim($_POST['old_pass']);
		$new_pass = trim($_POST['new_pass']);
		$new_pass1 = trim($_POST['new_pass1']);
		
		if (empty($old_pass)) {
			alert('请输入原始密码！');
		} else {
			$user = $DB->fetch_one("SELECT user_pass FROM $table WHERE user_id='".$myself['user_id']."'");
			if ($user['user_pass'] != md5($old_pass)) {
				alert('您输入的原始密码不正确！');
			}
		}
		
		if (empty($new_pass)) {
			alert('请输入新密码！');
		}
		
		if (empty($new_pass1)) {
			alert('请输入确认密码！');
		}
		
		if ($new_pass != $new_pass1) {
			alert('两次密码输入不一致，请重新输入！');
		}
		
		$data = array('user_pass' => md5($new_pass));
		$where = array('user_id' => $myself['user_id'],);
		
		$DB->update($table, $data, $where);
		alert('账户密码修改成功！', $page_url);
	}
}

smarty_output($tplfile);
?>