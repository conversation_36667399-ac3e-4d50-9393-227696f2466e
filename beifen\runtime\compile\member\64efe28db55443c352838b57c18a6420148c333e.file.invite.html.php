<?php /* Smarty version Smarty-3.1.18, created on 2023-11-06 23:00:21
         compiled from "/www/wwwroot/www.8t.lv/templet/member/invite.html" */ ?>
<?php /*%%SmartyHeaderCode:12592045826548ff856ac029-90432932%%*/if(!defined('SMARTY_DIR')) exit('no direct access allowed');
$_valid = $_smarty_tpl->decodeProperties(array (
  'file_dependency' => 
  array (
    '64efe28db55443c352838b57c18a6420148c333e' => 
    array (
      0 => '/www/wwwroot/www.8t.lv/templet/member/invite.html',
      1 => 1418038320,
      2 => 'file',
    ),
  ),
  'nocache_hash' => '12592045826548ff856ac029-90432932',
  'function' => 
  array (
  ),
  'variables' => 
  array (
    'page_name' => 0,
    'action' => 0,
    'option' => 0,
    'site_url' => 0,
    'myself' => 0,
    'users' => 0,
    'row' => 0,
    'showpage' => 0,
  ),
  'has_nocache_code' => false,
  'version' => 'Smarty-3.1.18',
  'unifunc' => 'content_6548ff85700f41_40219173',
),false); /*/%%SmartyHeaderCode%%*/?>
<?php if ($_valid && !is_callable('content_6548ff85700f41_40219173')) {function content_6548ff85700f41_40219173($_smarty_tpl) {?><?php echo $_smarty_tpl->getSubTemplate ("header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>




<div class="title"><?php echo $_smarty_tpl->tpl_vars['page_name']->value;?>
</div>

<div class="content">

	<?php if ($_smarty_tpl->tpl_vars['action']->value=='list') {?>

	<div class="listbox">

		<?php if ($_smarty_tpl->tpl_vars['option']->value['invite_register_num']>0) {?><div style="color: #f00; font: bold 14px/30px normal; padding-bottom: 10px;">邀请 <?php echo $_smarty_tpl->tpl_vars['option']->value['invite_register_num'];?>
 个好友送 <?php echo $_smarty_tpl->tpl_vars['option']->value['invite_register_score'];?>
 积分，复制链接邀请好友加入 <input type="text" size="80" class="ipt" value="<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
member/?mod=register&uid=<?php echo $_smarty_tpl->tpl_vars['myself']->value['user_id'];?>
" readonly></div><?php }?>

		<table width="100%" border="0" cellspacing="1" cellpadding="0">

			<tr>

				<th>ID</th>

				<th>好友昵称</th>

				<th>加入时间</th>

			</tr>

			<?php  $_smarty_tpl->tpl_vars['row'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['row']->_loop = false;
 $_from = $_smarty_tpl->tpl_vars['users']->value; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
 $_smarty_tpl->tpl_vars['smarty']->value['foreach']['num']['iteration']=0;
foreach ($_from as $_smarty_tpl->tpl_vars['row']->key => $_smarty_tpl->tpl_vars['row']->value) {
$_smarty_tpl->tpl_vars['row']->_loop = true;
 $_smarty_tpl->tpl_vars['smarty']->value['foreach']['num']['iteration']++;
?>

			<tr>

				<td><?php echo $_smarty_tpl->getVariable('smarty')->value['foreach']['num']['iteration'];?>
</td>

				<td><?php echo $_smarty_tpl->tpl_vars['row']->value['nick_name'];?>
</td>

				<td><?php echo date('Y-m-d',$_smarty_tpl->tpl_vars['row']->value['join_time']);?>
</td>

			</tr>

			<?php }
if (!$_smarty_tpl->tpl_vars['row']->_loop) {
?>

			<tr><td colspan="3">您还未邀请任何好友！</td></tr>

			<?php } ?>

		</table>

	</div>

	<div class="showpage"><?php echo $_smarty_tpl->tpl_vars['showpage']->value;?>
</div>

	<?php }?>

</div>

            

<?php echo $_smarty_tpl->getSubTemplate ("footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>
<?php }} ?>
