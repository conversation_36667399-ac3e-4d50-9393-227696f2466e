<?php
//File name: category_56.php
//Creation time: 2023-11-07 01:09:37

if (!defined('IN_IWEBDIR')) exit('Access Denied');

$static_data = array(
	'cate_id' => '56',
	'root_id' => '3',
	'cate_mod' => 'website',
	'cate_name' => '资讯',
	'cate_dir' => 'ITzixun',
	'cate_url' => '',
	'cate_isbest' => '0',
	'cate_keywords' => 'IT，IT资讯网址大全',
	'cate_description' => 'IT网址大全是为您精心挑选出国内外最优秀的IT网站，并保持定期更新和检查，确保您以最安全、最方便的方式，找到您的需要；IT网址大全下设分类：IT资讯，IT博客，网络编辑。',
	'cate_arrparentid' => '0,3',
	'cate_arrchildid' => '56',
	'cate_childcount' => '0',
	'cate_postcount' => '160',
);
?>