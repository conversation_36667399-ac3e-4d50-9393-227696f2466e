<?php /* Smarty version Smarty-3.1.18, created on 2023-11-06 21:31:57
         compiled from "/www/wwwroot/www.8t.lv/templet/system/consume.html" */ ?>
<?php /*%%SmartyHeaderCode:238422456548eacde9f368-54478389%%*/if(!defined('SMARTY_DIR')) exit('no direct access allowed');
$_valid = $_smarty_tpl->decodeProperties(array (
  'file_dependency' => 
  array (
    '53b17b6f1caf8890fec06b06efaaacd897eabee4' => 
    array (
      0 => '/www/wwwroot/www.8t.lv/templet/system/consume.html',
      1 => 1413167880,
      2 => 'file',
    ),
  ),
  'nocache_hash' => '238422456548eacde9f368-54478389',
  'function' => 
  array (
  ),
  'variables' => 
  array (
    'action' => 0,
    'page_name' => 0,
    'fileurl' => 0,
    'user_id' => 0,
    'type_id' => 0,
    'sort' => 0,
    'order' => 0,
    'key_url' => 0,
    'status' => 0,
    'constype_option' => 0,
    'consumes' => 0,
    'item' => 0,
    'showpage' => 0,
  ),
  'has_nocache_code' => false,
  'version' => 'Smarty-3.1.18',
  'unifunc' => 'content_6548eacdf3fc53_87605255',
),false); /*/%%SmartyHeaderCode%%*/?>
<?php if ($_valid && !is_callable('content_6548eacdf3fc53_87605255')) {function content_6548eacdf3fc53_87605255($_smarty_tpl) {?><?php echo $_smarty_tpl->getSubTemplate ("header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>


	<?php if ($_smarty_tpl->tpl_vars['action']->value=='list') {?>
    <h3 class="title"><em><?php echo $_smarty_tpl->tpl_vars['page_name']->value;?>
</em></h3>
	<div class="listbox">  
		<form name="mform" method="post" action="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">
		<div class="toolbar">
			<select name="act" id="act" class="sel">
			<option value="del" style="color: #f00;">删除选定</option>
			</select>
			<input type="submit" class="btn" value="应用" onClick="if(IsCheck('cons_id[]')==false){alert('请指定您要操作的记录ID！');return false;}else{return confirm('确认执行此操作吗？');}">
			<select class="sel" onChange="if(this.options[this.selectedIndex].value!=''){location='<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
?status='+this.options[this.selectedIndex].value+'&user_id=<?php echo $_smarty_tpl->tpl_vars['user_id']->value;?>
&type_id=<?php echo $_smarty_tpl->tpl_vars['type_id']->value;?>
&sort=<?php echo $_smarty_tpl->tpl_vars['sort']->value;?>
&order=<?php echo $_smarty_tpl->tpl_vars['order']->value;?>
<?php echo $_smarty_tpl->tpl_vars['key_url']->value;?>
';}">
			<option value="0">所有状态</option>
			<option value="1"<?php echo opt_selected($_smarty_tpl->tpl_vars['status']->value,1);?>
>已支付</option>
			<option value="2"<?php echo opt_selected($_smarty_tpl->tpl_vars['status']->value,2);?>
>等待发货</option>
			<option value="3"<?php echo opt_selected($_smarty_tpl->tpl_vars['status']->value,3);?>
>确认收货</option>
			<option value="4"<?php echo opt_selected($_smarty_tpl->tpl_vars['status']->value,4);?>
>交易完成</option>
			</select>
			<select class="sel" onChange="if(this.options[this.selectedIndex].value!=''){location='<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
?status=<?php echo $_smarty_tpl->tpl_vars['status']->value;?>
&user_id=<?php echo $_smarty_tpl->tpl_vars['user_id']->value;?>
&type_id='+this.options[this.selectedIndex].value+'&sort=<?php echo $_smarty_tpl->tpl_vars['sort']->value;?>
&order=<?php echo $_smarty_tpl->tpl_vars['order']->value;?>
<?php echo $_smarty_tpl->tpl_vars['key_url']->value;?>
';}">
			<option value="0" selected>消费类型</option>
			<?php echo $_smarty_tpl->tpl_vars['constype_option']->value;?>

			</select>
			<select class="sel" onChange="if(this.options[this.selectedIndex].value!=''){location='<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
?status=<?php echo $_smarty_tpl->tpl_vars['status']->value;?>
&user_id=<?php echo $_smarty_tpl->tpl_vars['user_id']->value;?>
&type_id=<?php echo $_smarty_tpl->tpl_vars['type_id']->value;?>
&sort='+this.options[this.selectedIndex].value+'<?php echo $_smarty_tpl->tpl_vars['key_url']->value;?>
';}">
			<option value="1"<?php echo opt_selected($_smarty_tpl->tpl_vars['sort']->value,1);?>
>按消费时间排列</option>
			</select>
			<select class="sel" onChange="if(this.options[this.selectedIndex].value!=''){location='<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
?status=<?php echo $_smarty_tpl->tpl_vars['status']->value;?>
&user_id=<?php echo $_smarty_tpl->tpl_vars['user_id']->value;?>
&type_id=<?php echo $_smarty_tpl->tpl_vars['type_id']->value;?>
&sort=<?php echo $_smarty_tpl->tpl_vars['sort']->value;?>
&order='+this.options[this.selectedIndex].value+'<?php echo $_smarty_tpl->tpl_vars['key_url']->value;?>
';}">
			<option value="DESC"<?php echo opt_selected($_smarty_tpl->tpl_vars['order']->value,'DESC');?>
>降序</option>
			<option value="ASC"<?php echo opt_selected($_smarty_tpl->tpl_vars['order']->value,'ASC');?>
>升序</option>
			</select>
		</div>
		
    	<table width="100%" border="0" cellspacing="1" cellpadding="0">
			<tr>
				<th><input type="checkbox" id="ChkAll" onClick="CheckAll(this.form)"></th>
				<th>ID</th>
				<th>会员昵称</th>
				<th>订单编号</th>
				<th>交 易 号</th>
                <th>交易状态</th>
                <th>消费名称</th>
				<th>消费类型</th>
				<th>消费金额</th>
				<th>消费积分</th>
				<th>属性状态</th>
				<th>消费时间</th>
				<th>操作选项</th>
			</tr>
			<?php  $_smarty_tpl->tpl_vars['item'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['item']->_loop = false;
 $_from = $_smarty_tpl->tpl_vars['consumes']->value; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['item']->key => $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->_loop = true;
?>
			<tr>
				<td><input name="cons_id[]" type="checkbox" value="<?php echo $_smarty_tpl->tpl_vars['item']->value['cons_id'];?>
"></td>
				<td><?php echo $_smarty_tpl->tpl_vars['item']->value['cons_id'];?>
</td>
				<td><?php echo $_smarty_tpl->tpl_vars['item']->value['nick_name'];?>
</td>
				<td><?php echo $_smarty_tpl->tpl_vars['item']->value['order_id'];?>
</td>
				<td><?php echo $_smarty_tpl->tpl_vars['item']->value['trade_no'];?>
</td>
                <td><?php echo $_smarty_tpl->tpl_vars['item']->value['trade_status'];?>
</td>
                <td><?php echo $_smarty_tpl->tpl_vars['item']->value['cons_name'];?>
</td>
				<td><?php echo $_smarty_tpl->tpl_vars['item']->value['cons_type'];?>
</td>
				<td><?php echo $_smarty_tpl->tpl_vars['item']->value['cons_money'];?>
</td>
				<td><?php echo $_smarty_tpl->tpl_vars['item']->value['cons_score'];?>
</td>
				<td><?php echo $_smarty_tpl->tpl_vars['item']->value['cons_attr'];?>
</td>
				<td><?php echo $_smarty_tpl->tpl_vars['item']->value['cons_time'];?>
</td>
				<td><?php echo $_smarty_tpl->tpl_vars['item']->value['cons_opera'];?>
</td>
			</tr>
			<?php }
if (!$_smarty_tpl->tpl_vars['item']->_loop) {
?>
			<tr><td colspan="13">无任何记录！</td></tr>
			<?php } ?>
		</table>
        </form>
        <div class="pagebox"><?php echo $_smarty_tpl->tpl_vars['showpage']->value;?>
</div>
    </div>
    <?php }?>

<?php echo $_smarty_tpl->getSubTemplate ("footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>
<?php }} ?>
