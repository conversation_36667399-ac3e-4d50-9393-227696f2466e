<?php /* Smarty version Smarty-3.1.18, created on 2023-11-08 01:14:59
         compiled from "/www/wwwroot/www.8t.lv/templet/default/category.html" */ ?>
<?php /*%%SmartyHeaderCode:3076288196548fe09851509-55925307%%*/if(!defined('SMARTY_DIR')) exit('no direct access allowed');
$_valid = $_smarty_tpl->decodeProperties(array (
  'file_dependency' => 
  array (
    'e9cf8bcf046c87d1ac6efae37524812ebe91809a' => 
    array (
      0 => '/www/wwwroot/www.8t.lv/templet/default/category.html',
      1 => 1699370043,
      2 => 'file',
    ),
  ),
  'nocache_hash' => '3076288196548fe09851509-55925307',
  'function' => 
  array (
  ),
  'version' => 'Smarty-3.1.18',
  'unifunc' => 'content_6548fe098c5036_99166875',
  'variables' => 
  array (
    'page_name' => 0,
    'site_name' => 0,
    'css_path' => 0,
    'site_root' => 0,
    'option' => 0,
    'js_path' => 0,
    'crumb' => 0,
    'rssfeed' => 0,
    'c' => 0,
    'sc' => 0,
    'new' => 0,
  ),
  'has_nocache_code' => false,
),false); /*/%%SmartyHeaderCode%%*/?>
<?php if ($_valid && !is_callable('content_6548fe098c5036_99166875')) {function content_6548fe098c5036_99166875($_smarty_tpl) {?><!DOCTYPE HTML>



<html>



<head>



<title><?php echo $_smarty_tpl->tpl_vars['page_name']->value;?>
_网站分类目录_中文分类目录 - <?php echo $_smarty_tpl->tpl_vars['site_name']->value;?>
</title>



<meta charset="utf-8">



<meta name="Keywords" content="分类目录,网站分类目录,中文分类目录,分类目录网站,35分类目录" />



<meta name="Description" content="8T分类目录栏目汇集所有类型网站分类目录，区块细化分类网站，能够第一时间让您快速找到适合自己需要的网站！" />



<link href="<?php echo $_smarty_tpl->tpl_vars['css_path']->value;?>
default/style.css" rel="stylesheet" type="text/css" />



<script type="text/javascript">var $root = '<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
'; var $linktype = '<?php echo $_smarty_tpl->tpl_vars['option']->value['link_struct'];?>
';</script>



<script type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['js_path']->value;?>
jquery.min.js"></script>



<script type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['js_path']->value;?>
common.js"></script>

<?php echo $_smarty_tpl->getSubTemplate ("header-public.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>


</head>







<body>



<?php echo $_smarty_tpl->getSubTemplate ("header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>




<div class="wrapper">



	<div class="crumb"><?php echo $_smarty_tpl->tpl_vars['crumb']->value;?>
&nbsp;<?php echo $_smarty_tpl->tpl_vars['rssfeed']->value;?>
</div>



    <div class="mainbox">



    	<div class="mainbox-left">



        	<div class="allcate">



				<?php  $_smarty_tpl->tpl_vars['c'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['c']->_loop = false;
 $_from = get_categories(); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['c']->key => $_smarty_tpl->tpl_vars['c']->value) {
$_smarty_tpl->tpl_vars['c']->_loop = true;
?>



                <?php if ($_smarty_tpl->tpl_vars['c']->value['cate_mod']=='website') {?>



            	<div class="clearfix catebox">



            		<h3><a href="<?php echo $_smarty_tpl->tpl_vars['c']->value['cate_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['c']->value['cate_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['c']->value['cate_name'];?>
</a> <em>(<?php echo $_smarty_tpl->tpl_vars['c']->value['cate_postcount'];?>
)</em></h3>



            		<ul>



						<?php  $_smarty_tpl->tpl_vars['sc'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['sc']->_loop = false;
 $_from = get_categories($_smarty_tpl->tpl_vars['c']->value['cate_id']); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['sc']->key => $_smarty_tpl->tpl_vars['sc']->value) {
$_smarty_tpl->tpl_vars['sc']->_loop = true;
?>



                		<li><a href="<?php echo $_smarty_tpl->tpl_vars['sc']->value['cate_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['sc']->value['cate_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['sc']->value['cate_name'];?>
</a> <em>(<?php echo $_smarty_tpl->tpl_vars['sc']->value['cate_postcount'];?>
)</em></li>



                		<?php } ?>



                	</ul>



				</div>



                <?php }?>



				<?php } ?>



            </div>



        </div>



        <div class="mainbox-right">



			





			<div class="newbox">



            	<div class="newbox-title">最新收录</div>



                <ul class="newbox-list">



                	<?php  $_smarty_tpl->tpl_vars['new'] = new Smarty_Variable; $_smarty_tpl->tpl_vars['new']->_loop = false;
 $_from = get_websites(0,30); if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array');}
foreach ($_from as $_smarty_tpl->tpl_vars['new']->key => $_smarty_tpl->tpl_vars['new']->value) {
$_smarty_tpl->tpl_vars['new']->_loop = true;
?>



					<li><span><?php echo $_smarty_tpl->tpl_vars['new']->value['web_ctime'];?>
</span><a href="<?php echo $_smarty_tpl->tpl_vars['new']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['new']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['new']->value['web_name'];?>
</a></li>



                   	<?php } ?>



                </ul>



            </div>







        </div>



    </div>



</div>



<?php echo $_smarty_tpl->getSubTemplate ("footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, null, array(), 0);?>




</body>



</html><?php }} ?>
