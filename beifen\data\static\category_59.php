<?php
//File name: category_59.php
//Creation time: 2023-11-07 01:09:37

if (!defined('IN_IWEBDIR')) exit('Access Denied');

$static_data = array(
	'cate_id' => '59',
	'root_id' => '3',
	'cate_mod' => 'website',
	'cate_name' => '软件',
	'cate_dir' => 'ruanjian',
	'cate_url' => '',
	'cate_isbest' => '0',
	'cate_keywords' => '软件，软件网址大全',
	'cate_description' => '软件网址大全是为您精心挑选出国内外最优秀的软件网站，并保持定期更新和检查，确保您以最安全、最方便的方式，找到您的需要；软件网址大全下设分类：常用软件，实用技巧，软件下载，驱动下载，装机软件，软件论坛。',
	'cate_arrparentid' => '0,3',
	'cate_arrchildid' => '59',
	'cate_childcount' => '0',
	'cate_postcount' => '174',
);
?>