@charset "utf-8";
/* global */
body, h1, h2, h3, h4, h5, h6, hr, p, blockquote, dl, dt, dd, ul, ol, li, pre, form, fieldset, legend, button, input, textarea, th, td {margin: 0; padding: 0;}
body, button, input, select, textarea {font: 12px/1.5 "Segoe UI", Tahoma, Arial, \5b8b\4f53, Sans-Serif;}
h1, h2, h3, h4, h5, h6 {font-size: 100%;}
address, cite, dfn, em, var {font-style: normal;}
code, kbd, pre, samp {font-family: courier new, courier, monospace;}
small {font-size: 12px;}
ul, ol {list-style: none;}
a {text-decoration: none;}
#myfrom input[type=submit],#myfrom input[type=reset],.siteinfo,a,div.allcate,div.allcate h3.listbox-title,div.allcate h3.scatbox-title,div.allcate:hover div.newbox-title,div.daili a,div.newbox,div.newbox div.newbox-title,div.topsite,div.topsite div.topsite-title,div.youke a{-webkit-transition:all .3s ease-out;-moz-transition:all .3s ease-out;-ms-transition:all .3s ease-out;-o-transition:all .3s ease-out;transition:all .3s ease-out}
a:hover{text-decoration:underline}
sup{vertical-align:text-top}
sub{vertical-align:text-bottom}
legend{color:#000}
fieldset,img{vertical-align:middle;border:0}
button,input,select,textarea{font-size:100%}
table{border-collapse:collapse;border-spacing:0}
.clearfix:after{clear:both;content:'.';display:block;font-size:0;height:1;visibility:hidden}
*html .clearfix{zoom:1}
.blue{color:#005F93!important}
.red{color:#E74C3C!important}
.green{color:#1ABC9C!important}
a.RSS{background-color:#E74C3C;color:#fff!important;font-size:12px;padding:0 2px;text-decoration:none!important}
a.RSS:hover{background-color:#F79186}
::selection{background:#E74C3C;color:#fff}
::-moz-selection{background:#E74C3C;color:#fff}
::-webkit-selection{background:#E74C3C;color:#fff}
/* custom */
body {background: #e9eaec;}
body, button, input, select, textarea {color: #1c1c1c; font-family: Arial, '宋体';}
a {color: #1c1c1c; text-decoration: none;}
a:hover {color: #E74C3C; text-decoration: underline;}
.blank10 {clear: both; display: block; font-size: 10px; height: 10px; line-height: 10px; width: 100%;}
.more {color: #999; font: 12px '宋体';}
.gre {color: #080;}
.org {color: #f60;}
/* wrapper */
.wrapper {margin: 0 auto; width: 1000px;}
.topbox {background: #E74C3C; border-bottom: solid 1px #4c7fbb; height: 40px;}
/* top login */
.login-text {float: left; font: bold 14px "Microsoft YaHei"; padding-top: 10px;}
.login-text a {color: #fff; text-decoration: none;}
.login-text a:hover {color: #980c20; font-weight: bold;}
.login-text span{color:red;}
.login-textt {float:left;font: 14px "Microsoft YaHei"; padding-top: 10px;padding-left: 250px;}

.login-textt a {color: #fff; text-decoration: none;}
.login-status {float: right; padding-top: 5px;}
.top-ulogin a {background: url(sprite.png) no-repeat; color: #fff; display: block; float: left; font-size: 13px; height: 32px; line-height: 32px; margin-right: 1px; text-align: center; text-decoration: none; width: 68px;}
.top-ulogin .qq {background: url(sprite.png) no-repeat -56px -290px; display: inline-block; *display: inline; *zoom: 1; height: 18px; width: 15px;}
.top-uinfo {color: #fff; padding-top: 2px;}
.top-uinfo a {color: #fff; margin: 0 5px;}
/* 首页hover */
div.allcate:hover,div.newbox:hover,.siteinfo:hover,div.topsite:hover{border: solid 1px #E74C3C;}
div.allcate:hover div.newbox-title,div.newbox:hover div.newbox-title,div.allcate:hover h3.scatbox-title,div.allcate:hover h3.listbox-title,div.topsite:hover div.topsite-title{border-bottom: solid 1px #F79186;}
.tuijian{color:red}
/* 广告 250px*/
.adcode {background: #fff; border: solid 1px transparent;box-shadow: 0 1px 2px rgba(0,0,0,0.05);padding: 8px;}
.adcode1 {float: right;}
/* sobox */
.sobox {height: 80px; padding-top: 10px;}
.logo {background: url(digg.png); float: left; height: 70px; width: 200px;    background-size: 200px 70px;}
.logo h1 {display: none;}
.search {float: left; margin-left: 100px; padding-top: 5px;}
.sofrm {display: block; padding-top: 14px; position: relative; width: 520px;}
.sipt:focus{border-color:#E74C3C;outline:0;-webkit-box-shadow:none;box-shadow:none}
.sbtn:focus{background-color:#F79186;outline:0;-webkit-box-shadow:none;box-shadow:none}
.sipt {background: #fff; border: solid 1px #E74C3C; border-right: 0; display: block; font-size: 14px; float: left; height: 19px; padding: 6px 6px 6px 90px; width: 350px;}
.sbtn { border: 0; color: #fff; background-color:#E74C3C; cursor: pointer; height: 33px; width: 71px;}
/* selopt */
#selopt {background: url(select.gif) no-repeat; height: 30px; width: 90px; position: absolute; left: 2px; top: 17px;}
#cursel {cursor: pointer; display: block; height: 28px; line-height: 28px; overflow: hidden; text-indent: 12px; width: 90px;}
#options {border: solid 1px #ccc; border-top: 0; display: none; list-style: none; position: absolute; left: -2px; width: 80px; z-index: 1000;}
#options li {background: #fff; clear: both; cursor: pointer;}
#options li a {color: #555; display: block; height: 25px; line-height: 25px; text-decoration: none; text-align: center;}
#options li a:hover {background-color: #F79186; color: #fff; display: block; text-decoration: none;}
.current {background: #97c853; color: #fff; display: block; text-decoration: none;}
/* navbar */
.navbar {background: #fff; border-top: solid 3px #E74C3C; border-right: solid 1px transparent; box-shadow: 0 1px 2px rgba(0,0,0,0.05); height: 40px;}
.navbar li {float: left; font: 14px normal; height: 40px; line-height: 40px; text-align: center; width: 99px;}
.navbar li a {color: #333; display: block; text-decoration: none;}
.navbar li a:hover {color: #E74C3C; font-weight: bold;}
.navbar .navline {background: #ccc; display: block; height: 20px; margin-top: 10px; width: 1px;}
.navbar .navcur {background: #E74C3C;}
.navbar .navcur a, .navbar .navcur a:hover {color: #fff; font-weight: bold;}
.navbar .navcur span {background: url(sprite.png) no-repeat -28px -295px; display: inline-block; *display: inline; *zoom: 1; height: 16px; width: 18px;}
/* homebox */
.homebox {}
.homebox-left {float: left; width: 740px;}
.homebox-right {float: right; width: 250px;}
/* bestbox */
.bestbox {background: #fff; border: solid 1px transparent;box-shadow: 0 1px 2px rgba(0,0,0,0.05);}
.bestbox-title {background: #f7f9fa; border-bottom: solid 1px #d8d8d8; font-size: 14px; height: 38px;}
.bestbox-title span {background: #fff; border-right: solid 1px #d8d8d8; display: block; font-weight: bold; height: 39px; line-height: 39px; text-align: center; width: 150px;}
.bestbox-body {padding: 2px 10px 10px 10px;}
.bestbox-list {padding-left: 30px;}
.bestbox-list li {float: left; font-size: 14px; height: 30px; line-height: 30px; overflow: hidden; white-space: nowrap; width: 109px;}
/* coolbox */
.coolbox {background: #fff; border: solid 1px transparent;box-shadow: 0 1px 2px rgba(0,0,0,0.05);}
.coolbox-title {display: block; font: bold 14px normal; padding-top: 15px;}
.coolbox-title span {background: #E74C3C; color: #fff; padding: 8px 15px;}
.coolbox-body {padding: 10px;}
.coolbox-list {padding-left: 10px;}
.coolbox-list li {font-size: 14px; height: 30px; line-height: 30px; white-space: nowrap;}
.coolbox-list li strong {display: block; float: left; font-weight: normal; width: 50px;}
.coolbox-list li strong a {color: #E74C3C;}
.coolbox-list li span {display: block; float:  left; overflow: hidden; width:  600px; white-space: nowrap;}
.coolbox-list li span a {margin-right:  30px;}
.coolbox-list li em {float: right;}
.coolbox-list .sline {background: #ccc; display: block; height: 1px; margin: 8px 0; width: auto;}
/* hcatebox */
.hcatebox {background: #fff; border: solid 1px transparent;box-shadow: 0 1px 2px rgba(0,0,0,0.05);}
.hcatebox-one {float: left; border-right: dotted 1px #d8d8d8; margin: 0 5px; width: 188px;}
.hcatebox-one dt {font: bold 14px normal; padding: 10px;}
.hcatebox-one dt a {color: #407bc6;}
.hcatebox-one dd {padding-left: 10px;}
.hcatebox-one dd a {float: left; font-size: 14px; height: 28px; text-align: center; width: 55px;}
/* quick */
.quick {display: block; height: 50px;}
.quick a {background: #E74C3C; color: #fff; display: block; float: left; font: bold 18px/50px "Microsoft YaHei"; height: 50px; margin: 1px; text-align: center; text-decoration: none; width: 123px;}
.quick a:hover {background: #4b8ad6;}
/* newsbox newsbox-list*/
.newsbox {background: #fff; border: solid 1px transparent;box-shadow: 0 1px 2px rgba(0,0,0,0.05);}
.newsbox-title {border-bottom: solid 1px #eaeaea; display: block; font: bold 14px normal; height: 39px; line-height: 39px; padding-left: 10px;}
.newsbox-list {padding: 5px; padding-left: 10px;padding-bottom: 11px;}
.newsbox-list li {background: url(arrow.gif) no-repeat left; font-size: 13px; overflow: hidden; padding: 5px; padding-left: 10px; white-space: nowrap; width: 210px;}
/* newbox */
.newbox {background: #fff; border: solid 1px transparent;box-shadow: 0 1px 2px rgba(0,0,0,0.05);padding: 10px;}
.newbox-title {border-bottom: solid 1px #eaeaea; display: block; font: bold 14px normal; height: 39px; line-height: 39px; padding-left: 10px;}
.newbox-list {padding: 10px 5px 0 5px;}
.newbox-list li {background: url(arrow.gif) no-repeat left; font-size: 13px; overflow: hidden; padding: 5px; padding-left: 10px; white-space: nowrap; width: 210px;}
.newbox-list li span {color: #ccc; float: right; font-size: 10px;}
/* exbox */
.exbox {background: #fff; border: solid 1px transparent;box-shadow: 0 1px 2px rgba(0,0,0,0.05);}
.exbox-title {border-bottom: solid 1px #eaeaea; display: block; font: bold 14px normal; height: 39px; line-height: 39px; padding-left: 10px;}
.exbox-body {padding: 10px;}
.exbox-list {padding-left: 10px;}
.exbox-list li {background: url(arrow.gif) no-repeat left; font-size: 13px; overflow: hidden; padding: 4px; padding-left: 10px; white-space: nowrap; width: 210px;}
/* inbox */
.inbox {background: #fff; border: solid 1px transparent;box-shadow: 0 1px 2px rgba(0,0,0,0.05);}
.inbox-title {border-bottom: solid 1px #eaeaea; display: block; font: bold 14px normal; height: 35px; line-height: 35px; padding-left: 10px;}
.inbox-list {padding: 10px;}
.inbox-list li {float: left; font-size: 13px; margin-right: 15px; padding-bottom: 5px;}
/* linkbox */
.linkbox {background: #fff; border: solid 1px transparent;box-shadow: 0 1px 2px rgba(0,0,0,0.05);}
.linkbox-title {border-bottom: solid 1px #eaeaea; display: block; font: bold 14px normal; height: 35px; line-height: 35px; padding-left: 10px;}
.linkbox-list {padding: 10px;}
.linkbox-list li {float: left; font-size: 13px; margin-right: 15px; padding-bottom: 5px;}
/* mainbox */
.crumb {font-size: 13px; padding-bottom: 10px;}
.crumb a {margin: 0 3px;}
.mainbox {}
.mainbox-left {float: left; width: 740px;}
.mainbox-right {float: right; width: 250px;}
/* scatbox */
.scatbox {background: #fff; border: solid 1px transparent;box-shadow: 0 1px 2px rgba(0,0,0,0.05);}
.scatbox-title {border-bottom: solid 1px #eaeaea; display: block; font: bold 14px normal; height: 35px; line-height: 35px; padding-left: 10px;}
.scatbox-list {padding: 10px;}
.scatbox-list li {float: left; font-size: 13px; margin-right: 15px; padding-bottom: 5px;}
.scatbox-list li em {color: #999; font-size: 10px;}
/* listbox */
.listbox {background: #fff; border: solid 1px transparent;box-shadow: 0 1px 2px rgba(0,0,0,0.05);}
.listbox-title {border-bottom: solid 1px #eaeaea; display: block; font: bold 14px normal; height: 39px; line-height: 39px; padding-left: 10px;}
.listbox-body {}
.sitelist {padding:0px 10px;}
.sitelist li {border-bottom: dotted 1px #e8e8e8; display: block; height: 100px; padding: 15px 5px; position: relative;}
.sitelist li .thumb {float: left; position: absolute; border: 1px solid #D8D8D8;}
.sitelist li .info {margin-left: 135px; position: absolute; width: 580px;}
.sitelist li .info h3 {display: block; font-size: 14px; height: 15px; line-height: 13px; position: absolute; top: 5px;}
.sitelist li .info h3 a {color: #005F93;}
.sitelistCAO img {border:1px solid #D8D8D8;width:120px; height:95px;padding:2px;}
.sitelist li .info h3 img {margin-left: 2px;}
.sitelist li .info p {display: block; font-size: 13px; height: 45px; line-height: 25px; overflow: hidden; position: absolute; top: 25px;width:550px}
.sitelist li .info cite {color: #080; font-size: 12px; padding: 0 10px 3px 0; position: absolute; top: 76px;}
.gray-bg {background: #f9f9f9;}
a.cate {color: #77c;}
a.addfav {color: #77c; padding-left: 10px;}
a.visit {background: url(link.png) no-repeat right; color: #E74C3C!important; font-size: 13px; padding-right: 16px;background-size: 14px;}
a.visit:hover {text-decoration: none;}
/* siteinfo */
.siteinfo {background: #fff; border: solid 1px transparent;box-shadow: 0 1px 2px rgba(0,0,0,0.05); border-top: solid 2px #E74C3C!important; padding: 10px;position: relative;}
.wtitle {font-size: 26px; padding-bottom: 15px; padding-top: 8px;}
.wtitle a {color: #005F93;}
.wtitle font {font: normal 12px normal;}
.wdata {background: #e9eaec; font: 12px/20px Arial; height: 45px; list-style: none; padding: 8px 0;}
.wdata li {color: #005F93; display: block; float: left; text-align: center; width: 78px;}
.wdata li em {color: #666666; display: block; padding-top: 3px;}
.line {border-right: dotted 1px #BDC3C7;}
.params {padding-top: 15px; position: relative;}
.wthumb {background: #fff; border: solid 1px #dadada; float: left; padding: 1px; width: 140px;}
.siteitem {font-size: 13px; margin-left: 160px;}
.siteitem li {padding-bottom: 8px;}
.siteitem li a {color: #005F93;}
.intro{margin-top:15px;padding:10px;background-color:#e9eaec;font-size:14px}
.siteHonour ul li{float:left;padding:6px 3px;background-color:#E74C3C;color:#fff;margin:0 3px;position:relative}
.siteHonour ul li:after{position:absolute;bottom:-8px;right:0;content:close-quote;border-right:0 solid transparent;border-top:8px solid #E74C3C;border-left:15px solid transparent}
.siteHonour ul li:before{position:absolute;bottom:-8px;left:0;content:close-quote;border-right:15px solid transparent;border-top:8px solid #E74C3C;border-left:0 solid transparent}
.siteHonour ul li.kuaishen:before,.siteHonour ul li.kuaishen:after{border-top: 8px solid #EE9A19!important;}
.siteHonour ul li.tuijian:before,.siteHonour ul li.tuijian:after{border-top: 8px solid #1ABC9C!important;}
.siteHonour ul li.kuaishen{background-color: #EE9A19;}
.siteHonour ul li.tuijian{background-color: #1ABC9C;}
.siteHonour ul li.lanmu{background-color: #E74C3C;}
.siteHonour{position:absolute;top:0;right:0;padding-right:20px}
ul#tag {
    padding: 10px 5px 0 5px;
}
ul#tag li {
    font-size: 13px;
    overflow: hidden;
    display: inline-block;
    line-height: 30px;
}
#tag li a {
    padding: 5px;
    background: #E74C3C;
    margin: 0 5px;
    color: #fff;
    text-decoration: initial;
}
#tag li:nth-child(2n) a {
    background: #005F93;
}
#tag li a:hover {
    background: #F79186;
}
#tag li:nth-child(2n) a:hover {
    background: #6799B4;
}
/* relsite */
.relsite {background: #fff; border: solid 1px transparent;box-shadow: 0 1px 2px rgba(0,0,0,0.05); padding: 10px;}
.relsite-title {border-bottom: solid 1px #eaeaea; display: block; font: bold 14px normal; height: 39px; line-height: 39px; padding-left: 10px;}
.relsite-list,.relsite-list-Alexa {padding-top: 5px;}
.relsite-list li,.relsite-list-Alexa li {float: left; font-size: 12px; margin: 1px; padding: 3px 3px 2px 3px; text-align: center;}
.relsite-list li img{border:1px solid #D8D8D8;padding:2px;width:129px; height:100px; overflow:hidden;}
.relsite-list-Alexa li img {border:1px solid #D8D8D8;padding:2px;width:345px; height:190px; overflow:hidden;}
.relsite-list li p {display: block; font-weight: normal; height: 18px; overflow: hidden; white-space: nowrap; width: 130px;}
.relsite-list li a {color: #07c; text-decoration: none;}
.relsite-list li a:hover {color: #f30; text-decoration: underline;}
.showpage a:hover{text-decoration: none;border: 1px solid #E74C3C;}
/* artlist */
.artlist {padding: 10px;}
.artlist li {padding: 8px;}
.artlist li h3 {font-size: 14px;}
.artlist li h3 a {color: #4784cd;}
.artlist li p {line-height: 25px;}
.artlist li cite {background: #f9f9f9; border-top: solid 1px #e8e8e8; border-bottom: solid 1px #e8e8e8; color: #999; display: block; margin-top: 8px; padding: 3px;}
/* artinfo */
.artinfo {background: #fff; border: solid 1px transparent;box-shadow: 0 1px 2px rgba(0,0,0,0.05); border-top: solid 2px #E74C3C; padding: 10px;}
.artinfo-title {color: #f60; font-size: 26px; padding: 15px 0; text-align: center;}
.artinfo-attr {border-top: solid 1px #e8e8e8; padding: 5px; text-align: center;}
.artinfo-cao {border-top: solid 1px #e8e8e8; border-bottom: solid 1px #e8e8e8;padding: 5px; text-align: right;}
.artinfo-cao a {color: #980c20; font-size: 13px;}
.artinfo-content {font-size: 16px; line-height: 35px; padding: 10px;}
.artinfo-prev {}
.artinfo-prev li {background: url(arrow.gif) no-repeat left; font-size: 13px; padding: 5px; padding-left: 10px;}
/* weblink */
.weblink {}
.weblink th {background: #f9f9f9; border-bottom: solid 1px #eaeaea; height: 30px;}
.weblink td {border-bottom: dashed 1px #ccc; height: 35px;}
/* linkinfo */
.linkinfo {background: #fff; border: solid 1px transparent;box-shadow: 0 1px 2px rgba(0,0,0,0.05); border-top: solid 2px #E74C3C; padding: 10px;}
.linkname {border-bottom: dashed 1px #e8e8e8; color: #f60; font-size: 24px; padding-bottom: 15px; padding-top: 5px;}
.linkitem {font-size: 13px; margin-left: 160px;}
.linkitem li {padding-bottom: 10px;}
.otherinfo {}
.otherinfo-title {background: #f9f9f9; color: #333; font-size: 14px; font-weight: bold; padding: 5px;}
.otherinfo-body {font-size: 14px; line-height: 35px; padding: 10px;}
/* showpage */
.showpage {display: block; font-size: 13px; text-align: left; padding: 10px;}
.total_page, .jump_page {background: #E74C3C; border: solid 1px #E74C3C; color: #fff; display: block; float: left; margin-right: 5px; padding: 2px 8px;}
.first_page, .last_page, .prev_page, .next_page, .pages {background: #fff; border: 1px solid #e8e8e8; color: #333; display: block; float: left; margin-right: 5px; padding: 2px 8px; text-decoration: none;}
.current {background: #E74C3C; color: #fff; display: block; float: left; margin-right: 5px; padding: 3px 8px;}
/* artbox */
.artbox {background: #fff; border: solid 1px transparent;box-shadow: 0 1px 2px rgba(0,0,0,0.05);}
.artbox-title {border-bottom: solid 1px #eaeaea; display: block; font: bold 14px normal; height: 39px; line-height: 39px; padding-left: 10px;}
.artbox-list {padding-left: 10px;}
.artbox-list li {background: url(arrow.gif) no-repeat left; font-size: 13px; overflow: hidden; padding: 5px; padding-left: 10px; white-space: nowrap; width: 210px;}
.artbox-list li span {color: #ccc; float: right; font-size: 10px;}
/* webbox */
.webbox {background: #fff; border: solid 1px transparent;box-shadow: 0 1px 2px rgba(0,0,0,0.05);}
.webbox-title {border-bottom: solid 1px #eaeaea; display: block; font: bold 14px normal; height: 39px; line-height: 39px; padding-left: 10px;}
.webbox-list {}
.webbox-list li {padding: 8px;}
.webbox-list li h3 {font: bold 14px Arial;}
.webbox-list li h3 a {color: #4784cd;}
.webbox-list li p {height: 40px; line-height: 20px; margin: 3px 0; overflow: hidden;}
.webbox-list li cite {}
/* timelink */
.timelink {float: right;}
.timelink a {color: #E74C3C; font-size: 12px; font-weight: normal; margin: 0 5px;}
a.timelink_bg {background: #E74C3C; color: #fff; padding: 3px;}
/* arcbox */
.arcbox {background: #fff; border: solid 1px transparent;box-shadow: 0 1px 2px rgba(0,0,0,0.05); padding: 10px;}
.arcbox-list {font-size: 13px;}
.arcbox-list li {padding: 5px 0;}
.arcbox-list li strong {background: #E74C3C; color: #fff; padding: 3px;}
.arcbox-list li a {color: #555; padding: 0 10px;}
.newbox .arcbox-list li a{padding: 0 3px!important}
/* allcate */
.allcate {background: #fff; border: solid 1px transparent;box-shadow: 0 1px 2px rgba(0,0,0,0.05); padding: 10px;}
.catebox {margin: 5px;}
.catebox h3 {font-size: 14px;}
.catebox h3 a {color: #4784cd; text-decoration: none;}
.catebox h3 em {color: #888; font: 10px normal;}
.catebox ul {display: block; margin-top: 2px;}
.catebox ul li {float: left; height: 23px; line-height: 23px; margin: 1px; text-indent: 30px; width: 125px;}
.catebox ul li em {color: #888; font: 10px Arial;}
.catebox ul li a {color: #444; font-size: 13px; text-decoration: none;}
.catebox ul li a:hover {color: #E74C3C; text-decoration: underline;}
.bestbox ul li a,.indexcatebox ul li a,.indexcatebox-sj ul li a{font-size:14px}
.indexcatebox ul,.indexcatebox-sj ul{display: block; margin-top: 10px;}
.indexcatebox ul li{float:left;height:28px;line-height:28px;text-align:center;width:10%;display:block;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}
.indexcatebox-sj ul li{float:left;height:28px;line-height:28px;text-align:center;width:20%}
.yq ul li{width:10%!important}
.indexcatebox-sj ul li a{font-size:14px;display:block;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}
/* top */
.topsite:nth-of-type(1),.topsite:nth-of-type(3){margin-right:10px}
.topsite {background: #fff; border: solid 1px transparent;box-shadow: 0 1px 2px rgba(0,0,0,0.05); float: left; padding: 10px; width: 343px;margin-bottom:10px}
.topsite-title {border-bottom: solid 1px #eaeaea; display: block; font: bold 14px normal; height: 39px; line-height: 39px; padding-left: 10px;}
.topsite-list {padding: 10px;}
.topsite-list li {font-size: 13px; overflow: hidden; padding: 3px 0; white-space: nowrap; width: 310px;}
.topsite-list li span {color: #f30; font-size: 12px; padding-right: 10px;}
/* feedback */
.subbox {background: #fff; border: solid 1px transparent;box-shadow: 0 1px 2px rgba(0,0,0,0.05);}
.subbox-title {background: #f9f9f9; border-bottom: solid 1px #e8e8e8; font: bold 14px "Microsoft YaHei"; padding: 8px;}
.subbox-form {margin: 30px 0 10px 250px;}
.stepbox {margin: 0 auto; padding: 10px;}
.stepbox p {padding: 15px;}
.textbox {background: #ffe; border: dashed 1px #f30; color: #080; line-height: 23px; margin-bottom: 10px; padding: 10px;}
.formbox {}
.add-left li {display: block; padding: 8px; overflow: hidden;}
.formbox li strong {display: block; float: left; font-weight: normal; height: 25px; line-height: 32px; text-align: right; width: 100px;}
.formbox li p {float: left; padding-top: 0;}
.subbox-form #myfrom input.fbtn {color: #fff;background: #27AE60;border: 2px solid #27AE60;width: 100px;}
.subbox-form #myfrom input#ip:focus {border: 2px solid #27AE60;}
.fipt {background: #fff url(ipt.png); border: solid 1px #e8e8e8; font-size: 12px; padding: 6px;}
.fbtn {background: #55a51c; border: 0; color: #fff; cursor: pointer; height: 30px; width: 60px;}
/* resbox */
.resbox {background: #f8fef4; border: dashed 1px #690; font-size: 13px; line-height: 25px; margin: 10px; padding: 10px 15px;}
/* urlbox */
.urlbox {margin: 10px;}
.urlbox-title {float: left; font-weight: bold; padding-top: 3px; padding-right: 30px;}
.urlbox-list {}
.urlbox-list li {float: left; padding: 5px 0; padding-right: 30px;}
/* footer */
.footer {font-size: 13px; text-align: center;}
.footer-nav {border-bottom: dotted 1px #d8d8d8; color: #ccc; padding: 8px 0;}
.footer-nav a {margin: 0 8px;}
.footer-copy {color: #999; line-height: 23px; padding-top: 5px;}

/*author 8865987*/
.bw-homebox-hot{overflow:hidden;}
.bw-homebox-hot .fl{ float:left; width:250px;}
.bw-homebox-hot li{ width:220px; overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
.bw-homebox-hot .fl .cate{color:#E74C3C; padding-right:6px;}
.bw-homebox-hot .newshot{width:480px;  background:#fff; border:1px solid #D8D8D8; float:right; overflow:hidden;}
.bw-homebox-hot .newshot dl{width:100%; clear:both; border-bottom:1px solid #EAEAEA; overflow:hidden;}
.bw-homebox-hot .newshot dt{width:100%; padding-top:10px; line-height:1.6em; overflow:hidden;text-overflow:ellipsis;white-space:nowrap; text-align:center;}
.bw-homebox-hot .newshot dt a{color:#E74C3C; font-size:18px; font-weight:bold;}
.bw-homebox-hot .newshot dd{ padding:10px; line-height:2em; height:51px; overflow:hidden; text-indent:28px;}
.bw-homebox-hot .newshot ul{overflow:hidden;}
.bw-homebox-hot .newshot li{ float:left; width:220px; padding:5px 10px; overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
.bw-homebox-hot .newshot li .cate{color:#E74C3C; padding-right:6px;}

.bw-bestbox-list{}
.bw-bestbox-list li:nth-of-type(5){margin:10px 0px 0px 0px;}
.bw-bestbox-list li{width:137.2px; height:125px; margin:10px 3px 0px 0px; _margin:0 2px; overflow:hidden; float:left}
.bw-bestbox-list li img{border:1px solid #D8D8D8;padding:2px;width:129px; height:100px; overflow:hidden;}
.bw-bestbox-list li span{display:block; height:30px; text-align:center; overflow:hidden;line-height: 22px;}

.bw-homebox-qiuky{overflow:hidden;}
.bw-homebox-qiuky .newsbox{ float:right; width:362px; height:370px;}
.bw-homebox-qiuky .fl{float:left;}
.bw-homebox-qiuky .newsbox .cate{color:#E74C3C; padding-right:6px;}
.bw-homebox-qiuky .newsbox span{float:right; color:#ccc; font-size:12px;}
.bw-homebox-qiuky .newsbox-list li{width:340px; padding:6px 0px 6px 0px; background:none;}

.bw-quick-list{overflow:hidden;}
.bw-quick-list li{padding:5px 10px;}
.bw-quick-list dl{overflow:hidden; clear:both;}
.bw-quick-list dt{width:136px; height:100px; overflow:hidden; float:left;}
.bw-quick-list dt img{border:1px solid #D8D8D8;padding:2px;width:130px; height:93px; overflow:hidden;}
.bw-quick-list dd{margin-left:140px; overflow:hidden;}
.bw-quick-list dd a{color:#E74C3C; font-size:16px;}
.bw-quick-list dd a.url{color:#276420; font-size:12px;}

.vcatebox {background: #fff; overflow:hidden; border: solid 1px transparent;box-shadow: 0 1px 2px rgba(0,0,0,0.05);}
.vcatebox-one { clear:both;}
.vcatebox-one dt {font: bold 14px normal; padding: 10px;}
.vcatebox-one dt a {color: #407bc6;}
.vcatebox-one dd {padding-left: 10px;}
.vcatebox-one dd a {float: left; font-size: 14px; height: 28px; text-align: center; width: 55px;}

.website-count{padding:10px; overflow:hidden; line-height:2em;}
.website-count span{color:red;}

/* addurl */
div.addurl-left{float:left;width:390px}
div.addurl-right{float:right;width:575px}
div.addurl-left,div.addurl-right{font-size:14px}
div.addurl-left>ul{padding:10px;background-color:#e9eaec;list-style:decimal inside}
div.addurl-left>ul li{margin-bottom:3px}
div.daili a,div.youke a{display:block;color:#fff;line-height:30px;text-align:center}
div.daili a{background-color:#005F93}
div.daili a:hover{background-color:#6799b4}
div.youke a{background-color:#E74C3C}
div.youke a:hover{background-color:#F79186}
div.addLinks button{border:2px solid #E9EAEC;background:#E9EAEC;width:100px}
div.addLinks input{border:2px solid #E9EAEC;width:286px}
ul.daohang,ul.dir,ul.spider{padding:10px 5px 0 5px;overflow:hidden;text-align:center}
ul.spider li{width:33.33%;display:inline-block;float:left}
ul.dir li{width:20%;display:inline-block;float:left}
#myfrom .linobox{display: none;}
#myfrom{font-size: 14px}
#key{width: 30px!important}
#myfrom input,#myfrom select,#myfrom textarea{border:2px solid #E9EAEC}
#myfrom input[type=button]{background:#83b3cd;border:2px solid #83b3cd;color:#fff;padding:0 5px;margin-left:-2px;cursor:pointer}
#myfrom input:focus,#myfrom select:focus,#myfrom textarea:focus{border:2px solid #83b3cd;outline:0;-webkit-box-shadow:none;box-shadow:none}
#myfrom select{width:81px!important;height: 25px;}
#myfrom input[type=text],#myfrom textarea{width:260px}
#myfrom input[type=submit]{background:#E9EAEC}
#myfrom input[type=submit],#myfrom input[type=reset]{cursor:pointer;color:#fff;padding:0 10px}
#myfrom .tijiao[type=submit],#myfrom input[type=reset]{border:2px solid #E74C3C;background:#E74C3C}
#myfrom .tijiao:hover,#myfrom input[type=reset]:hover{border:2px solid #F79186;background:#F79186}
#num1,#num2{background:#27AE60;color:#fff;display:inline-block;padding:0 10px;border:2px solid #27AE60}
.hint{height:40px}
.hint a{display:inline-block;background:#27AE60;color:#fff;padding:5px 10px}
.add-left{width:470px;float:left}
.add-right{width:473px;float:right}
